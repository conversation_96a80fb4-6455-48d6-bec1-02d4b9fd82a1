const Region = require("../models/Region");
const RegionGroup = require("../models/RegionGroup");
const awsKinesis = require("../modules/awsKinesis");
const pLimit = require("p-limit");

const limit = pLimit(1);

class VesselService {
    constructor() {
        this.vesselsInfo = { data: [], lastCheck: new Date(null) };
    }

    async resetCache() {
        this.vesselsInfo = { data: [], lastCheck: new Date(null) };
    }

    async fetchAll({ regions } = {}) {
        const lastCheck = this.vesselsInfo.lastCheck;

        if (Date.now() - lastCheck.getTime() < 300000) {
            // return existing data if last check was less than 5 minutes ago
            console.log("returning existing");
            return this.vesselsInfo.data.filter((vessel) => (regions === undefined ? true : regions.includes(vessel.region)));
        }

        console.log("[VesselService.fetchAll] Fetching updated list");

        const awsRegions = (await Region.find()).filter((r) => r.is_live);

        const allVessels = await Promise.all(
            awsRegions.map(async (region) => {
                try {
                    return await awsKinesis.listStreams({ region: region.value });
                } catch {
                    console.error(`[VesselService.fetchAll] [FATAL] Error fetching streams for region ${region.value}`);
                    return [];
                }
            }),
        );

        const flattenedVessels = allVessels.flat();

        const regionGroups = await RegionGroup.find();

        const vesselsInfo = flattenedVessels.map((v) => {
            const regionGroup = regionGroups.find((rg) => rg.unit_ids.includes(v.StreamName));

            return {
                unit_id: v.StreamName,
                name: v.Tags.Name,
                thumbnail: v.Tags.Thumbnail,
                region: v.Region,
                is_live: v.IsLive,
                timezone: regionGroup?.timezone || null,
                region_group: regionGroup?._id || null,
            };
        });

        this.vesselsInfo = { data: vesselsInfo, lastCheck: new Date() };

        return vesselsInfo.filter((vessel) => (regions === undefined ? true : regions.includes(vessel.region)));
    }

    async fetchSingle({ unitId }) {
        let vesselInfo = this.vesselsInfo.data.find((v) => v.unit_id === unitId);

        if (!vesselInfo) {
            const vessels = await limit(() => this.fetchAll());
            vesselInfo = vessels.find((v) => v.unit_id === unitId);
            if (!vesselInfo) {
                console.error("[VesselService.fetchSingle] [FATAL] couldn't find vessel", unitId, vessels);
            }
        }

        if (!vesselInfo) return null;

        return vesselInfo;
    }
}

const vesselService = new VesselService();

module.exports = vesselService;
