import { useMemo } from "react";
import axiosInstance from "../../../axios";
import { alpha, Button, Grid, MenuItem, Modal, TextField, useTheme } from "@mui/material";
import { Field, Form, Formik } from "formik";
import ModalContainer from "../../../components/ModalContainer";
import { inviteUserSchema } from "../../../validation-schemas";
import { useUser } from "../../../hooks/UserHook";
import MultiSelect from "../../../components/MultiSelect";
import { permissions } from "../../../utils";

const InviteUserModal = ({ showAddUser, setShowAddUser, onSuccess, roles, vessels, organizations, regionGroups }) => {
    const { user } = useUser();
    const theme = useTheme();

    const canManageOrganizations = user?.hasPermissions([permissions.manageOrganizations]);

    const vesselsByRegionGroup = useMemo(() => {
        return vessels
            .filter((v) => v.region_group)
            .map((v) => ({ ...v, region_group_object: regionGroups.find((rg) => rg._id === v.region_group) }));
    }, [vessels, regionGroups]);

    const handleClose = () => {
        setShowAddUser(false);
    };

    const onAdd = (values, { setSubmitting, resetForm }) => {
        setSubmitting(true);
        const data = {};
        Object.keys(values).forEach((key) => {
            const value = values[key];
            if (value !== "") data[key] = value;
        });
        data.allowed_units = data.allowed_units.map((v) => ({ unit_id: v, region: vessels.find((o) => o.unit_id === v).region }));
        axiosInstance
            .post("/users/invite", data, { meta: { showSnackbar: true } })
            .then(() => {
                onSuccess && onSuccess();
                resetForm();
            })
            .catch(console.error)
            .finally(() => setSubmitting(false));
    };

    const checkRoleUpdation = (role, values) => {
        if (user.role.hierarchy_number >= role.hierarchy_number) return true;
        if (
            organizations.find((org) => org._id == values.organization_id)?.is_miscellaneous &&
            !role.denied_permissions.includes(permissions.manageUsers)
        )
            return true;
        return false;
    };

    const getInitialOrgId = () => {
        if (canManageOrganizations) {
            return "";
        }

        if (user && user.organization) {
            return user.organization._id;
        }

        return "";
    };

    return (
        <Modal open={showAddUser} onClose={() => {}}>
            <ModalContainer title={"Invite New User"} onClose={handleClose}>
                <Formik
                    initialValues={{
                        email: "",
                        role_id: "",
                        organization_id: getInitialOrgId(),
                        allowed_units: [],
                    }}
                    validationSchema={inviteUserSchema}
                    onSubmit={onAdd}
                >
                    {({ errors, touched, isSubmitting, values, setValues, setFieldTouched, setErrors }) => (
                        <Form>
                            <Grid
                                container
                                flexDirection={"column"}
                                gap={2}
                                width={{ xs: 300, lg: 500 }}
                                sx={{
                                    "& .MuiFormHelperText-root": {
                                        color: alpha("#FFFFFF", 0.5) + " !important",
                                    },
                                    "& .MuiFormLabel-root": {
                                        color: "#FFFFFF !important",
                                    },
                                }}
                            >
                                <Grid>
                                    <Field
                                        as={TextField}
                                        required
                                        select
                                        name={"organization_id"}
                                        value={values.organization_id}
                                        label="Organization"
                                        variant="filled"
                                        size="small"
                                        error={touched.organization_id && Boolean(errors.organization_id)}
                                        helperText={touched.organization_id && errors.organization_id}
                                        fullWidth
                                        disabled={!canManageOrganizations}
                                        sx={{
                                            "& .Mui-disabled": {
                                                color: "white !important",
                                                cursor: "not-allowed !important",
                                                WebkitTextFillColor: "rgba(255, 255, 255, 0.7) !important",
                                            },
                                        }}
                                    >
                                        {organizations.map((org) => (
                                            <MenuItem
                                                key={org._id}
                                                value={org._id}
                                                disabled={
                                                    org.is_miscellaneous &&
                                                    values.role_id !== "" &&
                                                    !roles
                                                        .find((role) => role.role_id === values.role_id)
                                                        ?.denied_permissions.includes(permissions.manageUsers)
                                                }
                                            >
                                                {org.name}
                                            </MenuItem>
                                        ))}
                                    </Field>
                                </Grid>
                                <Grid>
                                    <Field
                                        as={TextField}
                                        required
                                        select
                                        name={"role_id"}
                                        value={values.role_id}
                                        label="Role"
                                        variant="filled"
                                        size="small"
                                        disabled={!values.organization_id}
                                        error={touched.role_id && Boolean(errors.role_id)}
                                        helperText={!values.organization_id ? "Please select organization first" : touched.role_id && errors.role_id}
                                        fullWidth
                                    >
                                        {roles
                                            .sort((a, b) => a.hierarchy_number - b.hierarchy_number)
                                            .map((role) => (
                                                <MenuItem key={role.role_id} value={role.role_id} disabled={checkRoleUpdation(role, values)}>
                                                    {role.role_name}
                                                </MenuItem>
                                            ))}
                                    </Field>
                                </Grid>
                                <Grid>
                                    <Field
                                        as={TextField}
                                        required={!values.username}
                                        name={"email"}
                                        value={values.email}
                                        type="email"
                                        label="Email"
                                        variant="filled"
                                        error={touched.email && Boolean(errors.email)}
                                        helperText={touched.email && errors.email}
                                        size="small"
                                        fullWidth
                                        sx={{
                                            backgroundColor: "transparent",
                                            "& .MuiFilledInput-root": {
                                                backgroundColor: "transparent",
                                                borderBottom: "none",
                                                border: (theme) => `1px solid ${theme.palette.custom.borderColor}`,
                                                borderRadius: "8px",
                                            },
                                            "& .MuiInputBase-input": {
                                                padding: 1.5,
                                            },
                                            "& .MuiFilledInput-root::after,.MuiFilledInput-root::before": {
                                                border: "none !important",
                                            },
                                            "& .MuiInputBase-root": {
                                                backgroundColor: "transparent",
                                                borderBottom: "none",
                                                border: (theme) => `1px solid ${theme.palette.custom.borderColor}`,
                                                borderRadius: "8px",
                                            },
                                            "& .MuiInputLabel-shrink": {
                                                display: "none",
                                            },
                                        }}
                                    />
                                </Grid>
                                <Grid>
                                    <MultiSelect
                                        loading={vessels.length === 0}
                                        options={vesselsByRegionGroup}
                                        value={values.allowed_units}
                                        multiple
                                        groupBy={(o) => o.region_group_object?.name}
                                        disableCloseOnSelect
                                        label={values.allowed_units.length === 0 ? "Select Units *" : `${values.allowed_units.length} selected *`}
                                        getOptionLabel={(o) => o.name || o.unit_id}
                                        isOptionEqualToValue={(o, v) => v.includes(o.unit_id)}
                                        renderTags={() => null}
                                        onChange={(e, newValue) => {
                                            setValues((v) => ({ ...v, allowed_units: newValue.map((o) => (typeof o === "string" ? o : o.unit_id)) }));
                                        }}
                                        backgroundColor={theme.palette.custom.darkBlue}
                                        sx={{
                                            ".MuiInputBase-input": {
                                                height: 24,
                                            },
                                        }}
                                        borderRadius={8}
                                        InputLabelStyle={{ fontSize: 16, opacity: 0.5 }}
                                        TextFieldProps={{
                                            error: touched.allowed_units && Boolean(errors.allowed_units),
                                            helperText: touched.allowed_units && errors.allowed_units,
                                            onBlur: () => {
                                                if (!touched.allowed_units) {
                                                    setFieldTouched("allowed_units", true, true);
                                                }
                                                if (values.allowed_units.length === 0) {
                                                    setErrors({ ...errors, allowed_units: "At least one unit is required" });
                                                }
                                            },
                                        }}
                                    />
                                </Grid>

                                <Grid justifyContent={"center"} display={"flex"}>
                                    <Button
                                        type="submit"
                                        variant="contained"
                                        disabled={
                                            !values.email ||
                                            !values.role_id ||
                                            !values.organization_id ||
                                            values.allowed_units.length === 0 ||
                                            isSubmitting
                                        }
                                        sx={{
                                            backgroundColor: (theme) => theme.palette.custom.mainBlue,
                                            color: "#FFFFFF",
                                            padding: "10px 24px",
                                        }}
                                    >
                                        Submit
                                    </Button>
                                </Grid>
                            </Grid>
                        </Form>
                    )}
                </Formik>
            </ModalContainer>
        </Modal>
    );
};

export default InviteUserModal;
