const { default: rateLimit } = require("express-rate-limit");
const express = require("express");
const router = express.Router();
const InAppNotification = require("../models/InAppNotification");
const isAuthenticated = require("../middlewares/auth");
const { validateError } = require("../utils/functions");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const { validateData } = require("../middlewares/validator");
const { query, param, body } = require("express-validator");
const db = require("../modules/db");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_IN_APP_NOTIFICATIONS),
    isAuthenticated,
    validateData.bind(this, [
        query("is_read")
            .optional()
            .isIn(["0", "1"])
            .withMessage((value, { path }) => `Invalid value ${value} provided for ${path}`)
            .toBoolean(),
    ]),
    async (req, res) => {
        try {
            const page = parseInt(req.query.page) || 1;
            const pageSize = Math.min(parseInt(req.query.page_size) || 50, 50);
            const skip = (page - 1) * pageSize;

            const totalRecords = await InAppNotification.countDocuments({ receiver: req.user._id });
            const inAppNotifications = await InAppNotification.find({ receiver: req.user._id }).sort({ created_at: -1 }).limit(pageSize).skip(skip);

            const artifactIds = inAppNotifications.map((notification) => notification.artifact_id);
            const artifacts = await db.qmai
                .collection("analysis_results")
                .find({ _id: { $in: artifactIds } })
                .toArray();

            const notificationsWithArtifacts = inAppNotifications
                .filter((notification) => artifacts.some((artifact) => artifact._id.equals(notification.artifact_id)))
                .map((notification) => {
                    const artifact = artifacts.find((artifact) => artifact._id.equals(notification.artifact_id));
                    return { ...notification.toObject(), artifact_details: artifact };
                });

            const totalPages = Math.ceil(totalRecords / pageSize);
            const nextPage = page < totalPages ? page + 1 : null;
            const previousPage = page > 1 ? page - 1 : null;

            res.json({
                data: notificationsWithArtifacts,
                pagination: {
                    totalRecords,
                    totalPages,
                    currentPage: page,
                    nextPage,
                    previousPage,
                },
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/markRead/:id",
    assignEndpointId.bind(this, endpointIds.MARK_AS_READ_IN_APP_NOTIFICATIONS),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    async (req, res) => {
        try {
            const inAppNotification = await InAppNotification.findByIdAndUpdate(req.params.id, { is_read: true }, { new: true });
            res.json({
                data: inAppNotification,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/bulkMarkRead",
    assignEndpointId.bind(this, endpointIds.MARK_AS_READ_IN_APP_NOTIFICATIONS),
    isAuthenticated,
    validateData.bind(this, [
        body("ids")
            .isArray()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    async (req, res) => {
        const { ids } = req.body;
        try {
            const inAppNotifications = await InAppNotification.updateMany({ _id: { $in: ids } }, { is_read: true });
            res.json({
                data: inAppNotifications,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
