import { alpha, createTheme, responsiveFontSizes, tablePaginationClasses } from "@mui/material";
import * as MuiColors from "@mui/material/colors";
import { defaultValues } from "./utils";

const colors = {
    grey: {
        dark: "#181818",
        main: "#1E293B",
        medium: "#BDBDBD",
        light: "#343B44",
        borderGrey: "#282C39",
    },
    red: {
        main: "#CC5500",
    },
    green: {
        main: "#00CC1F",
    },
    blue: {
        main: "#3873E4",
        light: "#7090B0",
        dark: "#020716",
        secondary: MuiColors.blue["800"],
    },
    white: {
        main: "#FFFFFF",
        light: "#545454",
    },
    contrast: {
        primary: "#FFFFFF",
    },
};

const fontFamily = `"Outfit", sans-serif`;

const baseTheme = createTheme();

const theme = responsiveFontSizes(
    createTheme({
        ...baseTheme,
        breakpoints: {
            values: {
                xs: 0,
                sm: 600,
                md: 900,
                lg: 1200,
                xl: 1536,
                "2xl": 1900,
            },
        },
        palette: {
            primary: {
                main: colors.grey.main,
                light: colors.grey.light,
            },
            custom: {
                live: colors.green.main,
                offline: colors.blue.light,
                replay: colors.blue.secondary,
                unfocused: colors.white.light,
                mediumGrey: colors.grey.medium,
                darkBlue: colors.blue.dark,
                mainBlue: colors.blue.main,
                borderColor: colors.grey.borderGrey,
                text: colors.white.main,
            },
            background: {
                paper: colors.grey.light,
                default: "#FFFFFF",
            },
        },
        typography: {
            fontFamily: fontFamily,
            fontWeightLight: 300,
            fontWeightRegular: 700,
            fontWeightMedium: 500,
        },
        components: {
            MuiCssBaseline: {
                styleOverrides: {
                    "::-webkit-scrollbar": {
                        width: "10px", // Set the scrollbar width
                        height: "10px", // Set the scrollbar height (for horizontal scrollbars),
                        paddingLeft: 0.5,
                    },
                    "::-webkit-scrollbar-track": {
                        backgroundColor: "transparent", // Background color of the scrollbar track
                    },
                    "::-webkit-scrollbar-thumb": {
                        backgroundColor: colors.grey.medium, // Color of the scrollbar thumb
                        borderRadius: "10px", // Rounded corners for the scrollbar thumb
                        border: "2px solid transparent",
                        backgroundClip: "padding-box", // Ensures the background is clipped within the padding
                    },
                },
            },
            MuiFilledInput: {
                styleOverrides: {
                    root: {
                        "&:before": {
                            borderBottomColor: colors.grey.main, // Color of the underline when not focused
                        },
                        "&:hover:not(.Mui-disabled):before": {
                            borderBottomColor: colors.grey.light, // Color of the underline on hover
                        },
                        "&:after": {
                            borderBottomColor: colors.contrast.primary, // Color of the underline when focused
                        },
                    },
                },
            },
            MuiTextField: {
                styleOverrides: {
                    root: {
                        "&:before": {
                            borderBottomColor: "blue", // Color of the underline when not focused
                        },
                        "&:after": {
                            borderBottomColor: "blue", // Color of the underline when not focused
                        },
                        "& .MuiInputLabel-root": {
                            "&.Mui-focused": {
                                color: colors.contrast.primary,
                                opacity: 0.5,
                            },
                        },
                        "& .MuiInputLabel-filled": {
                            color: colors.contrast.primary,
                            opacity: 0.5,
                        },
                        "& .MuiFilledInput-root": {
                            color: colors.contrast.primary,
                        },
                        "& .MuiOutlinedInput-root": {
                            color: colors.contrast.primary,
                        },
                        "& fieldset": {
                            border: "none", // Customize the border color here
                        },
                        "&.MuiFilledInput-root": {
                            backgroundColor: "transparent",
                            border: `1px solid ${colors.grey.borderGrey}`,
                            borderRadius: "8px",
                        },
                        "& .MuiFilledInput-input": {
                            padding: "14px 24px",
                        },
                        "& .MuiFilledInput-root::after,.MuiFilledInput-root::before": {
                            border: "none !important",
                        },
                        "& .MuiInputBase-root": {
                            backgroundColor: "transparent",
                            border: `1px solid ${colors.grey.borderGrey}`,
                            borderRadius: "8px",
                        },
                        "& .MuiInputLabel-shrink": {
                            display: "none",
                        },
                        "&.input-login": {
                            borderRadius: "10px",
                            height: "60px",
                            border: "1px solid" + colors.grey.medium,
                            backgroundColor: alpha(colors.blue.light, 0.08),
                            [baseTheme.breakpoints.up("sm")]: {
                                height: "80px",
                            },
                            "& input:-webkit-autofill": {
                                WebkitBoxShadow: `0 0 0 1000px ${alpha(colors.grey.main, 0.96)} inset`,
                                WebkitTextFillColor: "#FFFFFF",
                                borderRadius: 0,
                            },
                            "& .MuiInputBase-root": {
                                paddingLeft: "20px",
                                paddingRight: "20px",
                                height: "80px",
                                color: colors.contrast.primary,
                                fontWeight: 400,
                                lineHeight: "30px",
                                [baseTheme.breakpoints.up("sm")]: {
                                    fontSize: "24px",
                                },
                            },
                        },
                        "&.input-signup": {
                            borderRadius: "10px",
                            height: "60px",
                            border: "1px solid" + colors.grey.medium,
                            backgroundColor: alpha(colors.blue.light, 0.08),
                            [baseTheme.breakpoints.up("sm")]: {
                                height: "65px",
                            },
                            "& input:-webkit-autofill": {
                                WebkitBoxShadow: `0 0 0 1000px ${alpha(colors.grey.main, 0.96)} inset`,
                                WebkitTextFillColor: "#FFFFFF",
                                borderRadius: 0,
                                padding: 0,
                            },
                            "& .MuiInputBase-root": {
                                paddingLeft: "20px",
                                paddingRight: "20px",
                                height: "65px",
                                color: colors.contrast.primary,
                                fontWeight: 400,
                                lineHeight: "30px",
                                [baseTheme.breakpoints.up("sm")]: {
                                    fontSize: "24px",
                                },
                            },
                            "& .MuiInputBase-input.Mui-disabled": {
                                color: "white", // White text for disabled input fields
                                WebkitTextFillColor: "white",
                            },
                        },
                    },
                },
            },
            MuiButton: {
                defaultProps: {
                    disableRipple: true,
                },
                styleOverrides: {
                    root: {
                        borderRadius: "10px",
                        ":disabled": {
                            pointerEvents: "auto",
                            cursor: "not-allowed",
                        },
                    },
                    containedPrimary: {
                        textTransform: "none",
                        boxShadow: "none",
                        backgroundColor: colors.blue.main,
                        color: "#FFFFFF",
                        padding: "10px 24px",
                        ":hover": {
                            backgroundColor: colors.blue.main,
                        },
                        ":disabled": {
                            color: colors.contrast.primary,
                            backgroundColor: "#9A9CA2",
                        },
                        "&.btn-cancel": {
                            background: "#FFFFFF",
                            color: colors.grey.main,
                            ":hover": {
                                background: "#FFFFFF",
                                color: colors.grey.main,
                            },
                        },
                        "&.btn-login": {
                            fontSize: "24px",
                            lineHeight: "30px",
                            color: colors.grey.main,
                            fontWeight: "bold",
                            backgroundColor: "#FFFFFF",
                            ":hover": {
                                color: colors.contrast.primary,
                                backgroundColor: colors.grey.light,
                            },
                            height: "50px",
                            [baseTheme.breakpoints.up("sm")]: {
                                height: "80px",
                            },
                        },
                    },
                    outlinedPrimary: {
                        color: "#737791",
                        padding: "10px 24px",
                        borderColor: colors.grey.borderGrey,
                        ":disabled": {
                            opacity: 0.5,
                        },
                        ":hover": {
                            borderColor: colors.grey.borderGrey,
                        },
                    },
                },
            },
            MuiSelect: {
                styleOverrides: {
                    root: {
                        color: colors.contrast.primary,
                        borderColor: "red",
                        "& .MuiInputBase-root": {
                            color: colors.contrast.primary,
                            borderColor: "#FFFFFF",
                        },
                    },
                    icon: {
                        color: colors.contrast.primary, // Change the dropdown icon color here
                    },
                },
            },
            MuiOutlinedInput: {
                styleOverrides: {
                    root: {
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                            borderColor: colors.contrast.primary, // Border color when focused
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                            borderColor: colors.contrast.primary, // Border color on hover
                        },
                        "& .MuiOutlinedInput-notchedOutline": {
                            borderColor: colors.contrast.primary, // Default border color
                        },
                    },
                },
            },
            MuiAccordion: {
                styleOverrides: {
                    root: {
                        fontFamily: fontFamily,
                        borderRadius: 0,
                    },
                },
            },
            MuiAccordionSummary: {
                styleOverrides: {
                    root: {
                        fontSize: "15px",
                        lineHeight: "15px",
                        fontWeight: 700,
                        color: colors.contrast.primary,
                        "& .MuiSvgIcon-root": {
                            color: colors.contrast.primary,
                        },
                    },
                },
            },
            MuiMenuItem: {
                styleOverrides: {
                    root: {
                        color: colors.contrast.primary,
                    },
                },
            },
            MuiSkeleton: {
                styleOverrides: {
                    root: {
                        backgroundColor: MuiColors.grey["800"],
                    },
                },
                defaultProps: {
                    animation: "wave",
                },
            },
            MuiCircularProgress: {
                styleOverrides: {
                    root: {
                        color: "inherit",
                    },
                },
                defaultProps: {
                    size: 28, // Set a default size
                },
            },
            MuiSlider: {
                styleOverrides: {
                    root: {
                        color: colors.contrast.primary,
                        height: 8,
                    },
                    track: {
                        border: "none",
                    },
                    thumb: {
                        backgroundColor: "#FFFFFF",
                        height: 15,
                        width: 15,
                    },
                    markLabel: {
                        color: colors.grey.medium,
                        fontSize: "12px",
                    },
                },
            },
            MuiPickersDay: {
                styleOverrides: {
                    root: {
                        // Change the color of the day cells
                        color: colors.contrast.primary,
                        "&.Mui-selected": {
                            backgroundColor: colors.grey.dark, // Add background color
                            border: "1px solid black",
                        },
                        "&.Mui-disabled": {
                            color: `${colors.grey.light} !important`, // Change text color for disabled days
                        },
                    },
                },
            },
            MuiDateCalendar: {
                styleOverrides: {
                    root: {
                        "& .MuiSvgIcon-root": {
                            color: colors.contrast.primary,
                        },
                    },
                },
            },
            MuiPickersCalendarHeader: {
                styleOverrides: {
                    label: {
                        // Change the color of the day cells
                        color: colors.contrast.primary,
                    },
                    "& .MuiSvgIcon-root": {
                        color: colors.contrast.primary,
                    },
                },
            },
            MuiDayCalendar: {
                styleOverrides: {
                    weekDayLabel: {
                        // Change the color of the day cells
                        color: colors.contrast.primary,
                    },
                },
            },
            MuiPickersYear: {
                styleOverrides: {
                    yearButton: {
                        // Change the color of the day cells
                        color: colors.contrast.primary,
                    },
                },
            },
            MuiCheckbox: {
                styleOverrides: {
                    root: {
                        color: "inherit",
                        "&.Mui-checked": {
                            color: colors.contrast.primary,
                        },
                        "&.Mui-disabled": {
                            opacity: 0.5,
                        },
                    },
                    indeterminate: {
                        color: colors.contrast.primary + " !important",
                    },
                },
            },
            MuiFormControlLabel: {
                styleOverrides: {
                    label: {
                        color: "inherit",
                        "&.Mui-disabled": {
                            color: "inherit", // Color when disabled
                            opacity: 0.5,
                        },
                    },
                },
            },
            MuiIconButton: {
                styleOverrides: {
                    root: {
                        color: "inherit",
                        "&.Mui-disabled": {
                            color: colors.grey.light,
                            opacity: 0.5,
                        },
                    },
                },
                defaultProps: {
                    disableRipple: true,
                },
            },
            MuiDateTimePicker: {
                defaultProps: {
                    format: defaultValues.dateTimeFormat({ exclude_seconds: true }), // Set the global default format
                },
            },
            MuiPickersLayout: {
                styleOverrides: {
                    root: {
                        "& .MuiPickersToolbar-root": {
                            "& .MuiTypography-root": {
                                color: colors.contrast.primary,
                            },
                        },
                        "& .MuiDialogActions-root": {
                            "& .MuiButtonBase-root": {
                                backgroundColor: colors.contrast.primary,
                            },
                        },
                    },
                    contentWrapper: {
                        "& .MuiTimeClock-root": {
                            "& .MuiButtonBase-root": {
                                color: colors.contrast.primary,
                            },
                        },
                    },
                },
            },
            MuiClock: {
                styleOverrides: {
                    clock: {
                        backgroundColor: colors.contrast.primary,
                    },
                },
            },
            // MuiTimePicker: {
            //     defaultProps: {
            //         timezone: defaultValues.timezone
            //     }
            // },
            MuiTab: {
                styleOverrides: {
                    root: {
                        color: colors.contrast.primary,
                        "&.Mui-selected": {
                            color: colors.contrast.primary,
                            backgroundColor: colors.grey.light,
                        },
                    },
                },
                defaultProps: {
                    disableRipple: true,
                },
            },
            MuiTabs: {
                styleOverrides: {
                    root: {
                        backgroundColor: colors.grey.main,
                    },
                },
                defaultProps: {
                    TabIndicatorProps: {
                        style: { display: "none" },
                    },
                },
            },
            MuiDataGrid: {
                styleOverrides: {
                    root: {
                        "--DataGrid-containerBackground": "transparent",
                        "--DataGrid-rowBorderColor": "transparent",
                        color: "inherit",
                        borderColor: colors.grey.light,
                        borderRadius: "10px",
                        padding: "10px 15px",
                        fontWeight: "400",
                        "& .MuiDataGrid-main": {
                            display: "grid",
                        },
                        "& .MuiDataGrid-columnHeaderTitle": {
                            color: colors.blue.main,
                        },
                        [`& .${tablePaginationClasses.selectLabel}`]: {
                            display: "block",
                        },
                        [`& .${tablePaginationClasses.input}`]: {
                            display: "inline-flex",
                        },
                        "& .MuiDataGrid-cell:focus,.MuiDataGrid-columnHeader:focus,.MuiDataGrid-columnHeader:focus-within,.MuiDataGrid-cell:focus-within":
                            {
                                outline: "none",
                            },
                        "& .MuiDataGrid-columnSeparator": {
                            display: "none",
                        },
                        "& .MuiDataGrid-columnHeaders": {
                            backgroundColor: colors.blue.dark,
                        },
                    },
                    panelContent: {
                        color: colors.contrast.primary,
                        "& .MuiInputLabel-root": {
                            color: "inherit",
                        },
                        "& .MuiFormLabel-root": {
                            paddingLeft: "10px",
                            paddingTop: "10px",
                        },
                        "& .MuiInputBase-root": {
                            paddingLeft: "10px",
                            paddingTop: "5px",
                            color: colors.contrast.primary,
                            "& .MuiButtonBase-root": {
                                borderColor: colors.grey.main,
                                color: colors.contrast.primary,
                            },
                            "& .MuiChip-root": {
                                color: colors.contrast.primary,
                            },
                        },
                    },
                    columnsManagementHeader: {
                        "& .MuiInputBase-root": {
                            fontSize: " 14px !important",
                            height: "auto !important",
                        },
                    },
                    columnsManagement: {
                        color: colors.contrast.primary,
                        "& .MuiCheckbox-root": {
                            marginLeft: 4,
                        },
                    },
                    columnsManagementFooter: {
                        color: colors.contrast.primary,
                        "& .MuiButtonBase-root": {
                            color: colors.contrast.primary,
                            "&.MuiButton-text": {
                                color: colors.grey.dark,
                            },
                        },
                        "& .MuiCheckbox-root": {
                            marginLeft: 4,
                        },
                    },
                    overlay: {
                        backgroundColor: colors.grey.dark,
                    },
                    withBorderColor: {
                        borderColor: colors.grey.light,
                    },
                    cell: {
                        // Styles for individual cells
                        color: "inherit",
                    },
                    footerContainer: {
                        // Styles for the footer (pagination, etc.)
                        backgroundColor: colors.grey.main,
                        "& .MuiTablePagination-root": {
                            color: "inherit",
                        },
                        "& .MuiButtonBase-root": {
                            color: "inherit",
                        },
                    },
                },
            },
            MuiChip: {
                defaultProps: {
                    color: "primary",
                },
            },
            MuiList: {
                styleOverrides: {
                    root: {
                        "& .MuiListItemIcon-root .MuiSvgIcon-root": {
                            color: colors.contrast.primary,
                        },
                    },
                },
            },
            MuiAutocomplete: {
                styleOverrides: {
                    popper: {
                        "& .MuiAutocomplete-listbox": {
                            color: colors.contrast.primary,
                        },
                    },
                },
            },
        },
    }),
);

export default theme;
