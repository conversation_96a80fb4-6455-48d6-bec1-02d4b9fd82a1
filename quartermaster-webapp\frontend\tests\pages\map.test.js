import { render, screen, fireEvent } from "@testing-library/react";
import Map from "../../src/pages/Dashboard/Stream/Map";
import { BrowserRouter as Router } from "react-router-dom";
import { useNavigate } from "react-router-dom";

jest.mock("../../src/components/InsetMap", () => () => <div data-testid="inset-map">Inset Map</div>);
jest.mock("../../src/hooks/AppHook", () => ({
    useApp: jest.fn(() => ({ setSelectedVessel: jest.fn() })),
}));

jest.mock("react-router-dom", () => ({
    ...jest.requireActual("react-router-dom"),
    useNavigate: jest.fn(),
}));

describe("Map", () => {
    const mockNavigate = jest.fn();
    const vessel = { id: "vessel1", name: "Test Vessel" };

    beforeEach(() => {
        useNavigate.mockReturnValue(mockNavigate);
    });

    it("renders InsetMap with the correct props", () => {
        render(
            <Router>
                <Map vessel={vessel} />
            </Router>
        );

        const insetMap = screen.getByTestId("inset-map");
        expect(insetMap).toBeInTheDocument();
    });

    it("renders IconButton and navigates to /dashboard/map when clicked", () => {
        render(
            <Router>
                <Map vessel={vessel} />
            </Router>
        );

        const iconButton = screen.getByRole("button");
        expect(iconButton).toBeInTheDocument();

        fireEvent.click(iconButton);

        expect(mockNavigate).toHaveBeenCalledWith("/dashboard/map");
    });

    it("renders IconButton with correct position and zIndex styles", () => {
        render(
            <Router>
                <Map vessel={vessel} />
            </Router>
        );

        const iconButton = screen.getByRole("button");
        expect(iconButton).toHaveStyle("position: absolute");
        expect(iconButton).toHaveStyle("bottom: 25px");
        expect(iconButton).toHaveStyle("left: 1px");
        expect(iconButton).toHaveStyle("z-index: 0");
    });
});
