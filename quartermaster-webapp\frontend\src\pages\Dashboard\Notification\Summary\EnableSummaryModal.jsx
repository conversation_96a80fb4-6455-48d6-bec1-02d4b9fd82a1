import { Modal, Grid, Typo<PERSON>, Button } from "@mui/material";
import ModalContainer from "../../../../components/ModalContainer";
import axiosInstance from "../../../../axios";

const EnableSummaryModal = ({ enableKey, setEnableKey, setEnable }) => {
    const handleClose = () => {
        setEnableKey();
    };

    const onEnableDisable = async () => {
        handleClose();
        setEnable(enableKey._id);
        await axiosInstance
            .patch("/summaryReports/" + enableKey._id, { is_enabled: enableKey.is_enabled ? 0 : 1 }, { meta: { showSnackbar: true } })
            .catch(console.error)
            .finally(() => setEnable());
    };

    return (
        <Modal open={enableKey ? true : false} onClose={handleClose}>
            <ModalContainer title={enableKey?.is_enabled ? "Disable Summary Alert" : "Enable Summary Alert"} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography fontWeight={"500"} fontSize={"15px"}>
                            Are you sure you want to {enableKey?.is_enabled ? "Disable" : "Enable"} Summary Alert ?
                        </Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" className="btn-cancel" onClick={handleClose}>
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button
                                variant="contained"
                                color={enableKey?.is_enabled ? "error" : "success"}
                                onClick={onEnableDisable}
                                sx={{ textTransform: "none", padding: "10px 24px" }}
                            >
                                {enableKey?.is_enabled ? "Disable" : "Enable"}
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default EnableSummaryModal;
