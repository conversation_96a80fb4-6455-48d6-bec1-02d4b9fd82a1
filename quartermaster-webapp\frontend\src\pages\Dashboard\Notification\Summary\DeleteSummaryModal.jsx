import { Modal, Grid, Typo<PERSON>, Button } from "@mui/material";
import ModalContainer from "../../../../components/ModalContainer";
import axiosInstance from "../../../../axios";

const DeleteSummaryModal = ({ deleteKey, setDeleteKey, setDeleting }) => {
    const handleClose = () => {
        setDeleteKey();
    };

    const onDelete = async () => {
        handleClose();
        setDeleting(deleteKey._id);
        await axiosInstance
            .delete("/summaryReports/" + deleteKey._id, { meta: { showSnackbar: true } })
            .catch(console.error)
            .finally(() => setDeleting());
    };

    return (
        <Modal open={deleteKey ? true : false} onClose={handleClose}>
            <ModalContainer title={"Delete Summary Alert"} headerPosition="center">
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography fontWeight={"500"} fontSize={"15px"}>
                            Are you sure you want to Delete Summary Alert ?
                        </Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" className="btn-cancel" onClick={handleClose}>
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" color="error" onClick={onDelete} sx={{ textTransform: "none", padding: "10px 24px" }}>
                                Delete
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default DeleteSummaryModal;
