const { validateData } = require("../middlewares/validator");
const { query } = require("express-validator");
const dayjs = require("dayjs");
const express = require("express");
const awsKinesis = require("../modules/awsKinesis");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const { validateError, canAccessUnit, fileNameTimestamp, generateZip, removeSpecialCharsFromFilename } = require("../utils/functions");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const vesselService = require("../services/Vessel.service");
const Region = require("../models/Region");
const RegionGroup = require("../models/RegionGroup");
const db = require("../modules/db");
const { defaultDateTimeFormat } = require("../utils/timezonesList");
const router = express.Router();
// const { Readable } = require("node:stream");
// const { getObjectStream } = require("../modules/awsS3");

const authUserApiLimiter = rateLimit({
    windowMs: 10 * 1000,
    limit: 50,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

// Middleware to apply the correct limiter
function conditionalRateLimiter(req, res, next) {
    if (req.user) {
        authUserApiLimiter(req, res, next);
    } else {
        apiLimiter(req, res, next);
    }
}

router.use("/", conditionalRateLimiter);

router.get(
    "/listStreams",
    assignEndpointId.bind(this, endpointIds.FETCH_STREAMS_LIST),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { region } = req.query;

            let streams = [];

            if (region) {
                streams = await awsKinesis.listStreams({ region });
            } else {
                const awsRegions = (await Region.find()).filter((r) => r.is_live);

                const allVessels = await Promise.all(
                    awsRegions.map(async (region) => {
                        try {
                            return await awsKinesis.listStreams({ region: region.value });
                        } catch {
                            return [];
                        }
                    }),
                );

                streams = allVessels.flat();
            }

            const regionGroups = await RegionGroup.find();
            streams = streams.map((stream) => {
                const regionGroup = regionGroups.find((rg) => rg.unit_ids.includes(stream.StreamName));
                return { ...stream, RegionGroup: regionGroup ? regionGroup._id : null };
            });

            streams = streams.filter((stream) => canAccessUnit(req, { unit_id: stream.StreamName, name: stream.Tags.Name }));

            res.json(streams);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/dashStreamingSessionURL",
    assignEndpointId.bind(this, endpointIds.FETCH_STREAM_URL),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("streamMode")
                    .isString()
                    .notEmpty()
                    .toUpperCase()
                    .isIn(["LIVE", "ON_DEMAND", "LIVE_REPLAY"])
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("minutes")
                    .isNumeric()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { streamName, region, streamMode, minutes } = req.query;

            const streamInfo = await vesselService.fetchSingle({ unitId: streamName });
            if (!streamInfo) return res.status(400).json({ message: "Vessel does not exist" });

            if (!canAccessUnit(req, streamInfo)) {
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            }

            const url = await awsKinesis.getDashStreamingSessionURL({ streamName, region, streamMode, minutes });

            res.json({ url: url });
        } catch (err) {
            validateError(err, res);
        }
    },
);

const responseWithZipMediaData = async (res, region, streamName, startTime, endTime, targetTimestamp, extension, data, user) => {
    try {
        const tags = await awsKinesis.getStreamTags({ region, streamName });

        const filenameMask = `${fileNameTimestamp()} - ${removeSpecialCharsFromFilename((tags?.Name || streamName).replace(/ /g, "_"))}`;

        const collection = db.qm.collection(`${streamName}_location`);
        if (!collection) throw "Invalid Stream Name";

        let vesselLocation = await collection.findOne({ timestamp: { $gt: new Date(startTime), $lt: new Date(endTime) } });

        // if we can't found the location in the target time span we will get the last one
        if (!vesselLocation) {
            const lastLocations = await collection.find().sort({ $natural: -1 }).limit(1).toArray();
            if (lastLocations.length > 0) {
                vesselLocation = lastLocations[0];
            }
        }

        const metadata = `
Vessel Name: ${tags?.Name}
Location: ${vesselLocation?.latitude}${vesselLocation?.latitude ? "," : ""}${vesselLocation?.longitude}
Timestamp: ${dayjs(Number(targetTimestamp)).format(user.date_time_format || defaultDateTimeFormat)}
Ground Speed: ${vesselLocation?.groundSpeed} Knots
                    `;

        const filesData = [
            {
                name: `${filenameMask}.${extension}`,
                content: data, //clipResponse.Payload,
            },
            {
                name: `${filenameMask}.txt`,
                content: metadata,
            },
        ];

        const zip = generateZip(filesData);
        const stream = zip.generateNodeStream({ type: "nodebuffer", streamFiles: true });

        //Using pipeline for better error handling.
        res.setHeader("Content-Type", "application/zip");
        res.setHeader("Content-Disposition", `attachment; filename="${filenameMask}.zip"`);

        await new Promise((resolve, reject) => {
            stream.on("error", (err) => {
                console.error("stream error:", err);
                reject(err);
            });

            stream
                .pipe(res)
                .on("finish", resolve)
                .on("error", (e) => {
                    reject(e);
                });
        });
    } catch (error) {
        if (!res.headersSent) {
            console.error(`PROC MEDIA ERROR: ${error.message}`);
            res.status(500).json({ message: "Internal server error" });
        }
        res.end();
    }
};

router.get(
    "/getScreenShot",
    assignEndpointId.bind(this, endpointIds.GET_SCREENSHOT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("timestamp")
                    .isNumeric()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("isLive")
                    .isBoolean()
                    .optional()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { streamName, region } = req.query;
            const timestamp = Number(req.query.timestamp);
            const extension = "jpeg";
            const screenshotData = await awsKinesis.getScreenshot(streamName, region, timestamp, extension.toUpperCase());

            if (screenshotData) {
                await responseWithZipMediaData(res, region, streamName, timestamp, timestamp + 10000, timestamp, extension, screenshotData, req.user);
            } else {
                if (!res.headersSent) {
                    res.status(500).json({ message: "Cant process the request" });
                }
                res.end();
            }
        } catch (error) {
            console.error("Error in get-screenshot route:", error);
            if (!res.headersSent) {
                if (error.name === "ResourceNotFoundException") {
                    res.status(404).json({ message: "Stream not found" });
                } else if (error.name === "InvalidArgumentException") {
                    res.status(400).json({ message: "Invalid parameters" });
                } else if (error.name === "NotAuthorizedException") {
                    res.status(403).json({ message: "Not authorized to access the stream." });
                } else {
                    res.status(500).json({ message: "Internal server error" });
                }
            }
            res.end();
        }
    },
);

router.get(
    "/getClip",
    assignEndpointId.bind(this, endpointIds.GET_CLIP),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("timestamp")
                    .optional()
                    .isNumeric()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("isLive")
                    .isBoolean()
                    .optional()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            // take the window in scope 30 seconds before target timestamp and 30 seconds after
            const offsetBefore = 30000;
            const offsetAfter = 30000;

            const { streamName, region, timestamp } = req.query;
            let targetTimestamp = Number(timestamp);

            const startTime = targetTimestamp - offsetBefore;
            const endTime = targetTimestamp + offsetAfter;

            const clipResponse = await awsKinesis.getClip(streamName, region, startTime, endTime);

            if (clipResponse.Payload instanceof Buffer) {
                const extension = clipResponse.ContentType.split("/").pop();
                await responseWithZipMediaData(
                    res,
                    region,
                    streamName,
                    startTime,
                    endTime,
                    targetTimestamp,
                    extension,
                    clipResponse.Payload,
                    req.user,
                );
            } else {
                console.warn("Unexpected Payload type: ", typeof clipResponse.Payload);
                if (!res.headersSent) {
                    res.status(500).json({ message: "Unexpected payload type from Streams Service" });
                }
                res.end();
            }
        } catch (error) {
            console.error("Error in get-clip route:", error);
            if (!res.headersSent) {
                if (error.name === "ResourceNotFoundException") {
                    res.status(404).json({ message: "Stream not found" });
                } else if (error.name === "InvalidArgumentException") {
                    res.status(400).json({ message: "Invalid parameters" });
                } else if (error.name === "NotAuthorizedException") {
                    res.status(403).json({ message: "Not authorized to access the stream." });
                } else {
                    res.status(400).json({ message: error.message });
                }
            }
            res.end();
        }
    },
);

router.get(
    "/hlsStreamingSessionURL",
    assignEndpointId.bind(this, endpointIds.FETCH_STREAM_URL),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                query("streamName")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("region")
                    .isString()
                    .notEmpty()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("streamMode")
                    .isString()
                    .notEmpty()
                    .toUpperCase()
                    .isIn(["LIVE", "ON_DEMAND", "LIVE_REPLAY"])
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                query("minutes")
                    .isNumeric()
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`)
                    .optional(),
            ],
            req,
            res,
            next,
        ),
    async (req, res) => {
        try {
            const { streamName, region, streamMode, minutes } = req.query;

            const streamInfo = await vesselService.fetchSingle({ unitId: streamName });
            if (!streamInfo) return res.status(400).json({ message: "Vessel does not exist" });

            if (!canAccessUnit(req, streamInfo)) {
                return res.status(403).json({ message: `Cannot access stream '${streamName}'` });
            }

            const url = await awsKinesis.getHlsStreamingSessionURL({ streamName, region, streamMode, minutes });

            res.json({ url: url });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   name: Streams
 *   description: Get data regarding streams
 * components:
 *   schemas:
 *     Stream:
 *       type: object
 *       properties:
 *         DeviceName:
 *           type: string
 *           nullable: true
 *           example: null
 *         StreamName:
 *           type: string
 *           description: The name of the stream.
 *           example: "prototype-24"
 *         StreamARN:
 *           type: string
 *           description: The Amazon Resource Name (ARN) of the stream.
 *           example: "arn:aws:kinesisvideo:ap-southeast-1:123456789012:stream/prototype-24/1724069687123"
 *         MediaType:
 *           type: string
 *           nullable: true
 *           example: null
 *         KmsKeyId:
 *           type: string
 *           description: The KMS key ID used for encryption.
 *           example: "arn:aws:kms:ap-southeast-1:123456789012:alias/aws/kinesisvideo"
 *         Version:
 *           type: string
 *           description: The version of the stream.
 *           example: "ww1ljAmwqPscFzFHDxRO"
 *         Status:
 *           type: string
 *           enum:
 *             - ACTIVE
 *             - INACTIVE
 *           description: The current status of the stream.
 *           example: "ACTIVE"
 *         CreationTime:
 *           type: string
 *           format: date-time
 *           description: The time when the stream was created.
 *           example: "2024-08-19T12:14:47.123Z"
 *         DataRetentionInHours:
 *           type: integer
 *           description: The data retention period in hours.
 *           example: 1
 *         Tags:
 *           type: object
 *           description: The tags associated with the stream.
 *           properties:
 *             Name:
 *               type: string
 *               example: BRP Malapascua MRRV-4403
 *             Thumbnail:
 *               type: string
 *               example: https://portal.quartermaster.us/4403.jpg
 *           example: {
 *             "Name": "BRP Malapascua MRRV-4403",
 *             "Thumbnail": "https://portal.quartermaster.us/4403.jpg"
 *           }
 *         IsLive:
 *           type: boolean
 *           description: Indicates whether the stream is live.
 *           example: false
 *         RegionGroup:
 *           type: string
 *           description: The region group of the stream.
 *           example: "66e000000000000000000000"
 */

/**
 * @swagger
 * /kinesis/listStreams:
 *   get:
 *     summary: List all streams
 *     description: Rate limited to 20 requests every 5 seconds
 *     tags: [Streams]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: region
 *         in: query
 *         required: false
 *         description: The AWS region to list streams from
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *     responses:
 *       200:
 *         description: A list of streams
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Stream'
 *       400:
 *         description: Invalid region provided
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /kinesis/hlsStreamingSessionURL:
 *   get:
 *     summary: Get HLS streaming session URL
 *     description: Rate limited to 20 requests every 5 seconds
 *     tags: [Streams]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: streamName
 *         in: query
 *         required: true
 *         description: The name of the stream to get the URL for
 *         schema:
 *           type: string
 *           example: prototype-37
 *       - name: region
 *         in: query
 *         required: true
 *         description: The AWS region of the stream
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *       - name: streamMode
 *         in: query
 *         required: true
 *         description: The mode of the stream, either 'LIVE' or 'ON_DEMAND'
 *         schema:
 *           type: string
 *           enum: [LIVE, ON_DEMAND]
 *       - name: minutes
 *         in: query
 *         required: false
 *         description: The interval for which replay is fetched
 *         schema:
 *           type: number
 *           example: 60
 *     responses:
 *       200:
 *         description: The HLS streaming session URL
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                   description: The HLS streaming session URL
 *       400:
 *         description: Invalid parameters provided
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /kinesis/dashStreamingSessionURL:
 *   get:
 *     summary: Get Dash streaming session URL
 *     description: Rate limited to 20 requests every 5 seconds
 *     tags: [Streams]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: streamName
 *         in: query
 *         required: true
 *         description: The name of the stream to get the URL for
 *         schema:
 *           type: string
 *           example: prototype-37
 *       - name: region
 *         in: query
 *         required: true
 *         description: The AWS region of the stream
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *       - name: streamMode
 *         in: query
 *         required: true
 *         description: The mode of the stream, either 'LIVE' or 'ON_DEMAND'
 *         schema:
 *           type: string
 *           enum: [LIVE, ON_DEMAND]
 *       - name: minutes
 *         in: query
 *         required: false
 *         description: The interval for which replay is fetched
 *         schema:
 *           type: number
 *           example: 60
 *     responses:
 *       200:
 *         description: The HLS streaming session URL
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 url:
 *                   type: string
 *                   description: The HLS streaming session URL
 *       400:
 *         description: Invalid parameters provided
 *       500:
 *         description: Server error
 */

/**
 * @swagger
 * /kinesis/getClip:
 *   get:
 *     summary: Get the zip with part (30 seconds before and 30 seconds after (if possible) the passed timestamp) of the stream and metadata
 *     description: Rate limited to 20 requests every 5 seconds
 *     tags:
 *       - Streams
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - name: streamName
 *         in: query
 *         required: true
 *         description: The name of the stream to get the URL for
 *         schema:
 *           type: string
 *           example: prototype-37
 *       - name: region
 *         in: query
 *         required: true
 *         description: The AWS region of the stream
 *         schema:
 *           type: string
 *           example: ap-southeast-1
 *       - name: timestamp
 *         in: query
 *         required: false
 *         description: Unix based timestamp for the moment where we should to take the clip (for recorded sessions)
 *         schema:
 *           type: number
 *           example: 1743578679483
 *       - name: requestTime
 *         in: query
 *         required: false
 *         description: Unix based timestamp for the moment where we should to take the clip (for live sessions)
 *         schema:
 *           type: number
 *           example: 1743578679483
 *     responses:
 *       '200':
 *         description: Stream of a prepared zip file.
 *         headers:
 *           Content-Disposition:
 *             schema:
 *               type: string
 *             example: 'attachment; filename="my-download.zip"'
 *             description: Indicates the file should be downloaded with this filename
 *         content:
 *           application/zip:
 *             schema:
 *               type: string
 *               format: binary
 *       '400':
 *         description: Invalid parameters provided
 *       '500':
 *         description: Server error
 */
