import { render, screen, fireEvent } from '@testing-library/react';
import ConfirmModal from '../../src/components/ConfirmModal';

jest.mock('../../src/components/ModalContainer', () => ({ children }) => <div>{children}</div>);

describe('ConfirmModal', () => {
    const mockOnClose = jest.fn();
    const mockOnConfirm = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should call onClose when the Cancel button is clicked', () => {
        render(
            <ConfirmModal
                title="Confirm Action"
                message="Are you sure you want to proceed?"
                initialState={true}
                onClose={mockOnClose}
                onConfirm={mockOnConfirm}
            />
        );

        const cancelButton = screen.getByText('Cancel');
        fireEvent.click(cancelButton);

        expect(mockOnClose).toHaveBeenCalledTimes(1);
    });

    it('should call onConfirm when the Confirm button is clicked', () => {
        render(
            <ConfirmModal
                title="Confirm Action"
                message="Are you sure you want to proceed?"
                initialState={true}
                onClose={mockOnClose}
                onConfirm={mockOnConfirm}
            />
        );

        const confirmButton = screen.getByText('Confirm');
        fireEvent.click(confirmButton);

        expect(mockOnConfirm).toHaveBeenCalledTimes(1);
    });

    it('should apply "error" color to the Confirm button when isDanger is true', () => {
        render(
            <ConfirmModal
                title="Confirm Action"
                message="Are you sure you want to proceed?"
                initialState={true}
                onClose={mockOnClose}
                onConfirm={mockOnConfirm}
                isDanger={true}
            />
        );

        const confirmButton = screen.getByText('Confirm');
        expect(confirmButton).toHaveClass('MuiButton-containedError');
    });

    it('should not render Modal when initialState is false', () => {
        render(
            <ConfirmModal
                title="Confirm Action"
                message="Are you sure you want to proceed?"
                initialState={false}
                onClose={mockOnClose}
                onConfirm={mockOnConfirm}
            />
        );

        expect(screen.queryByText('Confirm Action')).not.toBeInTheDocument();
    });
});
