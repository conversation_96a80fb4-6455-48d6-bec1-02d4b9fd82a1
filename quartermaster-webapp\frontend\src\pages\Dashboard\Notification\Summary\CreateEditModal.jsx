import {
    Button,
    Grid,
    Modal,
    FormControl,
    FormLabel,
    Checkbox,
    ListItemText,
    Box,
    Autocomplete,
    CircularProgress,
    TextField,
    alpha,
    Typography,
    FormControlLabel,
    Chip,
} from "@mui/material";
import { useEffect, useState } from "react";
import theme from "../../../../theme";
import ModalContainer from "../../../../components/ModalContainer";
import axiosInstance from "../../../../axios";
import { useUser } from "../../../../hooks/UserHook";
import { arraysSame, validateEmailDomain } from "../../../../utils";

const ALL_OPTION = "All";

const getVessels = (editSummary, vessels) => {
    return editSummary.unit_id.includes("all") ? [ALL_OPTION] : vessels.filter((v) => editSummary.unit_id.includes(v.unit_id)).map((v) => v.name);
};

const CreateEditSummaryModal = ({
    showSummaryModal,
    setShowSummaryModal,
    editSummary,
    setEditSummary,
    setNotificationAddLoad,
    notificationAddLoad,
    vessels,
    emailDomains,
    isEditModal,
}) => {
    const { user } = useUser();
    const [submitDisable, setSubmitDisable] = useState(true);
    const vesselName = vessels?.map((vessel) => vessel.name.toLowerCase() !== "unregistered" && vessel.name).filter(Boolean) || [];
    const vesselsNames = [ALL_OPTION].concat(vesselName);
    const [vessel, setVessel] = useState(
        !isEditModal
            ? []
            : editSummary.unit_id.includes("all")
              ? [ALL_OPTION]
              : vessels.filter((v) => editSummary.unit_id.includes(v.unit_id)).map((v) => v.name),
        // Mahsam: deprecating title based implementation, leads to consistency issues when updating
        // Array.isArray(editSummary.title)
        //     ? editSummary.title.includes("all")
        //         ? [ALL_OPTION]
        //         : editSummary.title
        //     : [editSummary.title],
    );
    const [notificationPreference, setNotificationPreference] = useState(isEditModal ? editSummary.preference : []);
    const [emails, setEmails] = useState(isEditModal ? editSummary.receivers : []);
    const [emailInput, setEmailInput] = useState("");
    const [emailError, setEmailError] = useState("");
    const [isShowEmail] = useState(false);

    useEffect(() => {
        if (isEditModal) {
            setSubmitDisable(
                vessel.length <= 0 ||
                    notificationPreference.length <= 0 ||
                    (arraysSame(getVessels(editSummary, vessels), vessel) && arraysSame(editSummary.preference, notificationPreference)),
            );
        } else {
            setSubmitDisable(!(vessel.length > 0 && notificationPreference.length > 0));
        }
    }, [vessel, notificationPreference]);

    const handlePreferenceChange = (e) => {
        const { name, checked } = e.target;
        checked;
        setNotificationPreference((prev) =>
            notificationPreference.includes(name) ? notificationPreference.filter((n) => n !== name) : [...prev, name],
        );
    };

    const handleClose = (event, reason) => {
        if (reason === "backdropClick") {
            return;
        }

        setSubmitDisable(true);
        setEditSummary();
        setShowSummaryModal(false);
        if (isEditModal) {
            setSubmitDisable(true);
            setEditSummary();
            setShowSummaryModal(false);
        } else {
            setShowSummaryModal(false);
            setVessel([]);
            setNotificationPreference([]);
            setEmails([]);
            setEmailInput("");
        }
    };

    const handleSubmit = async () => {
        setNotificationAddLoad(true);

        try {
            if (isEditModal) {
                const data = {
                    preference: notificationPreference,
                    unit_id: vessel.includes(ALL_OPTION)
                        ? ["all"]
                        : vessel
                              .map((v) => {
                                  const found = vessels.find((vess) => vess.name === v);
                                  return found ? found.unit_id : null;
                              })
                              .filter((v) => v != null),
                    title: vessel.includes(ALL_OPTION) ? ["all"] : vessel,
                    // receivers: emails,
                    ...(emails.length > 0 && { receivers: emails }),
                };
                await axiosInstance.patch(`/summaryReports/${editSummary._id}`, data, { meta: { showSnackbar: true } }).then(() => {
                    setNotificationAddLoad(false);
                    setSubmitDisable(true);
                    setEditSummary();
                });
                // .catch((err) => {
                //     setNotificationAddLoad(false)
                // })
            } else {
                const data = {
                    preference: notificationPreference,
                    unit_id: vessel.includes(ALL_OPTION)
                        ? ["all"]
                        : vessel
                              .map((v) => {
                                  const found = vessels.find((vess) => vess.name === v);
                                  return found ? found.unit_id : null;
                              })
                              .filter((v) => v != null),
                    title: vessel.includes(ALL_OPTION) ? ["all"] : vessel,
                    ...(emails.length > 0 && { receivers: emails }),
                };
                await axiosInstance.post("/summaryReports", data, { meta: { showSnackbar: true } }).then(() => {
                    setSubmitDisable(false);
                    setVessel([]);
                    setNotificationPreference([]);
                    setEmails([]);
                    setEmailInput("");
                    setNotificationAddLoad(false);
                });
                // .catch((err) => {
                //     setNotificationAddLoad(false)
                // })
            }
        } catch (error) {
            console.log(`Error ${isEditModal ? "Edit" : "Create"} Summary ${error}`);
        }
        setShowSummaryModal(false);
    };

    const handleSelectChange = (setSelected, value, selected) => {
        const procAllMultiselectChoose = () => {
            let res;
            if (value.includes(ALL_OPTION)) {
                if (value.length > 1 && selected.includes(ALL_OPTION)) {
                    res = value.filter((item) => item !== ALL_OPTION);
                } else {
                    res = [ALL_OPTION];
                }
            } else {
                res = value;
            }

            return res;
        };

        value = procAllMultiselectChoose();
        setSelected(value);
    };

    const handleClear = () => {
        setSubmitDisable(true);
        if (isEditModal) {
            setVessel(getVessels(editSummary, vessels));
            setNotificationPreference(editSummary.preference);
        } else {
            setVessel([]);
            setNotificationPreference([]);
            setEmails([]);
            setEmailInput("");
        }
    };

    const handleEmailInputChange = (e) => {
        setEmailInput(e.target.value);
    };
    const handleEmailInputKeyDown = (e) => {
        setEmailError("");
        if (e.key === "Enter" || e.key === ",") {
            e.preventDefault();
            const newEmail = emailInput.trim();

            if (!newEmail) {
                setEmailError("Email cannot be empty.");
                return;
            }
            if (!user.email) {
                setEmailError(`You cannot add an email because no email is associated with your account.`);
                return;
            }
            const validationError = validateEmailDomain(newEmail, user, emailDomains);

            if (validationError) {
                setEmailError(validationError);
                return;
            }
            if (!emails.includes(newEmail)) {
                setEmails([...emails, newEmail]);
                setEmailInput("");
                setEmailError("");
            } else {
                setEmailError("Email already exists in the list.");
            }
        }
    };

    const handleEmailDelete = (emailToDelete) => {
        setEmails(emails.filter((email) => email !== emailToDelete));
    };
    const renderAutocomplete = (items, selected, setSelected, label, multiple = true) => (
        <FormControl sx={{ width: "100%", maxHeight: "150px", overflowX: "auto" }} size="small">
            <FormLabel sx={{ marginBottom: 1, color: "white", fontWeight: "bold" }}>{label}</FormLabel>
            <Autocomplete
                multiple={multiple}
                value={selected}
                onChange={(event, newValue) => handleSelectChange(setSelected, newValue, selected)}
                options={items}
                disableCloseOnSelect
                getOptionLabel={(option) =>
                    typeof option === "string" ? option.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase()) : ""
                }
                renderInput={(params) => <TextField {...params} label={"Select " + label} variant="outlined" />}
                sx={{ "& .MuiFormLabel-root": { color: alpha("#FFFFFF", 0.6), fontWeight: 400 } }}
                renderOption={(props, option, { selected, index }) => (
                    <li {...props} key={option + index}>
                        <Checkbox checked={selected} />
                        <ListItemText
                            primary={typeof option === "string" ? option.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase()) : ""}
                        />
                    </li>
                )}
                isOptionEqualToValue={(option, value) => option === value}
            />
        </FormControl>
    );

    return (
        <Modal open={Boolean(showSummaryModal)} onClose={handleClose}>
            <ModalContainer title={isEditModal ? "Edit Summary" : "Create Summary"} onClose={handleClose} showDivider>
                <Grid container direction="column" gap={2} width={{ xs: 300, sm: 500 }} maxHeight="70vh" overflow="auto" sx={{ flexWrap: "nowrap" }}>
                    {renderAutocomplete(vesselsNames, vessel, setVessel, "Vessels")}
                </Grid>
                <Grid container width={{ xs: 300, sm: 500 }} sx={{ marginTop: "10px", marginBottom: "10px" }}>
                    <Grid width={"100%"} sx={{ paddingTop: "8px" }}>
                        <Typography>Summary Preference</Typography>
                    </Grid>
                    <Grid container>
                        <FormControlLabel
                            control={<Checkbox checked={notificationPreference.includes("daily")} onChange={handlePreferenceChange} name="daily" />}
                            label="Daily"
                            componentsProps={{ typography: { fontWeight: 300 } }}
                        />
                        <FormControlLabel
                            control={<Checkbox checked={notificationPreference.includes("weekly")} onChange={handlePreferenceChange} name="weekly" />}
                            label="Weekly"
                            componentsProps={{ typography: { fontWeight: 300 } }}
                        />
                        <FormControlLabel
                            control={
                                <Checkbox checked={notificationPreference.includes("monthly")} onChange={handlePreferenceChange} name="monthly" />
                            }
                            label="Monthly"
                            componentsProps={{ typography: { fontWeight: 300 } }}
                        />
                    </Grid>
                </Grid>

                {isShowEmail ? (
                    <Grid container direction="column" gap={2} width={{ xs: 300, sm: 500 }} sx={{ marginTop: "5px", marginBottom: "5px" }}>
                        <Grid>
                            <Typography item>Email Addresses</Typography>
                        </Grid>
                        <Box
                            sx={{
                                display: "flex",
                                flexWrap: "wrap",
                                alignItems: "center",
                                gap: 1,
                                border: "1px solid rgba(255, 255, 255, 0.23)",
                                borderRadius: "4px",
                                padding: "8px",
                                minHeight: "50px",
                                maxHeight: "120px",
                                overflow: "auto",
                            }}
                        >
                            {user.email && (
                                <Chip
                                    key={user.email}
                                    label={user.email}
                                    onDelete={() => handleEmailDelete(user.email)}
                                    disabled
                                    sx={{
                                        borderRadius: "4px",
                                        color: "#FFFFFF",
                                        fontWeight: "bold",
                                        backgroundColor: "#1B1F2D",
                                        "& .MuiChip-deleteIcon": {
                                            color: theme.palette.grey[500],
                                        },
                                    }}
                                />
                            )}
                            {emails.map((email) => (
                                <Chip
                                    key={email}
                                    label={email}
                                    onDelete={() => handleEmailDelete(email)}
                                    sx={{
                                        borderRadius: "4px",
                                        color: "#FFFFFF",
                                        backgroundColor: "#1B1F2D",
                                        "& .MuiChip-deleteIcon": {
                                            color: theme.palette.grey[500],
                                        },
                                    }}
                                />
                            ))}
                            <TextField
                                // label="Enter Email"
                                variant="outlined"
                                value={emailInput}
                                onChange={handleEmailInputChange}
                                onKeyDown={handleEmailInputKeyDown}
                                placeholder="Additional Email Addresses"
                                sx={{
                                    flexGrow: 1,
                                    minWidth: "120px",
                                    "& .MuiOutlinedInput-root": {
                                        padding: "4px",
                                        border: "none",
                                        "& fieldset": {
                                            border: "none",
                                        },
                                    },
                                    "& .MuiInputBase-input": {
                                        padding: "6px",
                                        textAlign: "left",
                                    },
                                }}
                            />
                        </Box>
                        {emailError && (
                            <Typography color="error" variant="body2" sx={{ mt: 1 }}>
                                {emailError}
                            </Typography>
                        )}
                    </Grid>
                ) : (
                    <></>
                )}

                <Grid container gap={2} justifyContent="center" mt={2}>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                textTransform: "none",
                                border: "1px solid grey",
                                padding: "10px",
                            }}
                            onClick={handleClear}
                        >
                            Reset
                        </Button>
                    </Grid>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                textTransform: "none",
                                border: "1px solid grey",
                                padding: "10px",
                            }}
                            onClick={handleClose}
                        >
                            Close
                        </Button>
                    </Grid>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                display: "flex",
                                gap: 1,
                                backgroundColor: theme.palette.custom.mainBlue,
                                "&:hover": { backgroundColor: theme.palette.custom.mainBlue },
                            }}
                            variant="contained"
                            onClick={handleSubmit}
                            disabled={notificationAddLoad || submitDisable}
                        >
                            {notificationAddLoad && <CircularProgress size={18} />}
                            {isEditModal ? "Update" : "Save"}
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default CreateEditSummaryModal;
