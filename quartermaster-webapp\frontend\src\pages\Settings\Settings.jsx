import React, { useEffect, useState, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Switch, FormControlLabel, Button, Tooltip, CircularProgress, Select, MenuItem } from "@mui/material";
import axiosInstance from "../../axios";
import { useUser } from "../../hooks/UserHook";
import ConfirmModal from "../../components/ConfirmModal";
import theme from "../../theme";

import { useApp } from "../../hooks/AppHook";
import { datetimeFormats, roles } from "../../utils";
import notificationAlertController from "../../controllers/NotificationAlerts.controller";

export default function Settings() {
    const { devMode, setDevMode } = useApp();
    const { user, fetchUser } = useUser();

    const [isTwoFactorAuthEnabled, setIsTwoFactorAuthEnabled] = useState(false);
    const [dateTimeFormat, setDateTimeFormat] = useState(user?.date_time_format);
    const [isUseMGRS, setIsUseMGRS] = useState(!!user?.use_MGRS);

    const [isConfirmModalOpen, setConfirmModalOpen] = useState(false);
    const [confirmModalDetails, setConfirmModalDetails] = useState({ title: "", message: "" });
    const [isLoading, setIsLoading] = useState(false);

    const openConfirmationDialog = ({ title, message }) => {
        return new Promise((resolve) => {
            const handleConfirm = () => {
                resolve(true);
                setConfirmModalOpen(false);
            };

            const handleCancel = () => {
                resolve(false);
                setConfirmModalOpen(false);
            };

            setConfirmModalDetails({ title, message, onConfirm: handleConfirm, onCancel: handleCancel });
            setConfirmModalOpen(true);
        });
    };

    useEffect(() => {
        if (user?.email_verification_enabled !== undefined) {
            setIsTwoFactorAuthEnabled(user.email_verification_enabled);
        }

        setDateTimeFormat(user?.date_time_format);
    }, [user]);

    const toggleDevMode = (value) => {
        setDevMode(value);
        window.location.reload();
    };

    const handleSwitchChange = (event) => {
        setIsTwoFactorAuthEnabled(event.target.checked);
    };

    const handleDateFormatChange = (event) => {
        setDateTimeFormat(event.target.value);
    };

    const handleMGRSSwitchChange = (event) => {
        setIsUseMGRS(event.target.checked);
    };

    const handleSubmit = async () => {
        if (isTwoFactorAuthEnabled !== user?.email_verification_enabled) {
            const confirmed = await openConfirmationDialog({
                title: "Confirm",
                message: (
                    <>
                        Are you sure you want to <b>{isTwoFactorAuthEnabled ? "Enable" : "Disable"}</b> the Two Factor Authentication.
                    </>
                ),
            });

            if (!confirmed) {
                handleCancel();
                return;
            }
            axiosInstance
                .patch("/users/userEmailVerification", null, {
                    params: { email_verification_enabled: isTwoFactorAuthEnabled },
                })
                .then(() => fetchUser().catch(console.error))
                .catch(console.error);
        }

        if (dateTimeFormat !== user?.date_time_format || isUseMGRS !== !!user?.use_MGRS) {
            const args = {};

            if (isUseMGRS !== user?.use_MGRS) {
                args["use_MGRS"] = isUseMGRS;
            }

            if (dateTimeFormat !== user?.date_time_format) {
                args["date_time_format"] = dateTimeFormat;
            }

            axiosInstance
                .patch("/users/updateSettings", null, {
                    params: args,
                })
                .then(() => fetchUser().catch(console.error))
                .catch(console.error);
        }
    };

    const handleCancel = () => {
        setIsTwoFactorAuthEnabled(user.email_verification_enabled);
    };

    const isModified = useMemo(
        () =>
            isTwoFactorAuthEnabled !== user?.email_verification_enabled ||
            dateTimeFormat !== user?.date_time_format ||
            isUseMGRS !== !!user?.use_MGRS,
        [isTwoFactorAuthEnabled, user?.email_verification_enabled, user?.date_time_format, dateTimeFormat, isUseMGRS, user?.use_MGRS],
    );

    const getLatestNotificationAlerts = async () => {
        setIsLoading(true);
        try {
            const res = await notificationAlertController.getLatestUserNotificationAlerts();
            if (res.status === 200) {
                setIsLoading(false);
            } else {
                setIsLoading(false);
            }
            return res;
        } catch (error) {
            setIsLoading(false);
            console.log("Error Get All Latest Notification Alerts " + error);
            return error.response.data;
        }
    };

    if (!user) {
        return (
            <Grid container justifyContent="center" alignItems="center" height="100%" sx={{ backgroundColor: theme.palette.custom.darkBlue }}>
                <CircularProgress size={30} sx={{ color: "white" }} />
            </Grid>
        );
    }

    return (
        <Grid
            container
            overflow="auto"
            height="100%"
            width="100%"
            flexDirection={{ xs: "row", lg: "column" }}
            paddingX={{ xs: 3, md: 10 }}
            paddingY={{ xs: 5, md: 6 }}
            sx={{
                backgroundColor: theme.palette.custom.darkBlue,
            }}
        >
            <Grid container direction="column" gap="30px">
                <Grid>
                    <Typography variant="h4" component="h1" color="#FFFFFF">
                        Profile Settings
                    </Typography>
                    <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="400">
                        Update your profile.
                    </Typography>
                </Grid>

                <Grid container direction="column" gap="30px">
                    <Grid container paddingBottom="20px" borderBottom={theme.palette.custom.borderColor} borderRadius="10px">
                        <Grid
                            size={{
                                md: 5,
                                sm: 12,
                            }}
                        >
                            <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                                Two Factor Authentication
                            </Typography>
                            <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                                You will have to enter OTP while logging into the account.
                            </Typography>
                        </Grid>
                        <Grid
                            size={{
                                md: "grow",
                                sm: 12,
                            }}
                        >
                            <Tooltip enterDelay={300} title={!user?.email ? "Your Account is not linked with an email" : ""} placement="bottom">
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={isTwoFactorAuthEnabled}
                                            onChange={handleSwitchChange}
                                            disabled={!user?.email}
                                            color="primary"
                                            disableRipple
                                            sx={{
                                                height: "50px",
                                                width: "80px",
                                                borderRadius: "50px",
                                                "& .MuiSwitch-switchBase": {
                                                    padding: "15px 4px",
                                                    transform: "translate(9px, -2px)",
                                                },
                                                "& .MuiSwitch-track": {
                                                    backgroundColor: "#FFFFFF",
                                                    height: "30px",
                                                    borderRadius: "50px",
                                                },
                                                "& .Mui-checked+.MuiSwitch-track": {
                                                    backgroundColor: theme.palette.custom.mainBlue + " !important",
                                                    opacity: "1 !important",
                                                },
                                                "& .Mui-checked.MuiSwitch-switchBase": {
                                                    transform: "translate(36px, -2px)",
                                                },
                                                "& .MuiSwitch-thumb": {
                                                    backgroundColor: "#FFFFFF",
                                                    height: "28px",
                                                    width: "28px",
                                                },
                                                "& .Mui-disabled": {
                                                    opacity: 0.4,
                                                },
                                                "& .Mui-disabled+.MuiSwitch-track": {
                                                    opacity: "0.3 !important",
                                                },
                                            }}
                                        />
                                    }
                                    label="Enable Two Factor Authentication"
                                    sx={{
                                        "& .MuiFormControlLabel-label": {
                                            color: "#FFFFFF !important",
                                            fontSize: "18px",
                                            fontWeight: "400",
                                        },
                                    }}
                                />
                            </Tooltip>
                        </Grid>
                    </Grid>
                    <Grid container paddingBottom="20px" borderBottom={theme.palette.custom.borderColor} borderRadius="10px">
                        <Grid
                            size={{
                                md: 5,
                                sm: 12,
                            }}
                        >
                            <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                                Location Format
                            </Typography>
                            <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                                Choose your preferred location format.
                            </Typography>
                        </Grid>
                        <Grid
                            size={{
                                md: "grow",
                                sm: 12,
                            }}
                        >
                            <FormControlLabel
                                control={
                                    <Switch
                                        checked={isUseMGRS}
                                        onChange={handleMGRSSwitchChange}
                                        color="primary"
                                        disableRipple
                                        sx={{
                                            height: "50px",
                                            width: "80px",
                                            borderRadius: "50px",
                                            "& .MuiSwitch-switchBase": {
                                                padding: "15px 4px",
                                                transform: "translate(9px, -2px)",
                                            },
                                            "& .MuiSwitch-track": {
                                                backgroundColor: theme.palette.custom.mainBlue + " !important",
                                                height: "30px",
                                                borderRadius: "50px",
                                                opacity: "1 !important",
                                            },
                                            "& .Mui-checked+.MuiSwitch-track": {
                                                backgroundColor: theme.palette.custom.mainBlue + " !important",
                                                opacity: "1 !important",
                                            },
                                            "& .Mui-checked.MuiSwitch-switchBase": {
                                                transform: "translate(36px, -2px)",
                                            },
                                            "& .MuiSwitch-thumb": {
                                                backgroundColor: "#FFFFFF",
                                                height: "28px",
                                                width: "28px",
                                            },
                                            "& .Mui-disabled": {
                                                opacity: 0.4,
                                            },
                                            "& .Mui-disabled+.MuiSwitch-track": {
                                                opacity: "0.3 !important",
                                            },
                                        }}
                                    />
                                }
                                label={isUseMGRS ? "MGRS" : "Lat/Lng"}
                                sx={{
                                    "& .MuiFormControlLabel-label": {
                                        color: "#FFFFFF !important",
                                        fontSize: "18px",
                                        fontWeight: "400",
                                    },
                                }}
                            />
                        </Grid>
                    </Grid>

                    <Grid container paddingBottom="20px" borderBottom={theme.palette.custom.borderColor} borderRadius="10px">
                        <Grid
                            size={{
                                md: 5,
                                sm: 12,
                            }}
                        >
                            <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                                Date/ Time Format
                            </Typography>
                            <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                                Choose your preferred date/time format.
                            </Typography>
                        </Grid>
                        <Grid
                            size={{
                                md: "grow",
                                sm: 12,
                            }}
                        >
                            <FormControlLabel
                                control={
                                    <Select
                                        labelId="duration-select-label"
                                        id="duration-select"
                                        value={dateTimeFormat ?? Object.keys(datetimeFormats)[0]}
                                        onChange={handleDateFormatChange}
                                        variant={"filled"}
                                    >
                                        {Object.entries(datetimeFormats).map(([format, name]) => (
                                            <MenuItem key={format} value={format}>
                                                {name}
                                            </MenuItem>
                                        ))}
                                    </Select>
                                }
                            />
                        </Grid>
                    </Grid>
                </Grid>

                <Grid item container flexDirection="column" gap="30px" display={user && user.role_id === roles.super_admin ? "flex" : "none"}>
                    <Grid>
                        <Typography variant="h4" component="h1" color="#FFFFFF">
                            Dashboard Settings
                        </Typography>
                        <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="400">
                            Customize the behavior of the dashboard.
                        </Typography>
                    </Grid>
                </Grid>

                <Grid
                    container
                    display={user && (user.role_id === roles.super_admin || user.role_id === roles.internal_admin) ? "flex" : "none"}
                    paddingBottom="20px"
                    borderBottom={(theme) => theme.palette.custom.borderColor}
                    borderRadius="10px"
                >
                    <Grid
                        size={{
                            md: 5,
                            sm: 12,
                        }}
                    >
                        <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                            Developer Mode
                        </Typography>
                        <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                            View technical details on the dashboard.
                        </Typography>
                    </Grid>
                    <Grid
                        size={{
                            md: "grow",
                            sm: 12,
                        }}
                    >
                        <FormControlLabel
                            control={
                                <Switch
                                    checked={devMode}
                                    onChange={(e) => toggleDevMode(e.target.checked)}
                                    color="primary"
                                    disableRipple
                                    sx={{
                                        height: "50px",
                                        width: "80px",
                                        borderRadius: "50px",
                                        "& .MuiSwitch-switchBase": {
                                            padding: "15px 4px",
                                            transform: "translate(9px, -2px)",
                                        },
                                        "& .MuiSwitch-track": {
                                            backgroundColor: "#FFFFFF",
                                            height: "30px",
                                            borderRadius: "50px",
                                        },
                                        "& .Mui-checked+.MuiSwitch-track": {
                                            backgroundColor: (theme) => theme.palette.custom.mainBlue + " !important",
                                            opacity: "1 !important",
                                        },
                                        "& .Mui-checked.MuiSwitch-switchBase": {
                                            transform: "translate(36px, -2px)",
                                        },
                                        "& .MuiSwitch-thumb": {
                                            backgroundColor: "#FFFFFF",
                                            height: "28px",
                                            width: "28px",
                                        },
                                        "& .Mui-disabled": {
                                            opacity: 0.4,
                                        },
                                        "& .Mui-disabled+.MuiSwitch-track": {
                                            opacity: "0.3 !important",
                                        },
                                    }}
                                />
                            }
                            label="Enable Developer Mode"
                            sx={{
                                "& .MuiFormControlLabel-label": {
                                    color: "#FFFFFF !important",
                                    fontSize: "18px",
                                    fontWeight: "400",
                                },
                            }}
                        />
                    </Grid>
                </Grid>

                <Grid
                    container
                    display={user && user.permissions.some((p) => p.permission_name === "TEST_NOTIFICATION_ALERTS") ? "flex" : "none"}
                    paddingBottom="20px"
                    borderBottom={(theme) => theme.palette.custom.borderColor}
                    borderRadius="10px"
                >
                    <Grid
                        size={{
                            md: 5,
                            sm: 12,
                        }}
                    >
                        <Typography component="h6" color="#FFFFFF" fontSize="18px" fontWeight="600">
                            Get Latest Notification Alerts
                        </Typography>
                        <Typography component="p" color="#FFFFFF" fontSize="14px" fontWeight="400">
                            Get the latest notification alerts manually by clicking this button.
                        </Typography>
                    </Grid>
                    <Grid
                        size={{
                            md: "grow",
                            sm: 12,
                        }}
                    >
                        <Button
                            variant="contained"
                            onClick={getLatestNotificationAlerts}
                            disabled={isLoading}
                            sx={{
                                color: "#FFFFFF",
                                fontSize: "16px",
                                padding: "5px 20px",
                                borderRadius: "10px",
                                backgroundColor: isLoading ? theme.palette.grey[500] : theme.palette.primary.blue,
                                "&:hover": {
                                    backgroundColor: isLoading ? theme.palette.grey[500] : theme.palette.primary.dark,
                                },
                            }}
                        >
                            {isLoading ? "Loading..." : "Get Alerts"}
                        </Button>
                    </Grid>
                </Grid>

                {isModified && (
                    <Grid container justifyContent="flex-end" gap="20px">
                        <Button variant="text" sx={{ color: "#FFFFFF", fontSize: "16px", padding: "10px 30px" }} onClick={handleCancel}>
                            Cancel
                        </Button>
                        <Button
                            onClick={handleSubmit}
                            variant="contained"
                            sx={{
                                color: "primary",
                                fontSize: "16px",
                                padding: "10px 30px",
                                borderRadius: "5px",
                            }}
                        >
                            Save
                        </Button>
                    </Grid>
                )}
            </Grid>
            <ConfirmModal
                title={confirmModalDetails.title}
                message={confirmModalDetails.message}
                initialState={isConfirmModalOpen}
                onClose={confirmModalDetails.onCancel}
                onConfirm={confirmModalDetails.onConfirm}
            />
        </Grid>
    );
}
