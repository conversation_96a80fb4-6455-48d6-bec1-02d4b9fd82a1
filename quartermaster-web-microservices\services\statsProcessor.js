const StatsProcessor = require('../models/Statistics');
const db = require('../modules/db');
const { createLoggerWithPath } = require('../modules/winston');
const { getSessionsByCoordinates, getPreviousWeekDateTimeUTC, getPastDaylightDateTimeUTC } = require('../utils/functions');
const { schedule  } = require("node-cron");
const HomePort = require('../models/HomePort');

const logger = createLoggerWithPath('aggregate_statistics')

const stationaryDistance = 15; //in meters

async function generateWeeklyStatistics() {
    try {
        const ts = new Date().getTime();

        logger.info('[generateWeeklyStatistics] invoked')

        const { start: startTimestamp, end: endTimestamp } = getPreviousWeekDateTimeUTC()

        const query = {
            artifacts: {
                timestamp: { $gte: new Date(startTimestamp), $lte: new Date(endTimestamp) },
                vessel_presence: true
            },
            locations: {
                timestamp: { $gte: new Date(startTimestamp), $lte: new Date(endTimestamp) }
            }
        }
        logger.info(query)

        const artifacts = await db.qmai.collection('analysis_results').find(query.artifacts).toArray();
        const location_collections = await Promise.all(((await db.qm.db.listCollections().toArray()).filter(c => c.name.endsWith('_location')).map(c => c.name)).map(async (collection) => ({ unit_id: collection.replace(/_location$/, ''), locations: await db.qm.collection(collection).find(query.locations, { sort: { timestamp: 1 } }).toArray() })))
        logger.info(`time taken to fetch data ${new Date().getTime() - ts}`)

        const statistics = {
            totalArtifactsWithAtleastOneVessel: {
                confidenceAbove40: 0,
                confidenceAbove80: 0
            },
            totalVesselsSuperCategorized: {},
            totalVesselsSubCategorized: {},
            listOfTextsExtracted: [],
            totalVesselsWithCountryFlag: {},
            totalVesselsDetectedbySensors: {},
            totalVesselsByHoursUTC: Object.fromEntries(Array.from({ length: 24 }).map((_, i) => [i, 0])),
            totalVesselsByWeekDayHoursUTC: {},
            totalSensorsDurationAtSea: {},
            totalSensorsOnlineDuration: {},
            totalSmartmastsDistanceTraveled: {}
        }

        statistics.totalArtifactsWithAtleastOneVessel.confidenceAbove40 = artifacts.filter((a, i, self) => a.det_conf > 0.4 && i === self.findIndex((el) => el.image_path === a.image_path)).length;
        statistics.totalArtifactsWithAtleastOneVessel.confidenceAbove80 = artifacts.filter((a, i, self) => a.det_conf > 0.8 && i === self.findIndex((el) => el.image_path === a.image_path)).length;

        statistics.totalVesselsSuperCategorized = artifacts.reduce((obj, a) => {
            if (!a.super_category || a.det_nbbox_area < 0.03) return obj;
            if (!obj[a.super_category]) obj[a.super_category] = 0;
            obj[a.super_category] += 1;
            return obj;
        }, {});

        statistics.totalVesselsSubCategorized = artifacts.reduce((obj, a) => {
            if (!a.category || a.det_nbbox_area < 0.03) return obj;
            if (!obj[a.category]) obj[a.category] = 0;
            obj[a.category] += 1;
            return obj;
        }, {});

        statistics.listOfTextsExtracted = artifacts.filter(a => a.text_extraction && a.text_extraction.length > 0).map(a => a.text_extraction.map(o => o.text)).flat().filter((t, i, self) => i === self.findIndex((el) => el === t))

        statistics.totalVesselsWithCountryFlag = artifacts.reduce((obj, a) => {
            if (!a.country_flag) return obj;
            if (!obj[a.country_flag]) obj[a.country_flag] = 0;
            obj[a.country_flag] += 1;
            return obj;
        }, {});

        statistics.totalVesselsDetectedbySensors = artifacts.reduce((obj, a) => {
            if (!obj[a.unit_id]) obj[a.unit_id] = 0;
            obj[a.unit_id] += 1;
            return obj;
        }, {});

        artifacts.forEach((a) => {
            statistics.totalVesselsByHoursUTC[new Date(a.timestamp).getUTCHours()] += 1;
            const dateHour = new Date(new Date(a.timestamp).setMinutes(0, 0, 0)).toISOString();
            if (!statistics.totalVesselsByWeekDayHoursUTC[dateHour]) statistics.totalVesselsByWeekDayHoursUTC[dateHour] = 0;
            statistics.totalVesselsByWeekDayHoursUTC[dateHour] += 1;
            // statistics.totalVesselsByWeekDayHoursUTC[new Date(a.timestamp).toISOString().split('T')[0]][new Date(a.timestamp).getUTCHours()] += 1;
        });

        const LatLonSpherical = (await import('geodesy/latlon-spherical.js')).default;
        const homePorts = await HomePort.find()

        location_collections.forEach(unit_location => {
            const unit_id = unit_location.unit_id;
            const seaLocations = unit_location.locations.filter(loc => {
                if (homePorts.some(c => new LatLonSpherical(c.lat, c.lng).distanceTo(new LatLonSpherical(loc.latitude, loc.longitude)) < 1000))
                    return false
                else
                    return true
            })
            const sessions = getSessionsByCoordinates(seaLocations);
            if (sessions.length === 0) {
                statistics.totalSensorsDurationAtSea[unit_id] = 0;
            } else {
                const onlineDuration = sessions.reduce((sum, session) => sum += new Date(session[session.length - 1].timestamp).getTime() - new Date(session[0].timestamp).getTime(), 0);
                statistics.totalSensorsDurationAtSea[unit_id] = onlineDuration;
            }
        })

        location_collections.forEach(unit_location => {
            const unit_id = unit_location.unit_id;
            const sessions = getSessionsByCoordinates(unit_location.locations);
            if (sessions.length === 0) {
                statistics.totalSensorsOnlineDuration[unit_id] = 0;
            } else {
                const onlineDuration = sessions.reduce((sum, session) => sum += new Date(session[session.length - 1].timestamp).getTime() - new Date(session[0].timestamp).getTime(), 0);
                statistics.totalSensorsOnlineDuration[unit_id] = onlineDuration;
            }
        })

        location_collections.forEach(unit_location => {
            const unit_id = unit_location.unit_id;
            const locations = unit_location.locations

            /* excluding the homeports approach */
            // const seaLocations = unit_location.locations.filter(currLoc => {
            //     const nextLoc = unit_location.locations[i + 1]
            //     if ()
            //         if (seaPorts.some(c => new LatLonSpherical(c.lat, c.lng).distanceTo(new LatLonSpherical(loc.latitude, loc.longitude)) < 1000))
            //             return false
            //         else
            //             return true
            // })

            /* excluding stationary coordinates approach */
            // const locations = unit_location.locations.filter((currentCoordinate, i) => {
            //     const previousCoordinate = unit_location.locations[i - 1]
            //     const nextCoordinate = unit_location.locations[i + 1]
            //     const previousDistance = previousCoordinate && new LatLonSpherical(currentCoordinate.latitude, currentCoordinate.longitude).distanceTo(new LatLonSpherical(previousCoordinate.latitude, previousCoordinate.longitude));
            //     const nextDistance = nextCoordinate && new LatLonSpherical(currentCoordinate.latitude, currentCoordinate.longitude).distanceTo(new LatLonSpherical(nextCoordinate.latitude, nextCoordinate.longitude));
            //     if ((!previousCoordinate || previousDistance <= stationaryDistance) && nextDistance >= stationaryDistance) return true;
            //     if (previousDistance >= stationaryDistance && nextDistance >= stationaryDistance) return true;
            //     if (previousDistance >= stationaryDistance && (!nextCoordinate || nextDistance <= stationaryDistance)) return true;
            //     return false
            // })

            if (locations.length < 2) {
                statistics.totalSmartmastsDistanceTraveled[unit_id] = 0
            } else {
                var distanceSum = 0;
                locations.forEach((loc, i) => {
                    const nextLoc = locations[i + 1];
                    if (nextLoc) {
                        const distance = new LatLonSpherical(loc.latitude, loc.longitude).distanceTo(new LatLonSpherical(nextLoc.latitude, nextLoc.longitude));
                        if (distance >= stationaryDistance) distanceSum += distance;
                    }
                })
                statistics.totalSmartmastsDistanceTraveled[unit_id] = distanceSum;
            }
        })

        logger.info(`statistics are ${JSON.stringify(statistics)}`)

        await StatsProcessor.create({
            fromTimestamp: startTimestamp,
            toTimestamp: endTimestamp,
            type: 'weekly',
            stats: statistics
        })

        logger.info('[generateWeeklyStatistics] saved to db')
    } catch (err) {
        logger.error(`[generateWeeklyStatistics] error: ${err.message}`)
        console.error('[generateWeeklyStatistics] error:', err)
    }
}

async function generateDailyStatistics() {
    try {
        const ts = new Date().getTime();

        logger.info('[generateDailyStatistics] invoked')

        const { start: startTimestamp, end: endTimestamp } = getPastDaylightDateTimeUTC()

        const query = {
            artifacts: {
                timestamp: { $gte: new Date(startTimestamp), $lte: new Date(endTimestamp) },
                vessel_presence: true
            },
            locations: {
                timestamp: { $gte: new Date(startTimestamp), $lte: new Date(endTimestamp) }
            }
        }
        logger.info(query)

        const artifacts = await db.qmai.collection('analysis_results').find(query.artifacts).toArray();
        const location_collections = await Promise.all(((await db.qm.db.listCollections().toArray()).filter(c => c.name.endsWith('_location')).map(c => c.name)).map(async (collection) => ({ unit_id: collection.replace(/_location$/, ''), locations: await db.qm.collection(collection).find(query.locations, { sort: { timestamp: 1 } }).toArray() })))
        logger.info(`time taken to fetch records ${new Date().getTime() - ts}`)
        logger.info(`total artifacts in db within time range ${artifacts.length}`)

        const statistics = {
            totalVesselsDetected: 0,
            totalVesselsDetectedbySensors: {},
            totalVesselsSuperCategorized: {},
            totalVesselsSubCategorized: {},
            totalSmartmastsAtSea: 0,
            totalSmartmastsOnline: 0
        }

        statistics.totalVesselsDetected = artifacts.length;

        statistics.totalVesselsDetectedbySensors = artifacts.reduce((obj, a) => {
            if (!obj[a.unit_id]) obj[a.unit_id] = 0;
            obj[a.unit_id] += 1;
            return obj;
        }, {});

        statistics.totalVesselsSuperCategorized = artifacts.reduce((obj, a) => {
            if (!a.super_category || a.det_nbbox_area < 0.03) return obj;
            if (!obj[a.super_category]) obj[a.super_category] = 0;
            obj[a.super_category] += 1;
            return obj;
        }, {});

        statistics.totalVesselsSubCategorized = artifacts.reduce((obj, a) => {
            if (!a.category || a.det_nbbox_area < 0.03) return obj;
            if (!obj[a.category]) obj[a.category] = 0;
            obj[a.category] += 1;
            return obj;
        }, {});

        const LatLonSpherical = (await import('geodesy/latlon-spherical.js')).default;
        const homePorts = await HomePort.find()

        location_collections.forEach(unit_location => {
            const seaLocations = unit_location.locations.filter(loc => {
                if (homePorts.some(c => new LatLonSpherical(c.lat, c.lng).distanceTo(new LatLonSpherical(loc.latitude, loc.longitude)) < 1000))
                    return false
                else
                    return true
            })

            if (seaLocations.length >= 15) {
                statistics.totalSmartmastsAtSea += 1;
            }
        })

        location_collections.forEach(unit_location => {
            if (unit_location.locations.length >= 15) {
                statistics.totalSmartmastsOnline += 1;
            }
        })

        logger.info(`statistics are ${JSON.stringify(statistics)}`)

        await StatsProcessor.create({
            fromTimestamp: startTimestamp,
            toTimestamp: endTimestamp,
            type: 'daily',
            stats: statistics
        })

        logger.info('[generateDailyStatistics] saved to db')
    } catch (err) {
        logger.error(`[generateDailyStatistics] error: ${err.message}`)
        console.error('[generateDailyStatistics] error:', err)
    }
}

logger.info('setting init timers for statistics computation')

schedule('1 0 12 * * *', generateDailyStatistics, {scheduled: true, timezone: 'UTC'}) // at 12:00:01 each day
schedule('1 0 16 * * Sunday', generateWeeklyStatistics, {scheduled: true, timezone: 'UTC'}) // at 16:00:01 each Sunday
