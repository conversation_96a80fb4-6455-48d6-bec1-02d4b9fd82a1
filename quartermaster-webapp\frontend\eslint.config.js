import globals from "globals";
import pluginJs from "@eslint/js";
import pluginReact from "eslint-plugin-react";
import pluginPrettier from "eslint-plugin-prettier"; // Import the Prettier plugin
import unusedImports from "eslint-plugin-unused-imports"; // Import unused-imports plugin
import pluginReactHooks from "eslint-plugin-react-hooks"; // Import React Hooks plugin

/** @type {import('eslint').Linter.Config[]} */
export default [
    { files: ["**/*.{js,mjs,cjs,jsx}"] },
    { languageOptions: { globals: globals.browser } },
    pluginJs.configs.recommended,
    pluginReact.configs.flat.recommended,
    {
        plugins: {
            prettier: pluginPrettier, // Add the Prettier plugin
            "unused-imports": unusedImports, // Add unused-imports plugin
            "react-hooks": pluginReactHooks, // Add React Hooks plugin
        },
        rules: {
            ...pluginReactHooks.configs.recommended.rules,
            "react/react-in-jsx-scope": "off", // Disable the rule
            "react/prop-types": "off", // Disable the rule
            "unused-imports/no-unused-imports": "error", // Add rule for unused imports
            "no-unused-vars": "error", // Add rule for unused variables
            "prettier/prettier": [
                "error",
                {
                    endOfLine: "auto",
                    semi: true,
                    trailingComma: "all",
                    arrowParens: "always",
                    printWidth: 150,
                    tabWidth: 4,
                },
            ],
        },
    },
    {
        ignores: ["**/tests/**", "vite.config.js", "jest.config.js"], // Use *.config.js to ignore all config files
    },
];
