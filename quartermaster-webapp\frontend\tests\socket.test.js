import { getSocket, reconnectSocket, disconnectSocket } from '../src/socket';
import { io } from 'socket.io-client';
// import { browserName, osName, osVersion } from 'react-device-detect';

jest.mock('socket.io-client', () => ({
    io: jest.fn(() => ({
        on: jest.fn(),
        emit: jest.fn(),
        connect: jest.fn(),
        disconnect: jest.fn(),
    })),
}));

jest.mock('react-device-detect', () => ({
    browserName: 'MockBrowser',
    osName: 'MockOS',
    osVersion: '1.0',
}));

jest.mock('../environment', () => ({
    environment: {
        VITE_API_URL: 'https://api.quartermaster.us'
    }
}));

describe('Socket management functions', () => {
    let mockSocket;

    beforeEach(() => {
        jest.clearAllMocks();
        mockSocket = io();
    });

    it('should create and return a new socket instance', () => {
        const socket = getSocket();
        expect(io).toHaveBeenCalled();
        expect(socket).toBeDefined();
    });

    it('should return the same socket instance on repeated calls to getSocket', () => {
        const firstSocket = getSocket();
        const secondSocket = getSocket();
        expect(firstSocket).toBe(secondSocket);
    });

    it('should disconnect the current socket and create a new one on reconnectSocket', () => {
        const firstSocket = getSocket();
        reconnectSocket();
        const secondSocket = getSocket();
        expect(firstSocket.disconnect).toHaveBeenCalled();
        expect(firstSocket).not.toBe(secondSocket);
    });

    it('should disconnect and nullify the socket on disconnectSocket', () => {
        const firstSocket = getSocket();
        disconnectSocket();
        const secondSocket = getSocket();
        expect(firstSocket.disconnect).toHaveBeenCalled();
        expect(firstSocket).not.toBe(secondSocket);
    });

    it('should disconnect and nullify the socket if socket is disconnected and set to null', () => {
        const firstSocket = getSocket();
        disconnectSocket();
        disconnectSocket();
        reconnectSocket();
        const secondSocket = getSocket();
        expect(firstSocket.disconnect).toHaveBeenCalled();
        expect(firstSocket).not.toBe(secondSocket);
    });
});
