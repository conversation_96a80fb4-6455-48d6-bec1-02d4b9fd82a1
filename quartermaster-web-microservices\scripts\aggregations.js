const calculateDistanceBetweenVesselLocationsAggregationPipeline = [
  {
    $sort: {
      timestamp: 1
    }
  },
  {
    $setWindowFields: {
      sortBy: {
        timestamp: 1
      },
      output: {
        prevDoc: {
          $shift: {
            output: {
              latitude: "$latitude",
              longitude: "$longitude"
            },
            by: -1
          }
        }
      }
    }
  },
  {
    $addFields: {
      distance: {
        $cond: [
          {
            $and: [
              "$prevDoc.latitude",
              "$prevDoc.longitude"
            ]
          },
          {
            $let: {
              vars: {
                r: 6371000,
                dLat: {
                  $degreesToRadians: {
                    $subtract: [
                      "$latitude",
                      "$prevDoc.latitude"
                    ]
                  }
                },
                dLon: {
                  $degreesToRadians: {
                    $subtract: [
                      "$longitude",
                      "$prevDoc.longitude"
                    ]
                  }
                },
                lat1: {
                  $degreesToRadians:
                    "$prevDoc.latitude"
                },
                lat2: {
                  $degreesToRadians: "$latitude"
                }
              },
              in: {
                $let: {
                  vars: {
                    a: {
                      $add: [
                        {
                          $pow: [
                            {
                              $sin: {
                                $divide: [
                                  "$$dLat",
                                  2
                                ]
                              }
                            },
                            2
                          ]
                        },
                        {
                          $multiply: [
                            {
                              $cos: "$$lat1"
                            },
                            {
                              $cos: "$$lat2"
                            },
                            {
                              $pow: [
                                {
                                  $sin: {
                                    $divide: [
                                      "$$dLon",
                                      2
                                    ]
                                  }
                                },
                                2
                              ]
                            }
                          ]
                        }
                      ]
                    }
                  },
                  in: {
                    $multiply: [
                      2,
                      "$$r",
                      {
                        $asin: {
                          $sqrt: "$$a"
                        }
                      }
                    ]
                  }
                }
              }
            }
          },
          null
        ]
      }
    }
  },
  {
    $project: {
      prevDoc: 0
    }
  }
]