import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import Login from '../../src/pages/Login/Login';
import { useUser } from '../../src/hooks/UserHook';
import { BrowserRouter as Router } from 'react-router-dom';
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';
import { useNavigate } from 'react-router-dom';

jest.mock('../../src/hooks/UserHook', () => ({
    useUser: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: jest.fn(),
}));

describe('Login Component', () => {
    const mockLogin = jest.fn();
    const mockNavigate = jest.fn();
    const mockFetchUser = jest.fn().mockResolvedValue({});

    beforeEach(() => {
        useNavigate.mockReturnValue(mockNavigate);
    });

    afterEach(() => {
        jest.restoreAllMocks();
    });

    it('should render login page elements correctly', () => {
        useUser.mockReturnValue({
            user: null,
            login: mockLogin,
            fetchUser: mockFetchUser,
        });

        render(
            <Router>
                <ThemeProvider theme={theme}>
                    <Login />
                </ThemeProvider>
            </Router>
        );

        expect(screen.getByPlaceholderText('Email or Username')).toBeInTheDocument();
        expect(screen.getByPlaceholderText('Password')).toBeInTheDocument();
        expect(screen.getByText('Sign in')).toBeInTheDocument();
        expect(screen.getByText('Forgot Password?')).toBeInTheDocument();
    });

    it('should call login function when form is submitted', async () => {
        useUser.mockReturnValue({
            user: null,
            login: mockLogin,
            fetchUser: mockFetchUser,
        });

        render(
            <Router>
                <ThemeProvider theme={theme}>
                    <Login />
                </ThemeProvider>
            </Router>
        );

        const usernameInput = screen.getByPlaceholderText('Email or Username');
        const passwordInput = screen.getByPlaceholderText('Password');
        const submitButton = screen.getByText('Sign in');

        fireEvent.change(usernameInput, { target: { value: 'testuser' } });
        fireEvent.change(passwordInput, { target: { value: 'password123' } });
        fireEvent.click(submitButton);

        await waitFor(() => expect(mockLogin).toHaveBeenCalledWith({
            username: 'testuser',
            password: 'password123'
        }));
    });

    it('should navigate to OTP page if login returns status 302', async () => {
        useUser.mockReturnValue({
            user: null,
            login: jest.fn().mockRejectedValue({
                response: { status: 302 },
            }),
            fetchUser: mockFetchUser,
        });

        render(
            <Router>
                <ThemeProvider theme={theme}>
                    <Login />
                </ThemeProvider>
            </Router>
        );

        const usernameInput = screen.getByPlaceholderText('Email or Username');
        const passwordInput = screen.getByPlaceholderText('Password');
        const submitButton = screen.getByText('Sign in');

        fireEvent.change(usernameInput, { target: { value: 'testuser' } });
        fireEvent.change(passwordInput, { target: { value: 'password123' } });
        fireEvent.click(submitButton);

        await waitFor(() => expect(screen.getByText('Sign in')).toBeDisabled());
    });

    it('should display error message if login fails with response.data.message', async () => {
        useUser.mockReturnValue({
            user: null,
            login: jest.fn().mockRejectedValue({
                message: 'Invalid credentials'
            }),
            fetchUser: mockFetchUser,
        });

        render(
            <Router>
                <ThemeProvider theme={theme}>
                    <Login />
                </ThemeProvider>
            </Router>
        );

        const usernameInput = screen.getByPlaceholderText('Email or Username');
        const passwordInput = screen.getByPlaceholderText('Password');
        const submitButton = screen.getByText('Sign in');

        fireEvent.change(usernameInput, { target: { value: 'testuser' } });
        fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
        fireEvent.click(submitButton);

        await waitFor(() => expect(screen.getByText('Login failed: Invalid credentials')).toBeInTheDocument());
    });

    it('should display error message if login fails with JSON.stringify', async () => {
        useUser.mockReturnValue({
            user: null,
            login: jest.fn().mockRejectedValue({
                error: {
                    message: 'Invalid credentials'
                }
            }),
            fetchUser: mockFetchUser,
        });

        render(
            <Router>
                <ThemeProvider theme={theme}>
                    <Login />
                </ThemeProvider>
            </Router>
        );

        const usernameInput = screen.getByPlaceholderText('Email or Username');
        const passwordInput = screen.getByPlaceholderText('Password');
        const submitButton = screen.getByText('Sign in');

        fireEvent.change(usernameInput, { target: { value: 'testuser' } });
        fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
        fireEvent.click(submitButton);

        await waitFor(() =>
            expect(screen.queryByText('Login failed: {"message":"Invalid credentials"}')).not.toBeInTheDocument()
        );
    });

    it('should display error message if login fails and clear it after 3000ms', async () => {
        jest.useFakeTimers();

        useUser.mockReturnValue({
            user: null,
            login: jest.fn().mockRejectedValue({
                response: { data: { message: 'Invalid credentials' } },
            }),
            fetchUser: mockFetchUser,
        });

        render(
            <Router>
                <ThemeProvider theme={theme}>
                    <Login />
                </ThemeProvider>
            </Router>
        );

        const usernameInput = screen.getByPlaceholderText('Email or Username');
        const passwordInput = screen.getByPlaceholderText('Password');
        const submitButton = screen.getByText('Sign in');

        fireEvent.change(usernameInput, { target: { value: 'testuser' } });
        fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
        fireEvent.click(submitButton);

        await waitFor(() =>
            expect(screen.getByText('Login failed: Invalid credentials')).toBeInTheDocument()
        );

        jest.advanceTimersByTime(3000);

        await waitFor(() =>
            expect(screen.queryByText('Login failed: Invalid credentials')).not.toBeInTheDocument()
        );

        jest.runOnlyPendingTimers();
        jest.useRealTimers()
    });


    it('should toggle password visibility when icon is clicked', () => {
        useUser.mockReturnValue({
            user: null,
            login: mockLogin,
            fetchUser: mockFetchUser,
        });

        render(
            <Router>
                <ThemeProvider theme={theme}>
                    <Login />
                </ThemeProvider>
            </Router>
        );

        const passwordInput = screen.getByPlaceholderText('Password');
        const visibilityIcon = screen.getByTestId('toggle-visibility-button');

        expect(passwordInput.type).toBe('password');
        fireEvent.click(visibilityIcon);
        expect(passwordInput.type).toBe('text');
        fireEvent.click(visibilityIcon);
        expect(passwordInput.type).toBe('password');
    });


    it('should navigate to stream if user is true', () => {
        useUser.mockReturnValue({
            user: true,
            login: mockLogin,
            fetchUser: mockFetchUser,
        });

        render(
            <Router>
                <ThemeProvider theme={theme}>
                    <Login />
                </ThemeProvider>
            </Router>
        );

        expect(mockNavigate).toHaveBeenCalled();
    });
});
