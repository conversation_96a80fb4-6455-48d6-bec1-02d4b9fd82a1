import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import AddRoleModal from "../../src/pages/Dashboard/User/Roles/AddRoleModal";
import axiosInstance from "../../src/axios";

jest.mock("../../src/axios", () => ({
    post: jest.fn()
}));

describe("AddRoleModal Component", () => {
    const mockProps = {
        showAddRole: true,
        setShowAddRole: jest.fn(),
        setAdding: jest.fn(),
        onSuccess: jest.fn()
    };

    beforeEach(() => {
        axiosInstance.post.mockResolvedValue({ data: {} });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("does not render when `showAddRole` is false", () => {
        render(
            <AddRoleModal
                showAddRole={false}
                setShowAddRole={mockProps.setShowAddRole}
                setAdding={mockProps.setAdding}
                onSuccess={mockProps.onSuccess}
            />
        );

        expect(screen.queryByText("Add Role")).not.toBeInTheDocument();
    });

    it("disables the Submit button when `roleName` is empty", () => {
        render(
            <AddRoleModal
                showAddRole={true}
                setShowAddRole={mockProps.setShowAddRole}
                setAdding={mockProps.setAdding}
                onSuccess={mockProps.onSuccess}
            />
        );

        const submitButton = screen.getByText("Submit");
        expect(submitButton).toBeDisabled();
    });

    it("enables Submit button when roleName is entered", () => {
        render(<AddRoleModal {...mockProps} />);

        const input = screen.getByLabelText("Name");
        fireEvent.change(input, { target: { value: "New Role" } });

        const submitButton = screen.getByText("Submit");
        expect(submitButton).not.toBeDisabled();
    });

    it("handles form submission correctly", async () => {
        render(<AddRoleModal {...mockProps} />);

        const input = screen.getByLabelText("Name");
        fireEvent.change(input, { target: { value: "New Role" } });

        const submitButton = screen.getByText("Submit");
        fireEvent.click(submitButton);

        expect(axiosInstance.post).toHaveBeenCalledWith(
            '/roles',
            { role_name: "New Role" },
            { meta: { showSnackbar: true } }
        );

        await waitFor(() => {
            expect(mockProps.setShowAddRole).toHaveBeenCalledWith(false);
            expect(mockProps.setAdding).toHaveBeenCalledWith(false);
            expect(mockProps.onSuccess).toHaveBeenCalled();
        });
    });

    it("handles API error gracefully", async () => {
        const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();
        axiosInstance.post.mockRejectedValueOnce(new Error("API Error"));

        render(<AddRoleModal {...mockProps} />);

        const input = screen.getByLabelText("Name");
        fireEvent.change(input, { target: { value: "New Role" } });
        const submitButton = screen.getByText("Submit");
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(mockProps.setAdding).toHaveBeenCalledWith(false);
            expect(consoleErrorSpy).toHaveBeenCalled();
        });

        consoleErrorSpy.mockRestore();
    });
});
