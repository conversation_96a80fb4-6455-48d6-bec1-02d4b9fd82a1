const axios = require('axios');
const LogSeaVision = require('../models/logSeaVision');

const aisApi = axios.create({
    baseURL: process.env.SEAVISION_BASE_URL_AIS_PROD,
    headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.SEAVISION_AIS_TOKEN
    }
});

const rfApi = axios.create({
    baseURL: process.env.SEAVISION_BASE_URL_PROD,
    headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.SEAVISION_RF_TOKEN
    }
});

const cameraApi = axios.create({
    baseURL: process.env.SEAVISION_BASE_URL_PROD,
    headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.SEAVISION_CAMERA_TOKEN
    }
});

class SeaVisionApi {
    // artifactsData is an array of objects with the following fields:
    // {
    //     trackingCode, // string, can be object id of the artifact
    //     source, // string, must be "RF"
    //     lat, // float number
    //     lon, // float number
    //     time, // ISO string
    //     mmsi, // imo number of vessel if detected, must be a number
    //     attributes // additional json metadata
    // }
    async PostRFDetection(artifactsData, disableInternalLogs = false) {
        const data = []

        artifactsData.forEach(artifact => {
            if (!artifact.trackingCode || !artifact.source || !artifact.lat || !artifact.lon || !artifact.time) throw new Error('artifact is missing required fields');

            const obj = { trackingCode: artifact.trackingCode, source: artifact.source, lat: artifact.lat, lon: artifact.lon, time: artifact.time }
            if (artifact.mmsi) obj.mmsi = artifact.mmsi;
            if (artifact.attributes && typeof artifact.attributes === 'object' && Object.keys(artifact.attributes).length > 0) obj.attributes = artifact.attributes;

            data.push(obj)
        })

        if (!disableInternalLogs) console.log('[PostRFDetection] Sending data to SeaVision API', data);

        // throw new Error('request is disabled');

        const res = await rfApi.post(`/posits`, data).then(async res => {
            await LogSeaVision.create({
                submitted_data: artifactsData,
                response_data: { status: res.status, statusText: res.statusText, data: res.data },
                success: true,
                endpoint: 'PostRFDetection'
            })

            return res;
        }).catch(async err => {
            await LogSeaVision.create({
                submitted_data: artifactsData,
                response_data: { status: err.response.status, statusText: err.response.statusText, data: err.response.data },
                success: false,
                endpoint: 'PostRFDetection'
            })

            throw err;
        });

        return { status: res.status, statusText: res.statusText, data: res.data };
    }

    async PostCameraDetection({
        lat, // float number
        lon, // float number
        time, // ISO string
        file, // base64 encoded image
        mmsi, // imo number of vessel if detected, must be a number
        platform, // provide the unit_id of the smartmast
        attributes // additional json metadata
    }, disableInternalLogs = false) {
        const data = { lat, lon, time, file, platform }
        if (mmsi) data.mmsi = mmsi;
        if (attributes && typeof attributes === 'object' && Object.keys(attributes).length > 0) data.attributes = attributes;
        data.name = `${platform}_${time}`;

        if (!disableInternalLogs) console.log('[PostCameraDetection] Sending data to SeaVision API', data);

        // throw new Error('request is disabled');

        const res = await cameraApi.post(`/posits/camera`, data).then(async res => {
            const submittedData = data;
            delete submittedData.file;

            await LogSeaVision.create({
                submitted_data: submittedData,
                response_data: { status: res.status, statusText: res.statusText, data: res.data },
                success: true,
                endpoint: 'PostCameraDetection'
            })

            return res;
        }).catch(async err => {
            const submittedData = data;
            delete submittedData.file;

            await LogSeaVision.create({
                submitted_data: submittedData,
                response_data: { status: err.response.status, statusText: err.response.statusText, data: err.response.data },
                success: false,
                endpoint: 'PostCameraDetection'
            })

            throw err;
        });

        return { status: res.status, statusText: res.statusText, data: res.data };
    }
}

const seaVisionApi = new SeaVisionApi();

module.exports = seaVisionApi;