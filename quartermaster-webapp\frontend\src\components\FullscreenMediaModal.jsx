import React from "react";
import { Modal, Box, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import HelperIcons from "./HealperIcons.jsx";
import DetailVideoPlayer from "./DetailVideoPlayer.jsx";

const FullscreenMediaModal = ({
    isLoading,
    isFavourite,
    removeFavourite,
    addFavourite,
    toggleShare,
    downloadArtifact,
    open,
    onClose,
    mediaUrl,
    isImage,
    handleCurrentTimeChange = () => {},
    currentTime = 0,
}) => {
    return (
        <Modal open={open} onClose={onClose} aria-labelledby="fullscreen-media" aria-describedby="fullscreen-media-description">
            <Box
                sx={{
                    position: "absolute",
                    top: "50%",
                    left: "50%",
                    transform: "translate(-50%, -50%)",
                    width: "90%",
                    height: "90%",
                    bgcolor: "#000",
                    boxShadow: 24,
                    p: 0,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                }}
            >
                <IconButton
                    onClick={onClose}
                    sx={{
                        position: "absolute",
                        top: 8,
                        right: 8,
                        zIndex: 1,
                        color: "white",
                        backgroundColor: "rgba(0, 0, 0, 0.5)",
                        transition: "all 0.3s",
                        border: "1px solid #FFFFFF",
                        "&:hover": {
                            backgroundColor: "#FFFFFF",
                            color: "#000",
                            border: "1px solid #000",
                        },
                    }}
                >
                    <CloseIcon />
                </IconButton>
                {isImage ? (
                    <img src={mediaUrl} alt="Fullscreen" style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain" }} />
                ) : (
                    <Box sx={{ position: "relative", width: "100%", height: "100%", backgroundColor: "#000" }}>
                        {isLoading !== undefined && !isLoading && (
                            <HelperIcons
                                isFavourite={isFavourite}
                                removeFavourite={removeFavourite}
                                addFavourite={addFavourite}
                                toggleShare={toggleShare}
                                downloadArtifact={downloadArtifact}
                                containerStyle={{
                                    top: 80,
                                    right: 15,
                                }}
                            />
                        )}
                        <DetailVideoPlayer
                            src={mediaUrl}
                            style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain", height: "100%", width: "100%" }}
                            onLoadedData={() => () => {}}
                            onCurrentTimeChange={handleCurrentTimeChange}
                            currentTime={currentTime}
                            fullscreenOpen={open}
                            isInFullScreen={true}
                        />
                        {/* <video
                            src={mediaUrl}
                            controls
                            autoPlay
                            style={{ maxWidth: "100%", maxHeight: "100%", objectFit: "contain", height: "100%", width: "100%" }}
                        /> */}
                    </Box>
                )}
            </Box>
        </Modal>
    );
};

export default FullscreenMediaModal;
