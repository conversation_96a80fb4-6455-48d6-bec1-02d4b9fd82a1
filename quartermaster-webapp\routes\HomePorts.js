const express = require("express");
const { validateError } = require("../utils/functions");
const isAuthenticated = require("../middlewares/auth");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const HomePort = require("../models/HomePort");
const router = express.Router();
const compression = require("compression");
const { default: rateLimit } = require("express-rate-limit");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);
router.use(compression());

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ALL_HOME_PORTS), isAuthenticated, async (req, res) => {
    try {
        const homePorts = await HomePort.aggregate([
            {
                $project: {
                    _id: 1,
                    lat: 1,
                    lng: 1,
                },
            },
        ]);
        res.status(200).json(homePorts);
    } catch (err) {
        validateError(err, res);
    }
});

module.exports = router;
