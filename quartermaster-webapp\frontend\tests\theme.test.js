import theme from '../src/theme';
import { createTheme } from '@mui/material/styles';

describe('theme.js', () => {
    it('should have the correct color palette', () => {
        expect(theme.palette.primary.main).toBe('#1E293B');
        expect(theme.palette.custom.live).toBe('#00CC1F');
        expect(theme.palette.custom.offline).toBe('#7090B0');
        expect(theme.palette.custom.replay).toBe('#1565c0');
        expect(theme.palette.custom.unfocused).toBe('#545454');
    });

    it('should set the correct breakpoints', () => {
        expect(theme.breakpoints.values.xs).toBe(0);
        expect(theme.breakpoints.values.sm).toBe(600);
        expect(theme.breakpoints.values.md).toBe(900);
        expect(theme.breakpoints.values.lg).toBe(1200);
        expect(theme.breakpoints.values.xl).toBe(1536);
        expect(theme.breakpoints.values['2xl']).toBe(1900);
    });

    it('should have the correct typography settings', () => {
        expect(theme.typography.fontFamily).toBe('"Outfit", sans-serif');
        expect(theme.typography.fontWeightLight).toBe(300);
        expect(theme.typography.fontWeightRegular).toBe(700);
        expect(theme.typography.fontWeightMedium).toBe(500);
    });

    it('should have responsive font sizes enabled', () => {
        const responsiveTheme = createTheme(theme);
        expect(responsiveTheme.typography.h1).toHaveProperty('fontSize');
        expect(responsiveTheme.typography.h2).toHaveProperty('fontSize');
    });

    it('should have correct MuiButton styles', () => {
        const buttonStyles = theme.components.MuiButton.styleOverrides.root;
        expect(buttonStyles.borderRadius).toBe('10px');
        expect(buttonStyles[':disabled']).toHaveProperty('pointerEvents', 'auto');
        expect(buttonStyles[':disabled']).toHaveProperty('cursor', 'not-allowed');
    });

    it('should have the correct MuiTextField styles', () => {
        const textFieldStyles = theme.components.MuiTextField.styleOverrides.root;
        expect(textFieldStyles['& .MuiFilledInput-root']).toHaveProperty('color', '#FFFFFF');
        expect(textFieldStyles['& fieldset']).toHaveProperty('border', 'none');
    });

    it('should apply correct styles to MuiDateTimePicker', () => {
        const dateTimePickerProps = theme.components.MuiDateTimePicker.defaultProps;
        expect(dateTimePickerProps.format).toBe('DD-MMM-YYYY HH:mm');
    });

    it('should set the correct scrollbar styles', () => {
        const scrollbarStyles = theme.components.MuiCssBaseline.styleOverrides;
        expect(scrollbarStyles['::-webkit-scrollbar']).toHaveProperty('width', '10px');
        expect(scrollbarStyles['::-webkit-scrollbar-thumb']).toHaveProperty('backgroundColor', '#BDBDBD');
    });
});
