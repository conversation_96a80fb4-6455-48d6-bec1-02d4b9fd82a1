import { <PERSON>ton, Grid, <PERSON>dal, Typography, FormControl, RadioGroup, FormControlLabel, Radio, alpha } from "@mui/material";
import { useState } from "react";
import ModalContainer from "../../../components/ModalContainer";
import theme from "../../../theme";
import dayjs from "dayjs";

const FilterLogModal = ({ showFilterModal, setShowFilterModal, setFilters }) => {
    const [statusFilter, setStatusFilter] = useState("all_users");
    const [timeFilter, setTimeFilter] = useState("all_time");
    const [changed, setChanged] = useState({
        statusChanged: false,
        timeChanged: false,
        oldTime: "",
        oldStatus: "",
    });

    const handleChange = () => {
        let created_after;

        switch (timeFilter) {
            case "today": {
                created_after = dayjs().startOf("day").valueOf();
                break;
            }
            case "last_3_days": {
                created_after = dayjs().subtract(3, "days").valueOf();
                break;
            }
            case "last_week": {
                created_after = dayjs().subtract(7, "days").valueOf();
                break;
            }
            case "last_month": {
                created_after = dayjs().subtract(30, "days").valueOf();
                break;
            }
            case "last_year": {
                created_after = dayjs().subtract(365, "days").valueOf();
                break;
            }
            default:
                created_after = null;
        }

        setFilters((old) => ({
            ...old,
            status: statusFilter,
            created_after,
        }));
    };

    const handleClose = () => {
        if (changed.statusChanged) {
            setStatusFilter(changed.oldStatus);
            setChanged({ ...changed, statusChanged: false, oldStatus: "" });
        }
        if (changed.timeChanged) {
            setTimeFilter(changed.oldTime);
            setChanged({ ...changed, timeChanged: false, oldTime: "" });
        }

        setShowFilterModal(false);
    };

    const handleClear = () => {
        setStatusFilter("all_users");
        setTimeFilter("all_time");
        setChanged({ statusChanged: false, timeChanged: false, oldTime: "", oldStatus: "" });

        setFilters((old) => ({
            ...old,
            status: null,
            created_after: null,
        }));
    };

    const handleSubmit = () => {
        handleChange();
        setShowFilterModal(false);
    };

    return (
        <Modal open={Boolean(showFilterModal)} onClose={handleClose}>
            <ModalContainer title={"Filter"} onClose={handleClose} showDivider={true}>
                <Grid
                    container
                    flexDirection={"column"}
                    gap={2}
                    width={{ xs: 300, sm: 500 }}
                    maxHeight={"70vh"}
                    overflow={"auto"}
                    flexWrap={"nowrap"}
                >
                    <Grid>
                        <Typography variant="h6" fontSize={"16px !important"} marginBottom={2} fontWeight={500}>
                            Status
                        </Typography>
                        <Grid container border={`1px solid ${theme.palette.custom.borderColor}`} borderRadius={"8px"} padding={"3px"}>
                            {["online", "offline", "all_users"].map((value) => (
                                <Grid key={value} size={4}>
                                    <Button
                                        onClick={() => {
                                            setChanged({ ...changed, statusChanged: true, oldStatus: statusFilter });
                                            setStatusFilter(value);
                                        }}
                                        sx={{
                                            width: "100%",
                                            borderRadius: "8px",
                                            color: statusFilter === value ? "#FFFFFF" : "#737791",
                                            outline: "none !important",
                                            textTransform: "capitalize",
                                            backgroundColor: "transparent",
                                            border: statusFilter === value ? `1px solid ${theme.palette.custom.borderColor}` : "none",
                                            "&:hover": {
                                                backgroundColor: "transparent",
                                            },
                                        }}
                                    >
                                        {value.charAt(0).toUpperCase() + value.slice(1).replace(/_/g, " ")}
                                    </Button>
                                </Grid>
                            ))}
                        </Grid>
                    </Grid>

                    <Grid>
                        <Typography variant="h6" fontSize={"16px !important"} marginBottom={2} fontWeight={500}>
                            Time
                        </Typography>
                        <FormControl component="fieldset">
                            <RadioGroup
                                value={timeFilter}
                                onChange={(e) => {
                                    setChanged({ ...changed, timeChanged: true, oldTime: timeFilter });
                                    setTimeFilter(e.target.value);
                                }}
                            >
                                {["today", "last_3_days", "last_week", "last_month", "last_year", "all_time"].map((value) => (
                                    <FormControlLabel
                                        key={value}
                                        value={value}
                                        control={
                                            <Radio sx={{ color: "#737791", opacity: 0.3, "&.Mui-checked": { color: "#FFFFFF", opacity: 0.5 } }} />
                                        }
                                        label={value.replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase())}
                                        sx={{
                                            margin: 0,
                                            marginBottom: 1,
                                            width: "fit-content",
                                            padding: "0 15px 0 0",
                                            border: `1px solid ${timeFilter === value ? alpha("#FFFFFF", 0.5) : theme.palette.custom.borderColor}`,
                                            borderRadius: "8px",
                                            "& .MuiTypography-root": {
                                                color: timeFilter === value ? "#FFFFFF" : "#737791",
                                                fontWeight: 300,
                                            },
                                        }}
                                    />
                                ))}
                            </RadioGroup>
                        </FormControl>
                    </Grid>
                </Grid>
                <Grid container gap={2} justifyContent={"space-between"}>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                textTransform: "none",
                            }}
                            onClick={handleClear}
                        >
                            Clear filters
                        </Button>
                    </Grid>
                    <Grid>
                        <Button
                            sx={{
                                color: "#FFFFFF",
                                backgroundColor: theme.palette.custom.mainBlue,
                                "&:hover": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                            variant="contained"
                            onClick={handleSubmit}
                        >
                            Apply
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default FilterLogModal;
