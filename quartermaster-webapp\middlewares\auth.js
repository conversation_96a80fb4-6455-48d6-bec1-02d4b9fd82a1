const jwt = require("jsonwebtoken");
const userQueries = require("../queries/User");
const ApiKey = require("../models/ApiKey");

const isAuthenticated = async (req, res, next) => {
    if (!req.header("authorization")) return res.status(401).json({ message: "Unauthorized" });
    if (!req.header("authorization").startsWith("Bearer ")) return res.status(401).json({ message: "Token type must be Bearer" });
    const token = req.header("authorization").split("Bearer ")[1];
    if (!token) return res.status(401).json({ message: "Token is invalid" });

    try {
        const { user_id, api_key_id } = jwt.verify(token, process.env.JWT_SECRET);
        if (user_id) {
            const user = await userQueries.getUser({ user_id, includeUnprojected: true });
            if (user.is_deleted) return res.status(401).json({ message: "Your account has been deleted." });
            if (!user.jwt_tokens.includes(token)) return res.status(401).json({ message: "Token is not available." });
            req.user = user;
            next();
        } else if (api_key_id) {
            const apiKey = await ApiKey.findOne({ _id: api_key_id });
            if (!apiKey) return res.status(401).json({ message: "API key is invalid" });
            if (apiKey.is_deleted) return res.status(400).json({ message: "Your API key has been removed. Please contact an administrator" });
            if (apiKey.is_revoked) return res.status(400).json({ message: "Your access has been revoked. Please contact an administrator" });

            if (!apiKey.allowed_endpoints.includes(req._endpoint_id)) return res.status(403).json({ message: "You cannot access this resource" });

            apiKey.requests += 1;
            await apiKey.save();

            req.api_key = apiKey.toObject();
            next();
        } else throw new Error("JWT token returned unexpected data");
    } catch (err) {
        console.error(err);
        if (err instanceof jwt.JsonWebTokenError) res.status(401).json({ message: "Token is invalid" });
        else {
            console.error(err);
            res.status(500).json({ message: `Unexpected error occured: ${err.message}` });
        }
    }
};

module.exports = isAuthenticated;
