const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const isAuthenticated = require("../middlewares/auth");
const { validateData } = require("../middlewares/validator");
const { body } = require("express-validator");
const db = require("../modules/db");
const UserCompletionLogs = require("../models/UserCompletionLogs");
const { validateError } = require("../utils/functions");
const openai = require("openai");
const { default: rateLimit } = require("express-rate-limit");
const router = require("./S3");

const openaiClient = new openai.OpenAI(process.env.OPENAI_API_KEY);
const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_NLP_SUGGESTIONS),
    isAuthenticated,
    (req, res, next) => validateData([body("text").isString().notEmpty().withMessage("Text needs to be string and not empty")], req, res, next),
    async (req, res) => {
        try {
            const { text } = req.body;
            const super_categories = await db.qmai.collection("analysis_results").distinct("super_category", { super_category: { $ne: null } });
            const stream = await openaiClient.chat.completions.create({
                model: "chatgpt-4o-latest",
                messages: [
                    {
                        role: "system",
                        content:
                            "You are a helpful assistant returning only JSON. I will give you a phrase, and you will identify the country, color, category, or size of the vessel or ship (e.g., small, large). You will return a key-value JSON object with these attributes. " +
                            `Categories can be from the following: ${super_categories.join(", ")}.` +
                            'You can also identify time, which may be a range or a single time. If it is a range, return "startTime" and "endTime"; if it is a single time, return "time" as a timestamp (e.g., 2024-08-05T01:54:37.919+00:00). Today\'s date is ' +
                            `${new Date().toISOString()}.` +
                            "If no value is found for any attribute (color, country, size, weapon, or category), return null for that attribute. " +
                            'The "category" attribute can have multiple values, so always return it as an array. ' +
                            'Please replace the "country" key with "country_flags", "color" with "colors", "size" with "sizes", and "category" with "categories". ' +
                            'The attributes "categories", "colors", "sizes", and "country_flags" will always be arrays. ' +
                            'If time is found, only add "startTime" and "endTime" in the JSON. If an array is empty, return null.',
                        // `I want to restrict user to not let him get data for more than 1 year. if her requests huge data simply return empty response`
                    },
                    {
                        role: "user",
                        content: text,
                    },
                ],
            });

            console.log(stream.choices[0]?.message.content);

            // cleaning response received by removing ```, json, \n, and \ from it
            let responseContent = stream.choices[0]?.message.content || "Your search does not match any known artifacts.";
            UserCompletionLogs.create({ user_id: req.user._id || null, command: text, response: responseContent, completion_type: "events_filter" });

            responseContent = responseContent.replace(/```json|```|\n|\/|\\/g, "");
            const parsedResponse = JSON.parse(responseContent);
            res.json(parsedResponse);
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * /completions:
 *   post:
 *     summary: Fetch NLP suggestions
 *     description: Retrieves NLP suggestions based on the provided text input.
 *     tags: [Artifacts]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               text:
 *                 type: string
 *                 description: The text input for generating NLP suggestions.
 *                 example: "Identify the country, color, size, and weapon from this phrase."
 *     responses:
 *       200:
 *         description: A JSON object containing the NLP suggestions.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 country:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of identified countries.
 *                   example: ["Philippines", "China"]
 *                 color:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: Identified colors.
 *                   example: ["Red", Black]
 *                 size:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: Identified size.
 *                   example: ["Large", "Small"]
 *                 weapon:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: Identified weapon.
 *                   example: ["Missile", "Gun"]
 *                 category:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of identified super categories.
 *                   example: ["Cargo", "Fishing"]
 *                 startTime:
 *                   type: date-time
 *                   description: Start time of the identified range.
 *                   example: "2024-08-05T01:54:37.919+00:00"
 *                 endTime:
 *                   type: date-time
 *                   description: End time of the identified range.
 *                   example: "2024-08-05T01:54:37.919+00:00"
 *       400:
 *         description: Invalid request parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message describing the invalid request.
 *       401:
 *         description: Unauthorized. User must be authenticated.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Authentication error message.
 *       429:
 *         description: Too many requests.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Rate limit exceeded.
 *       500:
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Internal server error.
 */
