const mongoose = require('mongoose');
const db = require('../modules/db');

const permissionSchema = new mongoose.Schema({
    permission_id: { type: Number, required: true, unique: true },
    permission_name: { type: String, required: true, unique: true },
    permission_description: { type: String, required: true },
    assignable: { type: Boolean, required: true, default: true },
});

const Permission = db.qm.model('Permission', permissionSchema);

module.exports = Permission