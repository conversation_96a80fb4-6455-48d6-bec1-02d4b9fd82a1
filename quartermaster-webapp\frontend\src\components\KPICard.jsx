import { Grid, Typography } from "@mui/material";
import { useApp } from "../hooks/AppHook";

export default function KPICard({ title, value, values, icon }) {
    const { screenSize } = useApp();

    return (
        <Grid
            container
            height={"100%"}
            bgcolor={"primary.main"}
            flexDirection={screenSize.xs ? "column" : "row"}
            justifyContent={screenSize.xs ? "center" : "flex-start"}
            alignItems={"center"}
            borderRadius={"20px"}
            paddingX={2}
            paddingY={1}
            boxShadow={2}
            minHeight={125}
            gap={2}
        >
            <Grid display={"flex"} alignItems={"center"}>
                <img src={icon} alt="KPICard Icon" />
            </Grid>
            <Grid container width={"auto"} flexDirection={"column"} justifyContent={"center"} alignItems={screenSize.xs ? "center" : "flex-start"}>
                <Grid>
                    <Typography variant="body1" fontWeight={"400"} fontSize={"16px"} color={"#A0AEC0"} textAlign={screenSize.xs ? "center" : "left"}>
                        {title}
                    </Typography>
                </Grid>
                {value !== undefined ? (
                    <Grid>
                        <Typography variant="h5" fontWeight={"600"} fontSize={"24px"}>
                            {value < 10 ? "0" + value : value}
                        </Typography>
                    </Grid>
                ) : values ? (
                    <Grid
                        container
                        gap={2}
                        flexDirection={screenSize.xs ? "column" : "row"}
                        justifyContent={screenSize.xs ? "center" : "flex-start"}
                        alignItems={screenSize.xs ? "center" : "flex-start"}
                    >
                        {values.map((o, i) => (
                            <Grid key={i} container flexDirection={"column"} size="auto">
                                <Grid>
                                    <Typography variant="caption" fontWeight={"400"} fontSize={"12px"} color={"#A0AEC0"}>
                                        {o.title}
                                    </Typography>
                                </Grid>
                                <Grid>
                                    <Typography variant="h5" fontWeight={"600"} fontSize={"24px"}>
                                        {o.value < 10 ? "0" + o.value : o.value}
                                    </Typography>
                                </Grid>
                            </Grid>
                        ))}
                    </Grid>
                ) : (
                    <></>
                )}
            </Grid>
        </Grid>
    );
}
