import { Close } from "@mui/icons-material";
import { Box, Divider, Grid, IconButton, Typography } from "@mui/material";
import { forwardRef, useEffect } from "react";
import theme from "../theme";

const style = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    bgcolor: theme.palette.custom.darkBlue,
    boxShadow: 24,
    borderRadius: "8px",
    width: { xs: "auto", sm: "auto" },
    padding: 2,
    outline: "none !important",
};

const ModalContainer = forwardRef(({ children, title, onClose, headerPosition = "space-between", showDivider = false }, ref) => {
    useEffect(() => {
        if (ref.current) {
            ref.current.focus();
        }
    }, [ref]);

    return (
        <Box ref={ref} sx={style} tabIndex={0} role="dialog">
            <Grid container gap={1} flexDirection={"column"} color={"#FFFFFF"}>
                <Grid container position={"relative"} justifyContent={headerPosition}>
                    <Grid>
                        <Typography variant="h6">{title}</Typography>
                    </Grid>
                    {onClose && (
                        <Grid position={"absolute"} right={-5} top={-5}>
                            <IconButton onClick={onClose}>
                                <Close />
                            </IconButton>
                        </Grid>
                    )}
                </Grid>
                <Divider sx={{ backgroundColor: showDivider ? theme.palette.custom.borderColor : "transparent", marginBottom: 2 }} />
                <Grid>{children}</Grid>
            </Grid>
        </Box>
    );
});

ModalContainer.displayName = "ModalContainer";

export default ModalContainer;
