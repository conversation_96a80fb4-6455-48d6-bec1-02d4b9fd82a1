const express = require('express');
const { validateError } = require('../utils/functions');
const isAuthenticated = require('../middlewares/auth');
const { default: rateLimit } = require('express-rate-limit');
const hasPermission = require('../middlewares/hasPermission');
const { permissions } = require('../utils/permissions');
const ApiEndpoint = require('../models/ApiEndpoint');
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 10,
    message: { message: 'Too many requests from this IP' },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use('/', apiLimiter);

router.get('/',
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageApiKeys]),
    async (req, res) => {
        try {
            const apiEndpoints = await ApiEndpoint.find()
            res.json(apiEndpoints);
        } catch (err) {
            validateError(err, res)
        }
    }
);

module.exports = router;