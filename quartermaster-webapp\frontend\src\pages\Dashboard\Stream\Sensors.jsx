import { useMemo } from "react";
import { <PERSON>rid, Typography, Card, CardContent, useTheme, Stack, Skeleton } from "@mui/material";
import FiberManualRecordIcon from "@mui/icons-material/FiberManualRecord";
import { useApp } from "../../../hooks/AppHook";
import RegionGroupFilter from "../../../components/RegionGroupFilter";

const Sensors = ({
    view = "Single",
    loadingStreams,
    streams,
    selectedStream,
    setSelectedStream,
    fetchStreams,
    artifactIndicator,
    viewedArtifacts,
    selectedRegionGroup,
    setSelectedRegionGroup,
    regionGroups,
}) => {
    const { screenSize, devMode } = useApp();
    const theme = useTheme();

    const filteredStreams = useMemo(() => {
        if (selectedRegionGroup === "all") return streams;
        return streams.filter((stream) => stream.RegionGroup === selectedRegionGroup);
    }, [streams, selectedRegionGroup]);

    if (loadingStreams)
        return (
            <Stack direction={{ xs: "row", lg: "column" }} gap={"1px"} width={"100%"}>
                {Array.from({ length: screenSize.xs ? 4 : screenSize.md ? 7 : 3 }).map((_, index) => (
                    <Skeleton key={index} animation="wave" variant="rectangular" height={100} sx={{ width: { xs: 200, lg: "auto" } }} />
                ))}
            </Stack>
        );

    if (streams.length == 0)
        return (
            <Typography color={"#FFFFFF"} padding={"10px"} textAlign={"center"} sx={{ backgroundColor: "primary.light" }}>
                No sensors found
            </Typography>
        );

    return (
        <>
            {loadingStreams ? (
                <Stack direction={{ xs: "row", lg: "column" }} gap={"1px"}>
                    {Array.from({ length: screenSize.xs ? 4 : screenSize.md ? 7 : 3 }).map((_, index) => (
                        <Skeleton key={index} animation="wave" variant="rectangular" height={120} sx={{ width: { xs: 200, lg: "auto" } }} />
                    ))}
                </Stack>
            ) : streams.length == 0 ? (
                <Typography color={"#FFFFFF"} padding={"10px"} textAlign={"center"} sx={{ backgroundColor: "primary.light" }}>
                    No sensors in selected region
                </Typography>
            ) : (
                <Grid
                    container
                    className="dashboard-step-8"
                    flexDirection={{ xs: "row", lg: "column" }}
                    wrap="nowrap"
                    height={"auto"}
                    minWidth={"350px"}
                    maxHeight={400}
                    overflow={"auto"}
                >
                    <Grid
                        sx={{ position: "absolute", top: { xs: "30px", md: "30px", lg: "8px" }, right: { xs: "10px", md: "10px", lg: "45px" } }}
                        display={regionGroups.length > 1 ? "block" : "none"}
                    >
                        <RegionGroupFilter regionGroups={regionGroups} value={selectedRegionGroup} onChange={setSelectedRegionGroup} />
                    </Grid>
                    {filteredStreams.map((stream) => {
                        const artifactData = (artifactIndicator[stream.StreamName] || []).filter((a) => !viewedArtifacts.includes(a._id));
                        const artifactLength = artifactData.length;
                        return (
                            <Grid key={stream.StreamName} size="auto">
                                <Card
                                    elevation={0}
                                    sx={{
                                        borderRadius: 0,
                                        color: theme.palette.background.default,
                                        cursor: selectedStream.StreamName === stream.StreamName || view === "Mosaic" ? "not-allowed" : "pointer",
                                        display: "flex",
                                        backgroundColor:
                                            selectedStream.StreamName === stream.StreamName || view === "Mosaic" ? "primary.main" : "primary.light",
                                        transition: "0.2s",
                                        ":hover": {
                                            backgroundColor:
                                                selectedStream.StreamName === stream.StreamName || view === "Mosaic" ? "primary.main" : "#464F59",
                                            transition: "0.2s",
                                        },
                                        height: { xs: "100%", lg: "auto" },
                                    }}
                                    onClick={() => {
                                        if (selectedStream.StreamName === stream.StreamName || view === "Mosaic") return;
                                        // setStreamMode('LIVE')
                                        setSelectedStream(stream);
                                        fetchStreams();
                                    }}
                                >
                                    <CardContent
                                        sx={{
                                            width: "100%",
                                            padding: 0,
                                            "&:last-child": {
                                                paddingBottom: 0,
                                            },
                                        }}
                                    >
                                        <Grid
                                            container
                                            alignItems={{ xs: "unset", lg: "center" }}
                                            flexDirection={{ xs: "column", lg: "row" }}
                                            maxWidth={{ xs: 200, lg: "100%" }}
                                            gap={1.8}
                                            padding={{ xs: 1, lg: 1 }}
                                        >
                                            <Grid
                                                size={{
                                                    xs: "auto",
                                                    lg: "grow",
                                                }}
                                            >
                                                <img
                                                    width={"100%"}
                                                    height={"100%"}
                                                    style={{ objectFit: "cover", borderRadius: "10px" }}
                                                    src={stream.Tags.Thumbnail || "/ship.png"}
                                                />
                                            </Grid>
                                            <Grid
                                                container
                                                flexDirection={"column"}
                                                gap={1}
                                                size={{
                                                    xs: "grow",
                                                    lg: "grow",
                                                }}
                                            >
                                                <Grid>
                                                    <Typography fontSize={15} lineHeight={"25px"} fontWeight={600}>
                                                        {(!devMode && stream.Tags.Name) || stream.StreamName}
                                                    </Typography>
                                                </Grid>
                                                <Grid
                                                    container
                                                    display={"flex"}
                                                    justifyContent={"space-between"}
                                                    flexDirection={"row"}
                                                    alignItems={"center"}
                                                    gap={1}
                                                    size="auto"
                                                >
                                                    <Grid display={"flex"} alignItems={"center"} size="grow">
                                                        <Grid alignItems={"center"} gap={1} size="auto">
                                                            <FiberManualRecordIcon
                                                                sx={{
                                                                    display: stream.IsLive ? "block" : "none",
                                                                    color: theme.palette.custom.live,
                                                                    fontSize: "20px",
                                                                    lineHeight: "20px",
                                                                }}
                                                            />
                                                        </Grid>
                                                        <Grid alignItems={"center"} gap={1} size="auto">
                                                            <Typography
                                                                sx={{
                                                                    color: stream.IsLive ? theme.palette.custom.live : theme.palette.custom.offline,
                                                                }}
                                                                fontSize={"16px"}
                                                                fontWeight={400}
                                                            >
                                                                {stream.IsLive ? "LIVE" : "OFFLINE"}
                                                            </Typography>
                                                        </Grid>
                                                    </Grid>
                                                    {artifactLength > 0 ? (
                                                        <Grid display={"flex"} alignItems={"center"} size="grow">
                                                            <Grid alignItems={"center"} gap={1} size="auto">
                                                                <Typography sx={{ color: "red" }} fontSize={"16px"} fontWeight={400}>
                                                                    {artifactLength}
                                                                </Typography>
                                                            </Grid>
                                                            <Grid alignItems={"center"} gap={1} size="auto">
                                                                <Grid sx={{ display: "flex", justifyContent: "center", alignItems: "center" }}>
                                                                    <img
                                                                        src={"/icons/sensor_alert_icon.svg"}
                                                                        alt={"Artifact Indicator"}
                                                                        width={20}
                                                                        height={20}
                                                                        style={{ color: "red", marginLeft: "2px" }}
                                                                    />
                                                                </Grid>
                                                            </Grid>
                                                        </Grid>
                                                    ) : (
                                                        ""
                                                    )}
                                                </Grid>
                                            </Grid>
                                        </Grid>
                                    </CardContent>
                                </Card>
                            </Grid>
                        );
                    })}
                </Grid>
            )}
        </>
    );
};

export default Sensors;
