import { browser<PERSON>ame, osName, osVersion } from 'react-device-detect';
import { io } from 'socket.io-client';
import environment from '../environment';

let socket = null

const createSocket = () => {
    return io(environment.VITE_API_URL, {
        autoConnect: true,
        auth: {
            jwt_token: localStorage.getItem('jwt_token'),
            device: `${osName} ${osVersion}`,
            browser: browserName
        }
    });
};

export const getSocket = () => {
    if (!socket) {
        socket = createSocket();
    }
    return socket;
};

export const reconnectSocket = () => {
    if (socket) {
        socket.disconnect();
    }
    socket = createSocket();
    return socket;
};

export const disconnectSocket = () => {
    if (socket) {
        socket.disconnect();
        socket = null;
    }
};
