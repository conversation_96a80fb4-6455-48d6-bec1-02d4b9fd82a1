const mongoose = require('mongoose');

jest.mock('mongoose');

describe('Manually invoking db.qm.on', () => {
    let mockConnectionQM;
    let mockConnectionQMAI;

    beforeEach(() => {
        // Mock the connection object for 'quartermaster'
        mockConnectionQM = {
            on: jest.fn((event, callback) => {
                if (event === 'open') callback();
                if (event === 'error') callback(new Error('Connection error'));
            }),
        };
        mockConnectionQMAI = {
            on: jest.fn((event, callback) => {
                if (event === 'open') callback();
                if (event === 'error') callback(new Error('Connection error'));
            }),
        };

        // Mock mongoose.createConnection
        mongoose.createConnection.mockImplementation((uri, options) => {
            if (options.dbName === 'quartermaster') return mockConnectionQM;
            if (options.dbName === 'artifact_processor') return mockConnectionQMAI;
        });

        // Require the db file after mocks are set up
        require('../../modules/db');
    });

    it('should successfully create a connection', () => {
        expect(mockConnectionQM).toBeDefined()
        expect(mockConnectionQMAI).toBeDefined()
    });
});
