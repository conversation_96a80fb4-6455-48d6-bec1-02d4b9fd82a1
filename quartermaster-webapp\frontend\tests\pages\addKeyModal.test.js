import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import AddKeyModal from "../../src/pages/Dashboard/ApiKeys//AddKeyModal";
import axiosInstance from "../../src/axios";

jest.mock("../../src/axios", () => ({
    post: jest.fn(),
}));

describe("AddKeyModal", () => {
    let setShowAddKey, setAdding;

    beforeEach(() => {
        setShowAddKey = jest.fn();
        setAdding = jest.fn();
        axiosInstance.post.mockResolvedValue({});
        render(
            <AddKeyModal
                showAddKey={true}
                setShowAddKey={setShowAddKey}
                setAdding={setAdding}
            />
        );
    });

    it("renders the modal and description input field", () => {
        expect(screen.getByText("Create API Key")).toBeInTheDocument();
        expect(screen.getByLabelText("Description")).toBeInTheDocument();
    });

    it("enables Submit button when description is provided", async () => {
        const descriptionInput = screen.getByLabelText("Description");
        fireEvent.change(descriptionInput, { target: { value: "Test API Key" } });

        await waitFor(() => {
            expect(screen.getByText("Submit")).toBeEnabled();
        });
    });

    it("closes the modal when the form is submitted", async () => {
        const descriptionInput = screen.getByLabelText("Description");
        fireEvent.change(descriptionInput, { target: { value: "Test API Key" } });

        const submitButton = screen.getByText("Submit");
        fireEvent.click(submitButton);

        await waitFor(() => {
            expect(setShowAddKey).toHaveBeenCalledWith(false);
            expect(setAdding).toHaveBeenCalledWith(true);
            expect(axiosInstance.post).toHaveBeenCalledWith(
                '/apiKeys',
                { description: "Test API Key" },
                { meta: { showSnackbar: true } }
            );
        });
    });
});
