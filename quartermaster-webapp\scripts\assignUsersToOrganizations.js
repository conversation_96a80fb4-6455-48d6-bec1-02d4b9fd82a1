require("dotenv").config();
const Organization = require("../models/Organization");
const User = require("../models/User");

async function assignUsersToOrganizations() {
    try {
        const organizations = await Organization.find({});
        if (!organizations.length) {
            console.log("No organizations found.");
            return;
        }

        // Find or create the "Miscellaneous" organization
        let miscOrg = await Organization.findOne({ name: "Miscellaneous" });
        if (!miscOrg) {
            miscOrg = await Organization.create({
                name: "Miscellaneous",
                domain: "miscellaneous",
                is_internal: false,
            });
            console.log("Created 'Miscellaneous' organization.");
        }

        for (const org of organizations) {
            const domain = org.domain;
            const users = await User.find({
                email: {
                    $regex: new RegExp(`@${domain.replace(/\./g, "\\.")}$`, "i"),
                },
            });

            if (!users.length) {
                console.log(`No users found for organization '${org.name}' with domain '${domain}'.`);
                continue;
            }

            let updatedCount = 0;
            for (const user of users) {
                user.organization_id = org._id;
                await user.save();
                updatedCount++;
            }

            console.log(`Updated ${updatedCount} users for organization '${org.name}'.`);
        }

        // Assign users with domains "gmail.com" and "random.com" to "Miscellaneous"
        const miscDomains = ["gmail.com", "random.com"];
        for (const domain of miscDomains) {
            const miscUsers = await User.find({
                email: {
                    $regex: new RegExp(`@${domain.replace(/\./g, "\\.")}$`, "i"),
                },
            });

            if (!miscUsers.length) {
                console.log(`No users found for domain '${domain}' to assign to 'Miscellaneous'.`);
                continue;
            }

            let miscUpdatedCount = 0;
            for (const user of miscUsers) {
                user.organization_id = miscOrg._id;
                await user.save();
                miscUpdatedCount++;
            }

            console.log(`Updated ${miscUpdatedCount} users for domain '${domain}' to 'Miscellaneous'.`);
        }

        console.log("All users assigned to organizations successfully.");
    } catch (err) {
        console.error("Error assigning users to organizations:", err);
    } finally {
        process.exit(0);
    }
}

assignUsersToOrganizations();
