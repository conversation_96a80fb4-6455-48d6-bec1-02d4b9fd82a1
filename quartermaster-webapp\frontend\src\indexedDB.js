import { openDB } from "idb";

const DB_NAME = "quartermaster";
var db;
var promiseLock = Promise.resolve(); // A promise to manage sequential upgrades

// Initialize IndexedDB
const initDB = async () => {
    promiseLock = promiseLock.then(async () => {
        db = await openDB(DB_NAME);
        // console.log('db object', db)
        db.onversionchange = async () => {
            console.warn("IndexedDB version changed, trying to upgrade...");
            db.close();
            initDB();
        };
        // eslint-disable-next-line no-unused-vars
        db.onerror = async (_, err) => {
            console.warn("IndexedDB error occured, trying to reinitialize...");
            db.close();
            initDB();
        };
    });
    return await promiseLock;
};

// Add an item to the store
const addItems = async (store, items) => {
    promiseLock = promiseLock.then(async () => {
        // Check if the object store exists, create it if it does not
        if (!db.objectStoreNames.contains(store)) {
            await createObjectStore(store);
        }
        const tx = db.transaction(store, "readwrite");
        const oStore = tx.objectStore(store);
        // Add multiple items
        for (const item of items) {
            // Check if the item already exists in the store
            const existingItem = await oStore.get(item._id); // Adjust key if using a different key path

            // If the item does not exist, add it to the store
            if (!existingItem) {
                await oStore.add(item);
            }
        }
        await tx.done;
    });
    return await promiseLock;
};

const getItem = async (store, id) => {
    promiseLock = promiseLock.then(async () => {
        if (!db.objectStoreNames.contains(store)) {
            return null;
        }
        const tx = db.transaction(store, "readonly");
        const item = await tx.objectStore(store).get(id);
        await tx.done;
        return item || null;
    });
    return await promiseLock;
};

// Get all items from the store
const getItems = async (store, filter) => {
    promiseLock = promiseLock.then(async () => {
        if (!db.objectStoreNames.contains(store)) {
            return [];
        }
        const tx = db.transaction(store, "readonly");
        const items = await tx.objectStore(store).getAll();
        await tx.done;
        if (filter) {
            return items.filter(filter);
        }
        return items;
    });
    return await promiseLock;
};

// Delete an item from the store
const deleteItem = async (store, id) => {
    promiseLock = promiseLock.then(async () => {
        const tx = db.transaction(store, "readwrite");
        await tx.objectStore(store).delete(id);
        await tx.done;
    });
    return await promiseLock;
};

// Create a new object store dynamically
const createObjectStore = async (store) => {
    const version = db.version + 1; // Increment version to create new store
    db.close(); // Close the database before upgrading
    db = await openDB(DB_NAME, version, {
        upgrade(_db) {
            if (!_db.objectStoreNames.contains(store)) {
                _db.createObjectStore(store, { keyPath: "_id" });
            }
        },
    });
    db.onversionchange = async () => {
        console.warn("IndexedDB version changed, trying to upgrade...");
        db.close();
        initDB();
    };
    // eslint-disable-next-line no-unused-vars
    db.onerror = async (_, err) => {
        console.warn("IndexedDB error occured, trying to reinitialize...");
        db.close();
        initDB();
    };
    return;
};

const clearIndexedDB = async () => {
    promiseLock = promiseLock.then(async () => {
        const dbs = await indexedDB.databases();
        if (dbs) {
            dbs.forEach(async (db) => {
                indexedDB.deleteDatabase(db.name);
            });
        }
    });
    return await promiseLock;
};

const updateItem = async (store, id, updates) => {
    promiseLock = promiseLock.then(async () => {
        // Check if the object store exists
        if (!db.objectStoreNames.contains(store)) {
            return [];
        }
        const tx = db.transaction(store, "readwrite");
        const oStore = tx.objectStore(store);
        // Fetch the existing item
        const existingItem = await oStore.get(id);
        // Check if the item exists
        if (!existingItem) {
            return [];
        }
        // Merge updates into the existing item
        const updatedItem = { ...existingItem, ...updates };
        // Update the item in the store
        await oStore.put(updatedItem);
        await tx.done;
        return updatedItem;
    });

    return await promiseLock;
};

const idb = {
    initDB,
    addItems,
    getItem,
    getItems,
    deleteItem,
    clearIndexedDB,
    updateItem,
};

export default idb;
