import { Box, Slider } from "@mui/material";
import dayjs from "dayjs";
import { userValues } from "../utils";
import { useApp } from "../hooks/AppHook";
import { useUser } from "../hooks/UserHook.jsx";
export default function DateTimeSlider({ key, range, onChangeCommitted, steps = 60000, minWidth = 200, size = "small" } = {}) {
    const { timezone } = useApp();
    const { user } = useUser();

    return (
        <Box sx={{ paddingX: 6, minWidth }}>
            <Slider
                valueLabelFormat={(v) =>
                    dayjs(v)
                        .tz(timezone)
                        .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))
                }
                valueLabelDisplay="auto"
                key={key}
                size={size}
                defaultValue={range}
                min={range[0]}
                max={range[1]}
                step={steps}
                marks={[
                    {
                        value: range[0],
                        label: dayjs(range[0])
                            .tz(timezone)
                            .format(userValues.dateTimeFormat(user, { exclude_seconds: true })),
                    },
                    {
                        value: range[1],
                        label: dayjs(range[1])
                            .tz(timezone)
                            .format(userValues.dateTimeFormat(user, { exclude_seconds: true })),
                    },
                ]}
                sx={{ paddingBottom: 0.6, height: "2px" }}
                onChangeCommitted={onChangeCommitted}
            />
        </Box>
    );
}
