import { render, screen, fireEvent } from '@testing-library/react';
import ModalContainer from '../../src/components/ModalContainer';
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';
import React from 'react';

describe('ModalContainer', () => {
    const renderWithTheme = (component) => render(<ThemeProvider theme={theme}>{component}</ThemeProvider>);
    const mockOnClose = jest.fn();

    it('focuses on the container when rendered', () => {
        const ref = { current: null };
        renderWithTheme(
            <ModalContainer ref={ref} title="Test Modal" onClose={mockOnClose}>Test Content</ModalContainer>
        );
        expect(ref.current).toHaveFocus();
    });
});
