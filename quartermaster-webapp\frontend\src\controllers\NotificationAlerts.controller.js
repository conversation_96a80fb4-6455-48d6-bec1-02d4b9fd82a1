import axiosInstance from "../axios";

class NotificaionAlertController {
    async getLatestUserNotificationAlerts() {
        try {
            const res = await axiosInstance.get(`/notificationsAlerts/testing`, { meta: { showSnackbar: true } });
            return res;
        } catch (error) {
            console.log("Error Get All Latest Notification Alerts " + error);
            return error.response.data;
        }
    }
}

const notificationAlertController = new NotificaionAlertController();

export default notificationAlertController;
