import { render, screen, fireEvent } from '@testing-library/react';
import LogManagement from '../../src/pages/Dashboard/LogsPage/LogManagement';
import { useUser } from '../../src/hooks/UserHook';
import { permissions } from '../../src/utils';
import React from 'react';
import { useApp } from '../../src/hooks/AppHook';

jest.mock('../../src/hooks/UserHook', () => ({
    useUser: jest.fn(),
}));
jest.mock('../../src/hooks/AppHook', () => ({
    useApp: jest.fn(),
}));

jest.mock('../../src/pages/Dashboard/LogsPage/Sessions', () => () => <div>Sessions Component</div>);

describe('LogManagement Component', () => {
    const mockUserWithPermissions = {
        hasPermissions: (permissionArray) => permissionArray.includes(permissions.viewSessionLogs),
    };

    beforeEach(() => {
        useApp.mockReturnValue({ isMobile: true });
        jest.clearAllMocks();
    });

    it('renders Sessions tab and component if user has permissions', () => {
        useUser.mockReturnValue({ user: mockUserWithPermissions });
        useApp.mockReturnValue({ isMobile: false });
        render(<LogManagement />);

        expect(screen.getByRole('tab', { name: /sessions/i })).toBeInTheDocument();
        expect(screen.getByText(/sessions component/i)).toBeInTheDocument();
    });

    it('switches tabs correctly when clicked', () => {
        useUser.mockReturnValue({ user: mockUserWithPermissions });

        const originalUseMemo = jest.requireActual('react').useMemo;
        jest.spyOn(React, 'useMemo').mockImplementationOnce((fn) => {
            return originalUseMemo(() => [
                { value: 'sessions', label: 'Sessions', component: <div>Sessions Component</div>, display: true },
                { value: 'activity', label: 'Activity', component: <div>Activity Component</div>, display: true },
                { value: 'errors', label: 'Errors', component: <div>Errors Component</div>, display: true },
            ], []);
        });

        render(<LogManagement />);

        const tabs = ['Sessions', 'Activity', 'Errors'];

        tabs.forEach(tabLabel => {
            const tab = screen.getByRole('tab', { name: new RegExp(tabLabel, 'i') });
            fireEvent.click(tab);

            expect(tab).toHaveAttribute('aria-selected', 'true');
            expect(screen.getByText(new RegExp(`${tabLabel} component`, 'i'))).toBeVisible();
        });
    });

    it('renders nothing if no tabs are set to display', () => {
        const mockUserWithoutDisplayableTabs = {
            hasPermissions: () => false,
        };
        useUser.mockReturnValue({ user: mockUserWithoutDisplayableTabs });
        const { container } = render(<LogManagement />);

        expect(container).toBeEmptyDOMElement();
    });

    it('handles search input changes', () => {
        useUser.mockReturnValue({ user: mockUserWithPermissions });
        render(<LogManagement />);

        const searchInput = screen.getByPlaceholderText('Search');
        fireEvent.change(searchInput, { target: { value: 'test search' } });

        expect(searchInput).toHaveValue('test search');
    });

    it('toggles filter modal on filter button click', () => {
        useUser.mockReturnValue({ user: mockUserWithPermissions });
        useApp.mockReturnValue({ isMobile: false });

        render(<LogManagement />);

        const filterButton = screen.getByRole('button', { name: /filter/i });
        fireEvent.click(filterButton);

        expect(screen.getByText('Sessions Component')).toBeInTheDocument();
    });
});
