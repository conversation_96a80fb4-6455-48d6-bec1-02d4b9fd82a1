const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Role = require('../../models/Role');
const { default: mongoose } = require('mongoose');
const crypto = require('crypto');
const { sendEmail } = require('../../modules/email');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const { permissionsList } = require('../data/Permissions');
const Permission = require('../../models/Permission');
const { streamsList } = require('../data/Kinesis');
const { s3 } = require('../../modules/awsS3');
const { sessionLogsList } = require('../data/Logs');
const SessionLog = require('../../models/SessionLog');
const request = require("supertest");
const app = require('../../server');
const Statistics = require('../../models/Statistics');
const { statisticsList } = require('../data/Statistics');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe('Statistics API', () => {

    describe('GET /api/statistics/', () => {

        const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
            const nonAuthMockResolve = authMethod === 'user' ? [userOrApiKey.nonAuthorized] : userOrApiKey.nonAuthorized;
            const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
            const nonAuthToken = 'Bearer ' + generateToken(userOrApiKey.nonAuthorized._id);
            const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

            describe(`${authMethod} authentication`, () => {

                beforeEach(() => {
                    jest.resetAllMocks();
                });

                it('should return 401 if no token is provided', async () => {
                    const res = await request(app).get('/api/statistics');
                    expect(res.status).toBe(401);
                });

                it('should return 403 if user does not have the required permissions', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(nonAuthMockResolve);

                    const res = await request(app)
                        .get('/api/statistics')
                        .set('Authorization', nonAuthToken);

                    expect(res.status).toBe(403);
                });

                it('should return 400 if invalid query is provided', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Statistics.find.mockResolvedValue(statisticsList);

                    const res = await request(app)
                        .get('/api/statistics?type=invalid')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(400);
                });

                it('should return 200 and fetch statistics if the user is authorized', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Statistics.find.mockResolvedValue(statisticsList);

                    const res = await request(app)
                        .get('/api/statistics')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    ['_id', 'stats', 'fromTimestamp', 'toTimestamp', 'type'].forEach(prop => {
                        expect(res.body[0]).toHaveProperty(prop);
                    });
                });

                it('should return 200 and fetch daily statistics', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Statistics.find.mockResolvedValue(statisticsList);

                    const res = await request(app)
                        .get('/api/statistics?type=daily')
                        .set('Authorization', authToken);
                    console.log(res)
                    expect(res.status).toBe(200);
                    expect(res.body).toBeInstanceOf(Array);
                    ['_id', 'stats', 'fromTimestamp', 'toTimestamp', 'type'].forEach(prop => {
                        expect(res.body[0]).toHaveProperty(prop);
                    });
                });

                it('should return 500 if an error occurs while fetching statistics', async () => {
                    mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                    Statistics.find.mockRejectedValueOnce(new Error('Database error'));

                    const res = await request(app)
                        .get('/api/statistics')
                        .set('Authorization', authToken);

                    expect(res.status).toBe(500);
                });
            });
        };

        runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
        runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
    });
});
