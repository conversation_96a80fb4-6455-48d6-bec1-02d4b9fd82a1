import { Grid, Typography } from "@mui/material";
import React from "react";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import { useLocation } from "react-router-dom";
import CancelIcon from "@mui/icons-material/Cancel";
function Subscription() {
    const location = useLocation();
    const params = new URLSearchParams(location.search);
    const status = parseInt(params.get("status"));
    const message = params.get("message");
    const title = params.get("title");

    console.log(title, message);
    return (
        <Grid
            container
            sx={{
                backgroundColor: "black",
                width: "100%",
                height: "100vh",
                display: "flex",
                flexDirection: "column",
                // justifyContent: 'center',
                alignItems: "center",
                textAlign: "center",
                padding: "20px",
            }}
        >
            <CheckCircleIcon
                data-testid="CheckCircleIcon"
                color="success"
                sx={{ fontSize: { xs: "60px", md: "80px" }, display: status === 200 ? "" : "none" }}
            />
            <CancelIcon data-testid="CancelIcon" sx={{ fontSize: { xs: "60px", md: "80px", color: "red" }, display: status != 200 ? "" : "none" }} />
            <Grid sx={{ mt: 2 }}>
                <Typography
                    sx={{
                        fontSize: { xs: "24px", md: "36px" },
                        color: "white",
                        fontWeight: "bold",
                    }}
                >
                    {title}
                </Typography>
                <Typography
                    sx={{
                        color: "white",
                        fontSize: { xs: "14px", md: "18px" },
                        fontWeight: "500",
                        mt: 1,
                    }}
                >
                    {message}
                </Typography>
            </Grid>
        </Grid>
    );
}

export default Subscription;
