const mongoose = require('mongoose');
const User = require('./User');
const db = require('../modules/db');


const userCompletionLogsSchema = new mongoose.Schema({
    user_id: { type: mongoose.Schema.Types.ObjectId, ref: User, required: true },
    command: { type: String, required: true },
    response: { type: String, required: true },
    completion_type: { type: String, required: true, enum: ['events_filter', 'video_filters', 'other'] },
    completed_at: { type: Date, default: () => new Date().toISOString() },
    updated_at: { type: Date, default: () => new Date().toISOString() },
});

const UserCompletionLogs = db.qm.model('UserCompletionLogs', userCompletionLogsSchema, 'logs_users_completions');

module.exports = UserCompletionLogs;