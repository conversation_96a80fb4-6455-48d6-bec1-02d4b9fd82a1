require('dotenv').config();
const { processMessage } = require('../services/gps');
const { calculateDistanceInMeters } = require('../utils/functions');
const db = require('../modules/db');

// Global Configuration
const CONFIG = {
    INITIAL_DISTANCE: 2, // Starting distance in meters
    NUM_MESSAGES: 50,    // Number of messages to generate
    MAX_ATTEMPTS: 20,    // Maximum attempts to generate valid coordinates
    START_COORDINATES: {  // Starting point (Philippines)
        latitude: 14.5995,
        longitude: 120.9842
    },
    TOPIC: 'test-vessel/gps/status',
    REGION: 'ap-southeast-1'
};

// Function to generate a new coordinate with specified distance from previous
async function generateNextCoordinate(prevLat, prevLon, targetDistance) {
    let attempts = 0;
    let bestCoordinate = null;
    let bestDistance = Infinity;
    let bestDistanceDiff = Infinity;

    while (attempts < CONFIG.MAX_ATTEMPTS) {
        // Approximate degree change for 1 meter at the equator
        const oneDegree = 111320; // meters per degree at equator

        // Random angle in radians
        const angle = Math.random() * 2 * Math.PI;

        // Calculate new coordinates (approximate)
        const latChange = (targetDistance * Math.cos(angle)) / oneDegree;
        const lonChange = (targetDistance * Math.sin(angle)) / (oneDegree * Math.cos(prevLat * Math.PI / 180));

        const newLat = prevLat + latChange;
        const newLon = prevLon + lonChange;

        // Verify distance
        const actualDistanceMeters = await calculateDistanceInMeters(prevLat, prevLon, newLat, newLon);
        const distanceDiff = Math.abs(actualDistanceMeters - targetDistance);

        // Keep track of best result so far
        if (distanceDiff < bestDistanceDiff) {
            bestDistanceDiff = distanceDiff;
            bestDistance = actualDistanceMeters;
            bestCoordinate = { latitude: newLat, longitude: newLon };
        }

        // If within 5% error margin, use these coordinates
        const errorMargin = 0.05;
        if (distanceDiff <= (targetDistance * errorMargin)) {
            console.log(`Generated coordinate ${actualDistanceMeters.toFixed(2)}m apart (target: ${targetDistance}m)`);
            return { latitude: newLat, longitude: newLon };
        }

        attempts++;
    }

    // If we didn't find coordinates within error margin, use the best ones we found
    console.log(`Using best available coordinate ${bestDistance.toFixed(2)}m apart (target: ${targetDistance}m, diff: ${bestDistanceDiff.toFixed(2)}m)`);
    return bestCoordinate;
}

// Create GPS message with given coordinates
function createMessage(latitude, longitude) {
    return {
        header: {
            stamp: {
                sec: Math.floor(Date.now() / 1000)
            }
        },
        valid_gnss_fix: true,
        latitude,
        longitude,
        ground_speed: 5.5 + (Math.random() * 5)
    };
}

// Process a single message
async function processGPSMessage(latitude, longitude) {
    const message = createMessage(latitude, longitude);
    const messageBuffer = Buffer.from(JSON.stringify(message));
    try {
        await processMessage(CONFIG.TOPIC, messageBuffer, CONFIG.REGION);
        return true;
    } catch (error) {
        console.error('Error processing message:', error);
        return false;
    }
}

// Main test function
async function runTest() {
    let currentLat = CONFIG.START_COORDINATES.latitude;
    let currentLon = CONFIG.START_COORDINATES.longitude;
    let targetDistance = CONFIG.INITIAL_DISTANCE;
    let successCount = 0;

    console.log('Starting test sequence...');

    for (let i = 0; i < CONFIG.NUM_MESSAGES; i++) {
        console.log(`\nProcessing message ${i + 1}/${CONFIG.NUM_MESSAGES} (Target distance: ${targetDistance}m)`);

        try {
            // Process current position
            if (await processGPSMessage(currentLat, currentLon)) {
                successCount++;
            }

            // Generate next position with doubled distance
            if (i < CONFIG.NUM_MESSAGES - 1) {
                const nextCoord = await generateNextCoordinate(currentLat, currentLon, targetDistance);
                currentLat = nextCoord.latitude;
                currentLon = nextCoord.longitude;
                targetDistance *= 1.25; // Double the distance for next iteration
            }
        } catch (error) {
            console.error(`Failed at iteration ${i + 1}:`, error);
        }
    }

    console.log(`\nTest completed. Successfully processed ${successCount}/${CONFIG.NUM_MESSAGES} messages.`);
    return successCount;
}

// Start the test when database is connected
db.qm.once('open', async () => {
    console.log('Database connected, starting test...');
    try {
        await runTest();
    } catch (error) {
        console.error('Test sequence failed:', error);
    } finally {
        process.exit(0);
    }
});