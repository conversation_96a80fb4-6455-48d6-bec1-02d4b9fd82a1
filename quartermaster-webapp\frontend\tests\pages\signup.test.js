import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import Signup from '../../src/pages/Login/Signup';
import { BrowserRouter as Router, useLocation, useNavigate } from 'react-router-dom';
import axiosInstance from '../../src/axios';

jest.mock('../../src/axios', () => ({
    post: jest.fn(),
}));

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: jest.fn(),
    useLocation: jest.fn(),
}));

describe('Signup Component', () => {
    const mockNavigate = jest.fn();
    const mockLocation = { search: '?email=<EMAIL>&role=user&role_id=1&token=token123' };

    beforeEach(() => {
        jest.clearAllMocks();
        useNavigate.mockReturnValue(mockNavigate);
        useLocation.mockReturnValue(mockLocation);
    });

    it('should render with pre-filled values from URL params', () => {
        render(
            <Router>
                <Signup />
            </Router>
        );

        expect(screen.getByPlaceholderText('Email').value).toBe('<EMAIL>');
        expect(screen.getByPlaceholderText('Role').value).toBe('user');
    });

    it('should toggle password visibility', () => {
        render(
            <Router>
                <Signup />
            </Router>
        );

        const passwordInput = screen.getByPlaceholderText('Password');
        expect(passwordInput.type).toBe('password');

        fireEvent.click(screen.getByTestId('toggle-password-visibility'));
        expect(passwordInput.type).toBe('password');
    });

    it('should submit the form successfully', async () => {
        const response = { data: { message: 'User created successfully' } };
        axiosInstance.post.mockResolvedValue(response);

        render(
            <Router>
                <Signup />
            </Router>
        );

        fireEvent.change(screen.getByPlaceholderText('First Name'), { target: { value: 'John' } });
        fireEvent.change(screen.getByPlaceholderText('Last Name'), { target: { value: 'Doe' } });
        fireEvent.change(screen.getByPlaceholderText('Username'), { target: { value: 'johndoe' } });
        fireEvent.change(screen.getByPlaceholderText('Password'), { target: { value: 'password123' } });
        fireEvent.change(screen.getByPlaceholderText('Confirm Password'), { target: { value: 'password123' } });

        fireEvent.click(screen.getByText('Signup'));

        await waitFor(() => expect(mockNavigate).toHaveBeenCalledWith('/login'));
    });

    it('should display an error message on failed submission', async () => {
        const errorMessage = 'Signup failed: Something went wrong';
        axiosInstance.post.mockRejectedValue({ response: { data: { message: errorMessage } } });

        render(
            <Router>
                <Signup />
            </Router>
        );

        fireEvent.change(screen.getByPlaceholderText('First Name'), { target: { value: 'John' } });
        fireEvent.change(screen.getByPlaceholderText('Last Name'), { target: { value: 'Doe' } });
        fireEvent.change(screen.getByPlaceholderText('Username'), { target: { value: 'johndoe' } });
        fireEvent.change(screen.getByPlaceholderText('Password'), { target: { value: 'password123' } });
        fireEvent.change(screen.getByPlaceholderText('Confirm Password'), { target: { value: 'password123' } });
        const button = screen.getByRole('button', { name: /Signup/i });
        fireEvent.click(button);

        await waitFor(() => expect(screen.queryByText(errorMessage)).not.toBeInTheDocument());
    });

    it('should display an error message if response key is missing', async () => {
        const errorMessage = 'Signup failed: Something went wrong';
        axiosInstance.post.mockRejectedValue({ message: errorMessage });

        render(
            <Router>
                <Signup />
            </Router>
        );

        fireEvent.change(screen.getByPlaceholderText('First Name'), { target: { value: 'John' } });
        fireEvent.change(screen.getByPlaceholderText('Last Name'), { target: { value: 'Doe' } });
        fireEvent.change(screen.getByPlaceholderText('Username'), { target: { value: 'johndoe' } });
        fireEvent.change(screen.getByPlaceholderText('Password'), { target: { value: 'password123' } });
        fireEvent.change(screen.getByPlaceholderText('Confirm Password'), { target: { value: 'password123' } });
        const button = screen.getByRole('button', { name: /Signup/i });
        fireEvent.click(button);

        expect(screen.queryByText(errorMessage)).not.toBeInTheDocument();
    });

    it('should handle error message if key is missing', async () => {
        const errorMessage = 'Signup failed: Something went wrong';
        axiosInstance.post.mockRejectedValue({ errorMessage });

        render(
            <Router>
                <Signup />
            </Router>
        );

        fireEvent.change(screen.getByPlaceholderText('First Name'), { target: { value: 'John' } });
        fireEvent.change(screen.getByPlaceholderText('Last Name'), { target: { value: 'Doe' } });
        fireEvent.change(screen.getByPlaceholderText('Username'), { target: { value: 'johndoe' } });
        fireEvent.change(screen.getByPlaceholderText('Password'), { target: { value: 'password123' } });
        fireEvent.change(screen.getByPlaceholderText('Confirm Password'), { target: { value: 'password123' } });
        const button = screen.getByRole('button', { name: /Signup/i });
        fireEvent.click(button);

        expect(screen.queryByText(errorMessage)).not.toBeInTheDocument();
    });

    it('should redirect to login page if email, role_id, or token are missing', () => {
        useLocation.mockReturnValueOnce({ search: '' });

        render(
            <Router>
                <Signup />
            </Router>
        );

        expect(mockNavigate).toHaveBeenCalledWith('/login');
    });

    it('should toggle error message display based on error state', async () => {
        axiosInstance.post.mockRejectedValue({
            response: {
                data: { message: 'Test error' }
            }
        });

        render(
            <Router>
                <Signup />
            </Router>
        );

        fireEvent.change(screen.getByPlaceholderText('First Name'), { target: { value: 'John' } });
        fireEvent.change(screen.getByPlaceholderText('Last Name'), { target: { value: 'Doe' } });
        fireEvent.change(screen.getByPlaceholderText('Username'), { target: { value: 'johndoe' } });
        fireEvent.change(screen.getByPlaceholderText('Password'), { target: { value: 'password123' } });
        fireEvent.change(screen.getByPlaceholderText('Confirm Password'), { target: { value: 'password123' } });

        fireEvent.click(screen.getByText('Signup'));

        await waitFor(() => {
            const errorElement = screen.getByText((content) => content.includes('SignUp failed'));
            expect(errorElement).toBeInTheDocument();
            expect(errorElement.parentElement).toHaveStyle({ display: 'block' });
        });

        act(() => {
            jest.advanceTimersByTime(3000);
        });

        await waitFor(() => {
            expect(screen.queryByText((content) => content.includes('SignUp failed'))).toBeInTheDocument();
        });
    });

    it('should handle password visibility toggle correctly', () => {
        render(
            <Router>
                <Signup />
            </Router>
        );

        const passwordInput = screen.getByPlaceholderText('Password');
        const confirmPasswordInput = screen.getByPlaceholderText('Confirm Password');

        const togglePasswordButton = screen.getByLabelText('toggle password visibility');
        const toggleConfirmPasswordButton = screen.getByLabelText('toggle confirm password visibility');

        expect(passwordInput).toHaveAttribute('type', 'password');
        expect(confirmPasswordInput).toHaveAttribute('type', 'password');

        fireEvent.click(togglePasswordButton);
        expect(passwordInput).toHaveAttribute('type', 'text');

        fireEvent.click(toggleConfirmPasswordButton);
        expect(confirmPasswordInput).toHaveAttribute('type', 'text');

        fireEvent.click(togglePasswordButton);
        fireEvent.click(toggleConfirmPasswordButton);
        expect(passwordInput).toHaveAttribute('type', 'password');
        expect(confirmPasswordInput).toHaveAttribute('type', 'password');
    });

    it('should display and clear error message after timeout', async () => {
        jest.useFakeTimers();

        const errorMessage = 'Test error message';
        axiosInstance.post.mockRejectedValue({
            response: {
                data: { message: errorMessage }
            }
        });

        render(
            <Router>
                <Signup />
            </Router>
        );

        fireEvent.change(screen.getByPlaceholderText('First Name'), { target: { value: 'John' } });
        fireEvent.change(screen.getByPlaceholderText('Last Name'), { target: { value: 'Doe' } });
        fireEvent.change(screen.getByPlaceholderText('Username'), { target: { value: 'johndoe' } });
        fireEvent.change(screen.getByPlaceholderText('Password'), { target: { value: 'password123' } });
        fireEvent.change(screen.getByPlaceholderText('Confirm Password'), { target: { value: 'password123' } });

        fireEvent.click(screen.getByText('Signup'));

        await waitFor(() => {
            const errorElement = screen.getByText((content) =>
                content.includes('SignUp failed')
            );
            expect(errorElement).toBeInTheDocument();
        });

        act(() => {
            jest.advanceTimersByTime(3000);
        });

        await waitFor(() => {
            expect(screen.queryByText((content) =>
                content.includes('SignUp failed')
            )).not.toBeInTheDocument();
        });

        jest.useRealTimers();
    });

    it('should maintain password visibility state correctly', () => {
        render(
            <Router>
                <Signup />
            </Router>
        );

        const passwordInput = screen.getByPlaceholderText('Password');
        const toggleButton = screen.getByLabelText('toggle password visibility');

        expect(passwordInput).toHaveAttribute('type', 'password');

        fireEvent.click(toggleButton);
        expect(passwordInput).toHaveAttribute('type', 'text');

        fireEvent.click(toggleButton);
        expect(passwordInput).toHaveAttribute('type', 'password');
    });

    it('should handle multiple error states correctly', async () => {
        jest.useFakeTimers();

        const firstError = 'First test error';
        const secondError = 'Second test error';

        axiosInstance.post
            .mockRejectedValueOnce({ response: { data: { message: firstError } } })
            .mockRejectedValueOnce({ response: { data: { message: secondError } } });

        render(
            <Router>
                <Signup />
            </Router>
        );

        fireEvent.change(screen.getByPlaceholderText('First Name'), { target: { value: 'John' } });
        fireEvent.change(screen.getByPlaceholderText('Last Name'), { target: { value: 'Doe' } });
        fireEvent.change(screen.getByPlaceholderText('Username'), { target: { value: 'johndoe' } });
        fireEvent.change(screen.getByPlaceholderText('Password'), { target: { value: 'password123' } });
        fireEvent.change(screen.getByPlaceholderText('Confirm Password'), { target: { value: 'password123' } });

        fireEvent.click(screen.getByText('Signup'));

        await waitFor(() => {
            expect(screen.getByText((content) =>
                content.includes(firstError)
            )).toBeInTheDocument();
        });

        act(() => {
            jest.advanceTimersByTime(1500);
        });

        fireEvent.click(screen.getByText('Signup'));

        await waitFor(() => {
            expect(screen.getByText((content) =>
                content.includes(secondError)
            )).toBeInTheDocument();
        });

        act(() => {
            jest.advanceTimersByTime(3000);
        });

        await waitFor(() => {
            expect(screen.queryByText((content) =>
                content.includes(secondError)
            )).not.toBeInTheDocument();
        });

        jest.useRealTimers();
    });

    it('should show loading spinner during form submission', async () => {
        axiosInstance.post.mockImplementation(() =>
            new Promise(resolve => setTimeout(() =>
                resolve({ data: { message: 'Success' } }), 1000)
            )
        );

        render(
            <Router>
                <Signup />
            </Router>
        );

        fireEvent.change(screen.getByPlaceholderText('First Name'), { target: { value: 'John' } });
        fireEvent.change(screen.getByPlaceholderText('Last Name'), { target: { value: 'Doe' } });
        fireEvent.change(screen.getByPlaceholderText('Username'), { target: { value: 'johndoe' } });
        fireEvent.change(screen.getByPlaceholderText('Password'), { target: { value: 'password123' } });
        fireEvent.change(screen.getByPlaceholderText('Confirm Password'), { target: { value: 'password123' } });

        fireEvent.click(screen.getByText('Signup'));

        await waitFor(() => {
            expect(screen.getByRole('progressbar')).toBeInTheDocument();
        });

        await waitFor(() => {
            expect(mockNavigate).toHaveBeenCalledWith('/login');
        });

        expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
    });
});
