import { Box, CircularProgress, IconButton, Tooltip } from "@mui/material";
import StarBorderIcon from "@mui/icons-material/StarBorder";
import StarIcon from "@mui/icons-material/Star";
import DownloadIcon from "@mui/icons-material/Download";
import { Share as ShareIcon } from "@mui/icons-material";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import React from "react";

const HelperIcons = ({
    isFavourite,
    isFavouriteLoading = false,
    isDownloading = false,
    removeFavourite,
    addFavourite,
    toggleShare,
    downloadArtifact,
    handleFullscreenOpen,
    showFullscreenIconForMap,
    containerStyle = {},
}) => {
    return (
        <Box
            sx={{
                position: "absolute",
                top: 8,
                right: 8,
                display: "flex",
                flexDirection: "column",
                gap: 1,
                zIndex: 1000,
                ...containerStyle,
            }}
        >
            {removeFavourite !== undefined && addFavourite !== undefined && (
                <Tooltip title={isFavourite ? "Remove from favorites" : "Add to favorites"} arrow placement="left">
                    <IconButton
                        onClick={(e) => {
                            e.stopPropagation();
                            isFavourite ? removeFavourite() : addFavourite();
                        }}
                        sx={{
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: isFavourite ? "yellow" : "white",
                            backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent black background
                            borderRadius: "50%", // Makes the background round
                            "&:hover": {
                                backgroundColor: "rgba(0, 0, 0, 0.7)", // Slightly darker/less transparent on hover
                            },
                            "&.Mui-disabled": {
                                backgroundColor: "rgba(0, 0, 0, 0.5)",
                            },
                        }}
                        disableRipple
                        disabled={isFavouriteLoading}
                    >
                        {isFavouriteLoading ? (
                            <CircularProgress sx={{ color: "white" }} size={18} />
                        ) : isFavourite ? (
                            <StarIcon sx={{ height: 18 }} />
                        ) : (
                            <StarBorderIcon sx={{ height: 18 }} />
                        )}
                    </IconButton>
                </Tooltip>
            )}
            {toggleShare !== undefined && (
                <Tooltip title="Share this event" arrow placement="left">
                    <IconButton
                        className="icon-button"
                        onClick={(e) => toggleShare(e)}
                        sx={{
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent black background
                            borderRadius: "50%", // Makes the background round
                            "&:hover": {
                                backgroundColor: "rgba(0, 0, 0, 0.7)", // Slightly darker/less transparent on hover
                            },
                        }}
                    >
                        <ShareIcon sx={{ height: 18 }} />
                    </IconButton>
                </Tooltip>
            )}
            {!isDownloading && downloadArtifact !== undefined && (
                <Tooltip title="Download" arrow placement="left">
                    <IconButton
                        onClick={(e) => {
                            e.stopPropagation();
                            downloadArtifact();
                        }}
                        sx={{
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent black background
                            borderRadius: "50%", // Makes the background round
                            "&:hover": {
                                backgroundColor: "rgba(0, 0, 0, 0.7)", // Slightly darker/less transparent on hover
                            },
                        }}
                    >
                        <DownloadIcon sx={{ height: 18 }} />
                    </IconButton>
                </Tooltip>
            )}
            {isDownloading && (
                <CircularProgress
                    sx={{
                        fontSize: 18,
                        color: "white",
                        filter: "drop-shadow(0px 2px 3px rgba(0,0,0,0.5))",
                    }}
                />
            )}
            {handleFullscreenOpen !== undefined && showFullscreenIconForMap && (
                <Tooltip title="View fullscreen" arrow placement="left">
                    <IconButton
                        onClick={handleFullscreenOpen}
                        sx={{
                            height: 27,
                            width: 27,
                            padding: 0,
                            color: "#fff",
                            backgroundColor: "rgba(0, 0, 0, 0.5)", // Semi-transparent black background
                            borderRadius: "50%", // Makes the background round
                            "&:hover": {
                                backgroundColor: "rgba(0, 0, 0, 0.7)", // Slightly darker/less transparent on hover
                            },
                        }}
                    >
                        <FullscreenIcon sx={{ height: 18 }} />
                    </IconButton>
                </Tooltip>
            )}
        </Box>
    );
};

export default HelperIcons;
