const statisticsList = [
    {
        "_id": "670e59416d2582802dcdab4c",
        "stats": {
            "totalVesselsDetected": 34,
            "totalVesselsDetectedbySensors": {
                "prototype-37": 34
            },
            "totalVesselsSuperCategorized": {
                "Fishing": 17,
                "Pleasure Craft": 15,
                "Military": 1,
                "Special Craft": 1
            },
            "totalVesselsSubCategorized": {
                "Rowboat": 1,
                "Outrigger Canoe": 1,
                "Banca": 7,
                "Fishing Boat": 1,
                "Rigid Inflatable Boat": 1,
                "Speedboat": 1,
                "Patrol Boat": 1,
                "Fishing Vessel": 2,
                "Sinking Vessel": 1,
                "Sailing Boat": 2,
                "Small Boat": 3,
                "Gondola": 1,
                "Rigid Inflatable Boat (RIB)": 1,
                "Dhow": 1,
                "Canoe": 2,
                "Traditional Boat": 1,
                "Bangka": 1
            },
            "totalSmartmastsAtSea": 0,
            "totalSmartmastsOnline": 3
        },
        "fromTimestamp": "2024-10-14T21:00:00.000Z",
        "toTimestamp": "2024-10-15T12:00:00.000Z",
        "type": "daily",
        "__v": 0
    },
    {
        "_id": "670d5c2cbc6f7a464e65ba7f",
        "stats": {
            "totalVesselsDetected": 19,
            "totalVesselsDetectedbySensors": {
                "prototype-37": 19
            },
            "totalVesselsSuperCategorized": {
                "Pleasure Craft": 3,
                "Fishing": 3,
                "Military": 1
            },
            "totalVesselsSubCategorized": {
                "Banca": 4,
                "Small Boat": 2,
                "Patrol Boat": 1
            },
            "totalSmartmastsAtSea": 0,
            "totalSmartmastsOnline": 3
        },
        "fromTimestamp": "2024-10-13T21:00:00.000Z",
        "toTimestamp": "2024-10-14T12:00:00.000Z",
        "type": "daily",
        "__v": 0
    },
    {
        "_id": "670d5c2dbc6f7a464e65ba81",
        "stats": {
            "totalArtifactsWithAtleastOneVessel": {
                "confidenceAbove40": 96,
                "confidenceAbove80": 66
            },
            "totalVesselsSuperCategorized": {
                "Pleasure Craft": 33,
                "Special Craft": 7,
                "Military": 8,
                "Tugs": 2,
                "Cargo": 14,
                "Passenger": 3,
                "Fishing": 10
            },
            "totalVesselsSubCategorized": {
                "Inflatable Boat": 9,
                "Rigid Inflatable Boat": 3,
                "Submarine": 4,
                "Small Boat": 11,
                "Rigid Inflatable Boat (RIB)": 1,
                "Tug": 1,
                "Bulk Carrier": 1,
                "Tugboat": 1,
                "Ferry": 2,
                "Container Ship": 3,
                "Banca": 6,
                "Canoe": 3,
                "Kayak": 4,
                "Bangka": 2,
                "Motorboat": 2,
                "Fishing Vessel": 1,
                "Outrigger Canoe": 1,
                "Coast Guard Vessel": 1,
                "Patrol Boat": 1
            },
            "listOfTextsExtracted": [
                "KRAKEN",
                "007",
                "073",
                "73",
                "4411",
                "COAST GUARD",
                "YAMAHA",
                "SMART",
                "PHILIPPINE COAST GUARD",
                "INDIA PEACE 445",
                "BRP Tarlac",
                "FERRY",
                "BATANGAS",
                "MSC",
                "WILSON",
                "ANTWERP",
                "MAERSK",
                "LINE",
                "A",
                "dmg",
                "PHIL",
                "B- 8",
                "PAL-8AL-061927-JT215",
                "PHILIPPINE",
                "GUARD",
                "PUNTA CANA"
            ],
            "totalVesselsWithCountryFlag": 1,
            "totalVesselsDetectedbySensors": {
                "prototype-37": 87,
                "prototype-33": 31
            },
            "totalVesselsByHoursUTC": {
                "0": 2,
                "1": 5,
                "2": 6,
                "3": 8,
                "4": 7,
                "5": 5,
                "6": 2,
                "7": 27,
                "8": 24,
                "9": 18,
                "10": 5,
                "11": 0,
                "12": 0,
                "13": 0,
                "14": 0,
                "15": 0,
                "16": 0,
                "17": 0,
                "18": 0,
                "19": 0,
                "20": 0,
                "21": 2,
                "22": 3,
                "23": 4
            },
            "totalVesselsByWeekDayHoursUTC": {
                "0": {
                    "0": 1,
                    "1": 0,
                    "2": 0,
                    "3": 2,
                    "4": 2,
                    "5": 4,
                    "6": 0,
                    "7": 2,
                    "8": 1,
                    "9": 1,
                    "10": 1,
                    "11": 0,
                    "12": 0,
                    "13": 0,
                    "14": 0,
                    "15": 0,
                    "16": 0,
                    "17": 0,
                    "18": 0,
                    "19": 0,
                    "20": 0,
                    "21": 0,
                    "22": 0,
                    "23": 0
                },
                "1": {
                    "0": 0,
                    "1": 0,
                    "2": 0,
                    "3": 0,
                    "4": 0,
                    "5": 0,
                    "6": 0,
                    "7": 0,
                    "8": 0,
                    "9": 0,
                    "10": 0,
                    "11": 0,
                    "12": 0,
                    "13": 0,
                    "14": 0,
                    "15": 0,
                    "16": 0,
                    "17": 0,
                    "18": 0,
                    "19": 0,
                    "20": 0,
                    "21": 0,
                    "22": 0,
                    "23": 0
                },
                "2": {
                    "0": 0,
                    "1": 0,
                    "2": 0,
                    "3": 0,
                    "4": 0,
                    "5": 0,
                    "6": 0,
                    "7": 1,
                    "8": 0,
                    "9": 1,
                    "10": 0,
                    "11": 0,
                    "12": 0,
                    "13": 0,
                    "14": 0,
                    "15": 0,
                    "16": 0,
                    "17": 0,
                    "18": 0,
                    "19": 0,
                    "20": 0,
                    "21": 0,
                    "22": 0,
                    "23": 1
                },
                "3": {
                    "0": 0,
                    "1": 1,
                    "2": 5,
                    "3": 1,
                    "4": 0,
                    "5": 0,
                    "6": 2,
                    "7": 5,
                    "8": 1,
                    "9": 0,
                    "10": 1,
                    "11": 0,
                    "12": 0,
                    "13": 0,
                    "14": 0,
                    "15": 0,
                    "16": 0,
                    "17": 0,
                    "18": 0,
                    "19": 0,
                    "20": 0,
                    "21": 0,
                    "22": 0,
                    "23": 0
                },
                "4": {
                    "0": 0,
                    "1": 4,
                    "2": 1,
                    "3": 4,
                    "4": 1,
                    "5": 0,
                    "6": 0,
                    "7": 14,
                    "8": 0,
                    "9": 0,
                    "10": 0,
                    "11": 0,
                    "12": 0,
                    "13": 0,
                    "14": 0,
                    "15": 0,
                    "16": 0,
                    "17": 0,
                    "18": 0,
                    "19": 0,
                    "20": 0,
                    "21": 0,
                    "22": 0,
                    "23": 0
                },
                "5": {
                    "0": 0,
                    "1": 0,
                    "2": 0,
                    "3": 0,
                    "4": 0,
                    "5": 0,
                    "6": 0,
                    "7": 2,
                    "8": 22,
                    "9": 14,
                    "10": 1,
                    "11": 0,
                    "12": 0,
                    "13": 0,
                    "14": 0,
                    "15": 0,
                    "16": 0,
                    "17": 0,
                    "18": 0,
                    "19": 0,
                    "20": 0,
                    "21": 2,
                    "22": 1,
                    "23": 1
                },
                "6": {
                    "0": 1,
                    "1": 0,
                    "2": 0,
                    "3": 1,
                    "4": 4,
                    "5": 1,
                    "6": 0,
                    "7": 3,
                    "8": 0,
                    "9": 2,
                    "10": 2,
                    "11": 0,
                    "12": 0,
                    "13": 0,
                    "14": 0,
                    "15": 0,
                    "16": 0,
                    "17": 0,
                    "18": 0,
                    "19": 0,
                    "20": 0,
                    "21": 0,
                    "22": 2,
                    "23": 2
                }
            },
            "totalSensorsDurationAtSea": {
                "prototype-32": 0,
                "unit-3": 16876989,
                "prototype-24": 0,
                "prototype-25": 0,
                "prototype-36": 0,
                "prototype-37": *********,
                "prototype-33": 0
            },
            "totalSensorsOnlineDuration": {
                "prototype-32": 76088059,
                "unit-3": 16876989,
                "prototype-24": 0,
                "prototype-25": 0,
                "prototype-36": 0,
                "prototype-37": *********,
                "prototype-33": *********
            },
            "totalSmartmastsDistanceTraveled": {
                "prototype-32": 0,
                "unit-3": 6098.265904928738,
                "prototype-24": 0,
                "prototype-25": 0,
                "prototype-36": 0,
                "prototype-37": 1248595.*********,
                "prototype-33": 0
            }
        },
        "fromTimestamp": "2024-10-06T16:00:00.000Z",
        "toTimestamp": "2024-10-13T15:59:59.999Z",
        "type": "weekly",
        "__v": 0
    }
]

module.exports = {
    statisticsList
}