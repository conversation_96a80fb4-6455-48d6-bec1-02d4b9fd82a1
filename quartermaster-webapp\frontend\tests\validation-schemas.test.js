import { inviteUserSchema } from '../src/validation-schemas';
import * as yup from 'yup';

describe('inviteUserSchema', () => {
    it('should validate a valid user input', async () => {
        const validInput = {
            email: '<EMAIL>',
            role_id: 1
        };

        await expect(inviteUserSchema.isValid(validInput)).resolves.toBe(true);
    });

    it('should return an error when the name is missing', async () => {
        const invalidInput = {
            role_id: 1
        };

        await expect(inviteUserSchema.isValid(invalidInput)).resolves.toBe(false);
        try {
            await inviteUserSchema.validate(invalidInput, { abortEarly: false });
        } catch (err) {
            const errors = err.inner.map(e => e.message);
            expect(errors).toContain('Email is required');
        }
    });

    it('should return an error when role_id is missing or invalid', async () => {
        const invalidInput = {
            name: '<PERSON>',
            username: 'johndo<PERSON>',
            email: 'johndo<PERSON>@example.com',
            password: 'password123',
            confirmPassword: 'password123',
            role_id: null
        };

        await expect(inviteUserSchema.isValid(invalidInput)).resolves.toBe(false);
        try {
            await inviteUserSchema.validate(invalidInput, { abortEarly: false });
        } catch (err) {
            const errors = err.inner.map(e => e.message);
            expect(errors).toContain('Role is required');
        }
    });
});
