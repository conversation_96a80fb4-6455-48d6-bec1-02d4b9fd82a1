import { useState, useEffect } from "react";
import { Grid, TextField, Button, Modal } from "@mui/material";
import ModalContainer from "../../../components/ModalContainer";
import apiKeyController from "../../../controllers/ApiKey.controller";

const EditDetailsModal = ({ apiKey, showEditModal, setShowEditModal, setUpdating, fetchKeys }) => {
    const [email, setEmail] = useState("");
    const [description, setDescription] = useState("");
    const [emailError, setEmailError] = useState("");
    const [descriptionError, setDescriptionError] = useState("");
    const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

    useEffect(() => {
        if (apiKey) {
            setEmail(apiKey?.email || "");
            setDescription(apiKey?.description || "");
        }
    }, [apiKey]);

    const handleClose = () => {
        setShowEditModal(false);
        setEmail("");
        setDescription("");
        setEmailError("");
        setDescriptionError("");
        setHasAttemptedSubmit(false);
        setUpdating(null);
    };

    const validateEmail = (email) => {
        if (!email) return "";
        if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email)) {
            return "Please enter a valid email address";
        }
        return "";
    };

    const validateDescription = (description) => {
        if (!description || description.trim() === "") {
            return "Description is required";
        }
        return "";
    };

    const handleEmailChange = (e) => {
        const value = e.target.value;
        setEmail(value);
        if (hasAttemptedSubmit) {
            setEmailError(validateEmail(value));
        }
    };

    const handleDescriptionChange = (e) => {
        const value = e.target.value;
        setDescription(value);
        if (hasAttemptedSubmit) {
            setDescriptionError(validateDescription(value));
        }
    };

    const onUpdate = async () => {
        setHasAttemptedSubmit(true);
        const emailValidationError = validateEmail(email);
        const descriptionValidationError = validateDescription(description);

        setEmailError(emailValidationError);
        setDescriptionError(descriptionValidationError);

        if (emailValidationError || descriptionValidationError) {
            return;
        }

        try {
            setUpdating(apiKey._id);
            await apiKeyController.update({
                id: apiKey._id,
                description,
                ...(email.trim() !== "" && { email }),
            });
            fetchKeys();
            handleClose();
        } catch (error) {
            console.error("Error updating API key:", error);
        } finally {
            setUpdating(null);
        }
    };

    const hasDescriptionChanged = description !== (apiKey?.description || "");
    const hasEmailChanged = email !== (apiKey?.email || "");
    const hasChanged = hasDescriptionChanged || hasEmailChanged;
    const isUpdateDisabled = !hasChanged || !!emailError || !!descriptionError;

    return (
        <Modal open={showEditModal} onClose={handleClose}>
            <ModalContainer title={"Update API Key Details"} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2}>
                    <Grid>
                        <TextField
                            value={description}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleDescriptionChange}
                            label="Description"
                            variant="filled"
                            required
                            error={!!descriptionError}
                            helperText={descriptionError}
                        />
                    </Grid>
                    <Grid>
                        <TextField
                            value={email}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleEmailChange}
                            label="Email (optional)"
                            variant="filled"
                            type="email"
                            error={!!emailError}
                            helperText={emailError}
                        />
                    </Grid>
                    <Grid justifyContent={"center"} display={"flex"}>
                        <Button variant="contained" onClick={onUpdate} disabled={isUpdateDisabled}>
                            Update
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default EditDetailsModal;
