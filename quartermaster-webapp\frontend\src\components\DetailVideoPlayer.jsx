import { PauseCircleOutline, PlayArrowOutlined } from "@mui/icons-material";
import { I<PERSON><PERSON><PERSON><PERSON>, Slider } from "@mui/material";
import { useRef, useState, useEffect } from "react";
// import FullscreenIcon from "@mui/icons-material/Fullscreen";

function DetailVideoPlayer({ src, style, onLoadedData, onCurrentTimeChange, currentTime = 0, fullscreenOpen = false, isInFullScreen = false }) {
    const videoRef = useRef(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [progress, setProgress] = useState(0); // 0–100
    const [duration, setDuration] = useState(0);
    // const [volume, setVolume] = useState(1); // 0–1

    // Update progress as the video plays
    useEffect(() => {
        const video = videoRef.current;
        if (!video) return;
        if (currentTime) {
            video.currentTime = currentTime;
        }
        if (isInFullScreen) {
            video.play();
            setIsPlaying(true);
        }
        const onTimeUpdate = () => {
            setProgress((video.currentTime / video.duration) * 100);
            setDuration(video.duration);
            onCurrentTimeChange(video.currentTime);
            if (video.currentTime === video.duration) {
                setIsPlaying(false);
            }
        };
        video.addEventListener("timeupdate", onTimeUpdate);
        return () => video.removeEventListener("timeupdate", onTimeUpdate);
    }, []);

    const togglePlay = () => {
        const video = videoRef.current;
        if (!video) return;
        if (video.paused) {
            video.play();
            setIsPlaying(true);
        } else {
            video.pause();
            setIsPlaying(false);
        }
    };

    useEffect(() => {
        if (!isInFullScreen) {
            const video = videoRef.current;
            if (!video) return;
            if (!video.paused && fullscreenOpen) {
                video.pause();
                setIsPlaying(false);
            } else if (currentTime > 0 && video.paused && !fullscreenOpen) {
                //auto play the video when the video gets back from fullscreen
                //prevent the video from playing at start
                video.play();
                video.currentTime = currentTime;
                setIsPlaying(true);
            }
        }
    }, [fullscreenOpen]);

    const handleProgressChange = (e) => {
        const video = videoRef.current;
        const newProgress = +e.target.value;
        video.currentTime = (newProgress / 100) * video.duration;
        setProgress(newProgress);
    };

    // const handleVolumeChange = (e) => {
    //     const newVol = +e.target.value;
    //     videoRef.current.volume = newVol;
    //     setVolume(newVol);
    // };

    return (
        <div style={{ position: "relative", width: "100%", ...style }}>
            {/* hide native controls */}
            <video
                ref={videoRef}
                src={src}
                preload="auto"
                style={{ width: "100%", height: "100%", objectFit: "cover" }}
                onClick={(e) => e.stopPropagation()}
                onLoadedData={onLoadedData}
            />

            {/* custom controls overlay */}
            <div
                style={{
                    position: "absolute",
                    bottom: 0,
                    left: 0,
                    right: 0,
                    background: "rgba(0,0,0,0.3)",
                    color: "#fff",
                    display: "flex",
                    alignItems: "center",
                    paddingRight: "12px",
                }}
            >
                {/* Play/Pause */}
                <IconButton sx={{ paddingY: 0 }} onClick={togglePlay}>
                    {isPlaying ? <PauseCircleOutline /> : <PlayArrowOutlined />}
                </IconButton>

                {/* Progress Bar */}
                <Slider
                    value={progress}
                    onChange={handleProgressChange}
                    // valueLabelDisplay="auto"
                    valueLabelFormat={(value) => Math.ceil((value / 100) * duration) + "s"}
                    sx={{
                        "&.MuiSlider-root": { height: 2 },
                        "& .MuiSlider-thumb": { height: 10, width: 10 },
                        "& .MuiSlider-thumb::after": { content: "none" },
                    }}
                />

                {/* <IconButton>
                    <FullscreenIcon sx={{ height: 18 }} />
                </IconButton> */}
            </div>
        </div>
    );
}

export default DetailVideoPlayer;
