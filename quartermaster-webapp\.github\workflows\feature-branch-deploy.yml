name: Feature Branch Deployment

on:
  push:
    branches:
      - 'QMW-*'

jobs:
  validate-and-deploy:
    runs-on: MahsamMacbook
    permissions:
      contents: read
      actions: read
      statuses: write
    
    steps:
      - name: Extract branch name
        id: extract_branch
        run: echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_ENV

      - name: Validate branch name
        run: |
          if [[ ! ${{ env.BRANCH_NAME }} =~ ^QMW-[0-9]+$ ]]; then
            echo "Branch name must match pattern QMW-{number}"
            exit 1
          fi

      - name: Create deployment directory
        run: |
          DEPLOY_DIR="$HOME/${{ env.BRANCH_NAME }}.quartermaster.us"
          echo "DEPLOY_DIR=$DEPLOY_DIR" >> $GITHUB_ENV
          
          # Create directory if it doesn't exist
          if [ ! -d "$DEPLOY_DIR" ]; then
            mkdir -p "$DEPLOY_DIR"
          fi

      - name: Clone repository
        run: |
          cd "${{ env.DEPLOY_DIR }}"
          if [ ! -d ".git" ]; then
            git clone https://<EMAIL>/${{ github.repository }}.git .
          fi
          git fetch origin
          git checkout ${{ env.BRANCH_NAME }}
          git pull origin ${{ env.BRANCH_NAME }}

      - name: Setup backend
        run: |
          cd "${{ env.DEPLOY_DIR }}"
          npm ci
          rm -f .env
          echo "${{ secrets.DEV_QUARTERMASTER_US }}" > .env

      - name: Setup frontend
        run: |
          cd "${{ env.DEPLOY_DIR }}/frontend"
          npm ci
          rm -f .env
          echo "${{ secrets.DEV_QUARTERMASTER_US_FRONTEND }}" > .env
          npm run build

      # - name: Configure Nginx
      #   run: |
      #     BRANCH_NAME=${{ env.BRANCH_NAME }}
      #     PORT=$(pm2 list | grep "Quartermaster $BRANCH_NAME" | awk '{print $2}' | cut -d':' -f2 || echo "3000")
          
      #     # Create Nginx configuration
      #     sudo tee "/etc/nginx/conf.d/$BRANCH_NAME.quartermaster.us.conf" > /dev/null << EOF
      #     server {
      #         listen 80;
      #         server_name $BRANCH_NAME.quartermaster.us;
              
      #         location / {
      #             proxy_pass http://localhost:$PORT;
      #             proxy_http_version 1.1;
      #             proxy_set_header Upgrade \$http_upgrade;
      #             proxy_set_header Connection 'upgrade';
      #             proxy_set_header Host \$host;
      #             proxy_cache_bypass \$http_upgrade;
      #         }
      #     }
      #     EOF
          
      #     # Test and reload Nginx
      #     sudo nginx -t && sudo systemctl reload nginx

      - name: Start/restart application
        run: |
          cd "${{ env.DEPLOY_DIR }}"
          if pm2 list | grep -q "Quartermaster ${{ env.BRANCH_NAME }}"; then
            pm2 restart "Quartermaster ${{ env.BRANCH_NAME }}"
          else
            pm2 start npm --name "Quartermaster ${{ env.BRANCH_NAME }}" -- start
          fi

      - name: Notify deployment status
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: workflow,job,commit,repo,ref,author,took
          custom_payload: |
            {
              "attachments": [
                {
                  "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                  "text": `\nFeature Branch Deployment: ${{ env.BRANCH_NAME }}\nURL: https://${{ env.BRANCH_NAME }}.quartermaster.us\nStatus: ${{ job.status }}\nCommit: (${process.env.AS_COMMIT}) by ${process.env.AS_AUTHOR}`
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }} 