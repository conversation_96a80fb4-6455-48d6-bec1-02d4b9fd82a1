import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material';
import UserManagement from '../../src/pages/Dashboard/User/UserManagement';
import { useUser } from '../../src/hooks/UserHook';
import { permissions } from '../../src/utils';
import { useApp } from '../../src/hooks/AppHook';

jest.mock('../../src/hooks/UserHook', () => ({
    useUser: jest.fn(),
}));
jest.mock('../../src/hooks/AppHook', () => ({
    useApp: jest.fn().mockReturnValue({ isMobile: false }),
}));
jest.mock('../../src/pages/Dashboard/User/Users', () => () => <div>Users Component</div>);
jest.mock('../../src/pages/Dashboard/User/Roles/Roles', () => () => <div>Roles Component</div>);

const theme = createTheme();

describe('UserManagement Component', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should render correctly when user has permission for both tabs', async () => {
        useUser.mockReturnValue({
            user: {
                hasPermissions: (permissionsList) =>
                    permissionsList.includes(permissions.manageUsers) ||
                    permissionsList.includes(permissions.manageRoles),
            },
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );

        expect(screen.getByText('Users Component')).toBeInTheDocument();
        expect(screen.getByText('Roles Component')).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: /Users/<USER>
        expect(screen.getByRole('tab', { name: /Roles/i })).toBeInTheDocument();
    });

    it('should render only the "Users" tab when user has permission for users only', async () => {
        useUser.mockReturnValue({
            user: {
                hasPermissions: (permissionsList) =>
                    permissionsList.includes(permissions.manageUsers),
            },
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );

        expect(screen.queryByText('Users Component')).toBeInTheDocument();
        expect(screen.queryByText('Roles Component')).not.toBeInTheDocument();
        expect(screen.getByRole('tab', { name: /Users/<USER>
        expect(screen.queryByRole('tab', { name: /Roles/i })).not.toBeInTheDocument();
    });

    it('should render only the "Roles" tab when user has permission for roles only', async () => {
        useUser.mockReturnValue({
            user: {
                hasPermissions: (permissionsList) =>
                    permissionsList.includes(permissions.manageRoles),
            },
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );

        expect(screen.queryByText('Users Component')).not.toBeInTheDocument();
        expect(screen.queryByText('Roles Component')).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: /Roles/i })).toBeInTheDocument();
        expect(screen.queryByRole('tab', { name: /Users/<USER>
    });

    it('should display the correct component when a tab is selected', async () => {
        useUser.mockReturnValue({
            user: {
                hasPermissions: (permissionsList) =>
                    permissionsList.includes(permissions.manageUsers) ||
                    permissionsList.includes(permissions.manageRoles),
            },
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );

        fireEvent.click(screen.getByRole('tab', { name: /Roles/i }));
        expect(screen.getByText('Roles Component')).toBeInTheDocument();
    });

    it('should not render the component if no permissions are available', async () => {
        useUser.mockReturnValue({
            user: {
                hasPermissions: () => false,
            },
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );

        expect(screen.queryByText('Users Component')).not.toBeInTheDocument();
        expect(screen.queryByText('Roles Component')).not.toBeInTheDocument();
        expect(screen.queryByRole('tab', { name: /Users/<USER>
        expect(screen.queryByRole('tab', { name: /Roles/i })).not.toBeInTheDocument();
    });

    it('should handle search input changes', () => {
        useUser.mockReturnValue({
            user: {
                hasPermissions: () => true
            }
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );

        const searchInput = screen.getByPlaceholderText('Search');
        fireEvent.change(searchInput, { target: { value: 'test search' } });
        expect(searchInput).toHaveValue('test search');
    });

    it('should handle user filter modal', () => {
        useUser.mockReturnValue({
            user: { hasPermissions: () => true }
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );

        const filterButton = screen.getAllByText('Filter')[0];
        fireEvent.click(filterButton);

        expect(screen.getByText('Users Component')).toBeInTheDocument();
    });

    it('should handle role actions (add, reorder, filter)', () => {
        useUser.mockReturnValue({
            user: { hasPermissions: () => true }
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );

        fireEvent.click(screen.getByRole('tab', { name: /Roles/i }));

        const addButton = screen.getByText('Add New Role');
        fireEvent.click(addButton);

        const reorderButton = screen.getByText('Reorder Roles');
        fireEvent.click(reorderButton);

        const filterButton = screen.getByText('Filter');
        fireEvent.click(filterButton);

        expect(screen.getByText('Roles Component')).toBeInTheDocument();
    });

    it('should handle mobile view', () => {
        useApp.mockReturnValueOnce({ isMobile: true });

        useUser.mockReturnValue({
            user: { hasPermissions: () => true }
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );

        expect(screen.queryByText('Filter')).toBeInTheDocument();
        expect(screen.queryByText('Add New Role')).not.toBeInTheDocument();
        expect(screen.queryByText('Reorder Roles')).not.toBeInTheDocument();
    });

    it('should handle add user action', () => {
        useApp.mockReturnValueOnce({ isMobile: false });
        useUser.mockReturnValue({
            user: { hasPermissions: () => true }
        });

        render(
            <MemoryRouter>
                <ThemeProvider theme={theme}>
                    <UserManagement />
                </ThemeProvider>
            </MemoryRouter>
        );
        const addUserButton = screen.getByText('Invite User');
        fireEvent.click(addUserButton);

        expect(screen.getByText('Users Component')).toBeInTheDocument();
    });
});
