#!/usr/bin/env sh

# List of branches that are protected
BRANCHES_TO_PROTECT="main staging dev"

# Get the current branch name
CURRENT_BRANCH=$(git symbolic-ref --short HEAD)

# Check if the current branch is in the protected list
for BRANCH in $BRANCHES_TO_PROTECT; do
  if [ "$CURRENT_BRANCH" = "$BRANCH" ]; then
    echo "You cannot commit directly to $BRANCH branch. Please create a pull request."
    exit 1
  fi
done

# Run lint-staged to lint and format staged files
# npx lint-staged