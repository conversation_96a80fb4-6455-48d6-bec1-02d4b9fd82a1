import { useState } from "react";
import axiosInstance from "../../../../axios";
import { Button, Grid, Modal, TextField } from "@mui/material";
import ModalContainer from "../../../../components/ModalContainer";

const AddRoleModal = ({ showAddRole, setShowAddRole, setAdding, onSuccess }) => {
    const [roleName, setRoleName] = useState("");

    const handleClose = () => {
        setShowAddRole(false);
        setRoleName("");
    };

    const onAdd = () => {
        handleClose();
        setAdding(true);
        axiosInstance
            .post("/roles", { role_name: roleName }, { meta: { showSnackbar: true } })
            .then(() => {
                onSuccess && onSuccess();
                setRoleName("");
            })
            .catch(console.error)
            .finally(() => setAdding(false));
    };

    return (
        <Modal open={showAddRole} onClose={handleClose}>
            <ModalContainer title={"Add New Role"} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: 500 }}>
                    <Grid>
                        <TextField value={roleName} onChange={(e) => setRoleName(e.target.value)} label="Name" variant="filled" fullWidth />
                    </Grid>
                    <Grid justifyContent={"center"} display={"flex"}>
                        <Button disabled={!roleName} variant="contained" onClick={onAdd}>
                            Submit
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default AddRoleModal;
