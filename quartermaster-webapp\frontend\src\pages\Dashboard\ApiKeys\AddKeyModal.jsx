import { useState } from "react";
import { Grid, TextField, Button, Modal } from "@mui/material";
import ModalContainer from "../../../components/ModalContainer";
import apiKeyController from "../../../controllers/ApiKey.controller";

const AddKeyModal = ({ showAddKey, setShowAddKey, setAdding, fetchKeys }) => {
    const [description, setDescription] = useState("");
    const [email, setEmail] = useState("");
    const [emailError, setEmailError] = useState("");
    const [hasAttemptedSubmit, setHasAttemptedSubmit] = useState(false);

    const handleClose = () => {
        setShowAddKey(false);
        setDescription("");
        setEmail("");
        setEmailError("");
        setHasAttemptedSubmit(false);
    };

    const validateEmail = (email) => {
        if (email && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(email)) {
            return "Please enter a valid email address";
        }
        return "";
    };

    const handleEmailChange = (e) => {
        const value = e.target.value;
        setEmail(value);
        if (hasAttemptedSubmit) {
            setEmailError(validateEmail(value));
        }
    };

    const onAdd = async () => {
        try {
            setHasAttemptedSubmit(true);
            const emailValidationError = validateEmail(email);
            if (emailValidationError) {
                setEmailError(emailValidationError);
                return;
            }

            setAdding(true);
            await apiKeyController.create({ description, ...(email && { email }) });
            fetchKeys();
            setDescription("");
            setEmail("");
            handleClose();
        } catch (error) {
            console.error("Error creating API key:", error);
        } finally {
            setAdding(false);
        }
    };

    return (
        <Modal open={showAddKey} onClose={handleClose}>
            <ModalContainer title={"Create API Key"} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2}>
                    <Grid>
                        <TextField
                            value={description}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={(e) => setDescription(e.target.value)}
                            label="Description"
                            variant="filled"
                            required
                        />
                    </Grid>
                    <Grid>
                        <TextField
                            value={email}
                            sx={{ minWidth: { xs: 250, sm: 500 } }}
                            onChange={handleEmailChange}
                            label="Email (optional)"
                            variant="filled"
                            type="email"
                            error={!!emailError}
                            helperText={emailError}
                        />
                    </Grid>
                    <Grid justifyContent={"center"} display={"flex"}>
                        <Button disabled={!description || !!emailError} variant="contained" onClick={onAdd}>
                            Submit
                        </Button>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default AddKeyModal;
