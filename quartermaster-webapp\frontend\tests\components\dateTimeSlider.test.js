import { render, screen, fireEvent } from '@testing-library/react';
import DateTimeSlider from '../../src/components/DateTimeSlider';
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

jest.mock('../../src/utils', () => ({
    defaultValues: {
        timezone: 'UTC',
        dateTimeFormat: jest.fn(({ exclude_seconds }) =>
            exclude_seconds ? 'YYYY-MM-DD HH:mm' : 'YYYY-MM-DD HH:mm:ss'
        ),
    },
}));

describe('DateTimeSlider', () => {
    const mockOnChangeCommitted = jest.fn();
    const range = [1633046400000, 1633132800000];
    const defaultProps = {
        key: 'test-slider',
        range,
        onChangeCommitted: mockOnChangeCommitted,
        steps: 60000,
        minWidth: 200,
        size: 'small',
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('renders the DateTimeSlider correctly', () => {
        render(
            <ThemeProvider theme={theme}>
                <DateTimeSlider {...defaultProps} />
            </ThemeProvider>
        );

        const sliders = screen.getAllByRole('slider');
        expect(sliders.length).toBe(2);
    });
});
