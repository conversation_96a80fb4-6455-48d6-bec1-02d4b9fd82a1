name: Feature Branch Cleanup

on:
  delete:

jobs:
  debug:
    runs-on: MahsamMacbook
    steps:
      - name: Debug Information
        run: |
          echo "Event Name: ${{ github.event_name }}"
          echo "Ref Type: ${{ github.event.ref_type }}"
          echo "Ref: ${{ github.ref }}"
          echo "Full Event: ${{ toJSON(github.event) }}"

  cleanup:
    if: github.event.ref_type == 'branch' && startsWith(github.ref, 'refs/heads/QMW-')
    runs-on: MahsamMacbook
    permissions:
      contents: read
      actions: read
      statuses: write
    
    steps:
      - name: Extract branch name
        id: extract_branch
        run: echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_ENV

      - name: Validate branch name
        run: |
          if [[ ! ${{ env.BRANCH_NAME }} =~ ^QMW-[0-9]+$ ]]; then
            echo "Branch name must match pattern QMW-{number}"
            exit 1
          fi

      - name: Stop PM2 process
        run: |
          if pm2 list | grep -q "Quartermaster ${{ env.BRANCH_NAME }}"; then
            pm2 delete "Quartermaster ${{ env.BRANCH_NAME }}"
          fi

      - name: Remove Nginx configuration
        run: |
          sudo rm -f "/etc/nginx/conf.d/${{ env.BRANCH_NAME }}.quartermaster.us.conf"
          sudo nginx -t && sudo systemctl reload nginx

      - name: Remove deployment directory
        run: |
          rm -rf "$HOME/${{ env.BRANCH_NAME }}.quartermaster.us"

      - name: Notify cleanup status
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          fields: workflow,job,repo,ref
          custom_payload: |
            {
              "attachments": [
                {
                  "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                  "text": `\nFeature Branch Cleanup: ${{ env.BRANCH_NAME }}\nStatus: ${{ job.status }}`
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }} 