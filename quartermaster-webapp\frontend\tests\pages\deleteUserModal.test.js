import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import DeleteUserModal from "../../src/pages/Dashboard/User/DeleteUserModal";
import axiosInstance from "../../src/axios";

jest.mock("../../src/axios", () => ({
    delete: jest.fn()
}));

describe("DeleteUserModal", () => {
    const mockSetDeleteUser = jest.fn();
    const mockSetDeleting = jest.fn();
    const mockOnSuccess = jest.fn();

    const mockUser = { _id: "user1", name: "User One" };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it("renders the modal with delete user name", () => {
        render(
            <DeleteUserModal
                deleteUser={mockUser}
                setDeleteUser={mockSetDeleteUser}
                setDeleting={mockSetDeleting}
                onSuccess={mockOnSuccess}
            />
        );

        expect(screen.getByText(/Are you sure you want to delete user "User One"?/i)).toBeInTheDocument();
    });

    it("calls setDeleteUser to close the modal when clicking Cancel", () => {
        render(
            <DeleteUserModal
                deleteUser={mockUser}
                setDeleteUser={mockSetDeleteUser}
                setDeleting={mockSetDeleting}
                onSuccess={mockOnSuccess}
            />
        );

        fireEvent.click(screen.getByText("Cancel"));

        expect(mockSetDeleteUser).toHaveBeenCalled();
    });

    it("calls onDelete and axios.delete when clicking Delete", async () => {
        render(
            <DeleteUserModal
                deleteUser={mockUser}
                setDeleteUser={mockSetDeleteUser}
                setDeleting={mockSetDeleting}
                onSuccess={mockOnSuccess}
            />
        );
        axiosInstance.delete.mockResolvedValueOnce({});
        fireEvent.click(screen.getByText("Delete"));

        await waitFor(() => expect(axiosInstance.delete).toHaveBeenCalled());
    });

    it("does not render modal when deleteUser is null or undefined", () => {
        render(
            <DeleteUserModal
                deleteUser={null}
                setDeleteUser={mockSetDeleteUser}
                setDeleting={mockSetDeleting}
                onSuccess={mockOnSuccess}
            />
        );

        expect(screen.queryByText(/Are you sure you want to delete user/i)).not.toBeInTheDocument();
    });
});
