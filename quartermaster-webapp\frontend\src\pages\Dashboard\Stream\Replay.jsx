import { <PERSON>, Button, Grid, Typography } from "@mui/material";
import React, { useState } from "react";
import { subMinutes } from "date-fns";
import CustomDateTimePicker from "../../../components/CustomDateTimePicker";
import dayjs from "dayjs";
import { userValues } from "../../../utils.js";
import { useUser } from "../../../hooks/UserHook.jsx";
// import utc from 'dayjs/plugin/utc'
// import timezone from 'dayjs/plugin/timezone'

// dayjs.extend(utc)
// dayjs.extend(timezone)

const Replay = ({ handleReplayTime, onMenuUpdate, setCustomReplaySelect }) => {
    const { user } = useUser();
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const replays = [
        { title: "Last 1 Hour", interval: 60, time: 60, tag: "1H", marksDuartion: 2 },
        { title: "Last 6 Hours", interval: 360, time: 360, tag: "6H", marksDuartion: 12 },
        { title: "Last 12 Hours", interval: 720, time: 720, tag: "12H", marksDuartion: 23 },
        { title: "Last 24 Hours", interval: 1440, time: 1440, tag: "24H", marksDuartion: 45 },
        { title: "Last 7 Days", interval: 10080, time: 10080, tag: "7D", marksDuartion: 315 },
        { title: "Last 30 Days", interval: 43200 - 180, time: 43200 - 180, tag: "30D", marksDuartion: 1350 }, // Reduced 3 hours from 30 days to resolve a potential retention period issue
    ];

    const replayHours = (replay) => {
        handleReplayTime(replay);
        setCustomReplaySelect(0);
    };

    const handleCustomReplay = () => {
        if (fromDate && toDate && dayjs(toDate).diff(dayjs(fromDate), "minute") <= 43200 - 180) {
            const fromMinutes = dayjs().diff(dayjs(fromDate), "minute");
            const toMinutes = dayjs().diff(dayjs(toDate), "minute");
            setCustomReplaySelect(toMinutes);
            handleReplayTime({
                interval: fromMinutes - toMinutes,
                time: fromMinutes - toMinutes,
                toDate,
                tag: `${dayjs(fromDate).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))} to ${dayjs(toDate).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}`,
            });
            onMenuUpdate(null);
        }
    };

    const minDateTime = dayjs(subMinutes(new Date(), 43200 - 180));
    const maxDateTime = dayjs(new Date());

    return (
        <Box
            className="dashboard-step-13"
            sx={{
                minWidth: { xs: "0", md: "380px" },
                width: "100%",
                position: "relative",
            }}
        >
            {replays.map((replay, index) => (
                <Grid
                    key={index}
                    container
                    flexDirection={"row"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    sx={{
                        display: "flex",
                        marginTop: "5px",
                        marginBottom: "5px",
                        borderBottom: "1px solid #282C39",
                        color: "#FFFFFF",
                        paddingY: 1,
                        paddingX: 2,
                    }}
                >
                    <Grid>
                        <Typography fontWeight={400} fontSize={"14px"}>
                            {replay.title}
                        </Typography>
                    </Grid>
                    <Grid>
                        <Button
                            variant="contained"
                            onClick={() => replayHours(replay)}
                            sx={{
                                fontSize: "12px",
                                padding: "5px 10px",
                                borderRadius: "4px",
                            }}
                        >
                            View Replay
                        </Button>
                    </Grid>
                </Grid>
            ))}
            <Grid
                container
                flexDirection={"column"}
                justifyContent={"space-between"}
                gap={2}
                sx={{ display: "flex", marginTop: "5px", marginBottom: "5px", color: "#FFFFFF", paddingY: 1, paddingX: 2 }}
            >
                <Grid>
                    <Grid container color={"#FFFFFF"} gap={1}>
                        <Grid container flexDirection={"column"} justifyContent={"center"} size="grow">
                            <Grid display={"flex"}>
                                <Typography
                                    textAlign={"center"}
                                    justifyContent={"center"}
                                    color={(theme) => theme.palette.custom.mediumGrey}
                                    fontSize={"16px"}
                                >
                                    From
                                </Typography>
                            </Grid>
                            <Grid>
                                <CustomDateTimePicker
                                    label="From Date"
                                    value={fromDate}
                                    onChange={(newValue) => setFromDate(newValue)}
                                    minDateTime={minDateTime}
                                    maxDateTime={maxDateTime}
                                />
                            </Grid>
                        </Grid>
                        <Grid container flexDirection={"column"} size="grow">
                            <Grid display={"flex"}>
                                <Typography
                                    textAlign={"center"}
                                    justifyContent={"center"}
                                    color={(theme) => theme.palette.custom.mediumGrey}
                                    fontSize={"16px"}
                                >
                                    To
                                </Typography>
                            </Grid>
                            <Grid>
                                <CustomDateTimePicker
                                    label="To Date"
                                    value={toDate}
                                    onChange={(newValue) => setToDate(newValue)}
                                    minDateTime={fromDate || minDateTime}
                                    maxDateTime={maxDateTime}
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
                <Grid display={"flex"} justifyContent={"center"} size="grow">
                    <Button
                        variant="contained"
                        onClick={handleCustomReplay}
                        disabled={!(fromDate && toDate)}
                        fullWidth
                        sx={{
                            fontSize: "12px",
                            padding: "5px 10px",
                            borderRadius: "4px",
                        }}
                    >
                        View Custom Replay
                    </Button>
                </Grid>
            </Grid>
        </Box>
    );
};

export default Replay;
