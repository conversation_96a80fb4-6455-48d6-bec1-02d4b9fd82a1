import { render, screen, fireEvent } from '@testing-library/react';
import Appbar from '../../src/layouts/Appbar';
import { useApp } from '../../src/hooks/AppHook';
import { version } from '../../../package.json';

jest.mock('../../src/hooks/AppHook');
jest.mock('../../src/layouts/ProfileMenu', () => ({ avatarOnly }) => (
    <div data-testid="profile-menu" data-avatar-only={avatarOnly}>ProfileMenu</div>
));
jest.mock('@mui/icons-material/Menu', () => () => 'MenuIcon');

describe('AppBar', () => {
    const mockSetDrawerOpen = jest.fn();

    beforeEach(() => {
        jest.clearAllMocks();
    });

    const renderAppbar = (screenSize = {}) => {
        useApp.mockReturnValue({
            screenSize: {
                xs: false,
                sm: false,
                md: false,
                lg: true,
                ...screenSize
            }
        });

        return render(<Appbar setDrawerOpen={mockSetDrawerOpen} />);
    };

    it('should show menu button on mobile', () => {
        renderAppbar({ xs: true });
        const menuButton = screen.getByRole('button');

        fireEvent.click(menuButton);
        expect(mockSetDrawerOpen).toHaveBeenCalledWith(true);
    });

    it('should render mobile layout with correct logo size', () => {
        renderAppbar({ xs: true });
        const logo = screen.getAllByAltText('Quartermaster Logo')[1];
        expect(logo).toHaveAttribute('width', '180');
    });

    it('should show avatar-only ProfileMenu on mobile', () => {
        renderAppbar({ xs: true });
        const profileMenu = screen.getByTestId('profile-menu');
        expect(profileMenu).toHaveAttribute('data-avatar-only', 'true');
    });

    it('should render desktop layout with correct logo size', () => {
        renderAppbar({ lg: true });
        const logo = screen.getAllByAltText('Quartermaster Logo')[1];
        expect(logo).toHaveAttribute('width', '230');
    });

    it('should show full ProfileMenu on desktop', () => {
        renderAppbar({ lg: true });
        const profileMenu = screen.getByTestId('profile-menu');
        expect(profileMenu).toHaveAttribute('data-avatar-only', 'false');
    });

    it('should adapt to tablet view', () => {
        renderAppbar({ md: true });
        expect(screen.getByTestId('profile-menu')).toHaveAttribute('data-avatar-only', 'true');
    });

    it('should render both logos', () => {
        renderAppbar();
        const logos = screen.getAllByAltText('Quartermaster Logo');
        expect(logos).toHaveLength(2);
        expect(logos[0]).toHaveAttribute('src', '/quartermaster-logo-white.svg');
        expect(logos[1]).toHaveAttribute('src', '/quartermaster-text-white.svg');
    });

    it('should display correct version number', () => {
        renderAppbar();
        expect(screen.getByText(`v${version}`)).toBeInTheDocument();
    });

    it('should handle menu button click', () => {
        renderAppbar({ xs: true });
        fireEvent.click(screen.getByRole('button'));
        expect(mockSetDrawerOpen).toHaveBeenCalledWith(true);
        expect(mockSetDrawerOpen).toHaveBeenCalledTimes(1);
    });
});
