const sessionLogsList = [
    {
        "_id": "66e07be04c55798738bd1c88",
        "socket_id": "ybESml9L0ANGKxmqAAAF",
        "type": "connect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T17:03:28.789Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "<PERSON><PERSON><PERSON>",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e07c784c55798738bd1cbe",
        "socket_id": "JwQwASoshrqg9FBvAAAN",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T17:06:00.596Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e07e844c55798738bd1cdb",
        "socket_id": "JwQwASoshrqg9FBvAAAN",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T17:14:44.094Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e07e844c55798738bd1cdd",
        "socket_id": "ybESml9L0ANGKxmqAAAF",
        "type": "disconnect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T17:14:44.998Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e07ed04c55798738bd1ce3",
        "socket_id": "QYjcW-CpfdUteyYCAAAR",
        "type": "connect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T17:16:00.709Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e07f0d4c55798738bd1d06",
        "socket_id": "QYjcW-CpfdUteyYCAAAR",
        "type": "disconnect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T17:17:01.100Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e08e264c55798738bd1d0c",
        "socket_id": "nrPTAHUOUSQSXTaVAAAX",
        "type": "connect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T18:21:26.918Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e0926b4c55798738bd1d23",
        "socket_id": "nrPTAHUOUSQSXTaVAAAX",
        "type": "disconnect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T18:39:39.991Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e092724c55798738bd1d29",
        "socket_id": "A9f19L40dkPQMcGNAAAb",
        "type": "connect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T18:39:46.724Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e0938b4c55798738bd1d52",
        "socket_id": "A9f19L40dkPQMcGNAAAb",
        "type": "disconnect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-10T18:44:27.994Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e183e96d2fea58d1dbd5ef",
        "socket_id": "VuGc4d_cKq-Jii9kAAAD",
        "type": "connect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T11:50:01.253Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e184836d2fea58d1dbd605",
        "socket_id": "VuGc4d_cKq-Jii9kAAAD",
        "type": "disconnect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T11:52:35.485Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e187006d2fea58d1dbd60f",
        "socket_id": "Vl4i_gg_EIfcnlCQAAAH",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T12:03:12.080Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e189e26d2fea58d1dbd62f",
        "socket_id": "HtMxWwS-BMdTDyXQAAAL",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T12:15:30.372Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e18d046d2fea58d1dbd64d",
        "socket_id": "Vl4i_gg_EIfcnlCQAAAH",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T12:28:52.895Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e18eb16d2fea58d1dbd651",
        "socket_id": "HtMxWwS-BMdTDyXQAAAL",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T12:36:01.788Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e19fc66d2fea58d1dbd662",
        "socket_id": "UGmIG7EPflPjzQ3sAAAP",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T13:48:54.548Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a72fa1e5d00403bbd8cc",
        "socket_id": "5OOrnTETvx1RS5r7AAAB",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:20:31.835Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a736a1e5d00403bbd8d4",
        "socket_id": "5OOrnTETvx1RS5r7AAAB",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:20:38.039Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a740a1e5d00403bbd8da",
        "socket_id": "8x5gesv5Z5J_A0nkAAAF",
        "type": "connect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:20:48.153Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a742a1e5d00403bbd8ed",
        "socket_id": "SV-jTZTIMFDJP0t_AAAH",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:20:50.541Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a761a1e5d00403bbd905",
        "socket_id": "SV-jTZTIMFDJP0t_AAAH",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:21:21.533Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a76fa1e5d00403bbd91c",
        "socket_id": "9YEkgwJUWZtqOqJpAAAL",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:21:35.454Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a76fa1e5d00403bbd922",
        "socket_id": "8x5gesv5Z5J_A0nkAAAF",
        "type": "disconnect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:21:35.990Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a786a1e5d00403bbd941",
        "socket_id": "_vrgpRKBaAa6IcI5AAAP",
        "type": "connect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:21:58.412Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a787a1e5d00403bbd952",
        "socket_id": "9YEkgwJUWZtqOqJpAAAL",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:21:59.324Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a7aea1e5d00403bbd96c",
        "socket_id": "lVkF48Ct4L5qIeZmAAAV",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:22:38.413Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1a7afa1e5d00403bbd971",
        "socket_id": "_vrgpRKBaAa6IcI5AAAP",
        "type": "disconnect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:22:39.166Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1aa3da1e5d00403bbd996",
        "socket_id": "iN0DUHPxs54GkDbcAAAb",
        "type": "connect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T14:33:33.161Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bba6a1e5d00403bbd9bb",
        "socket_id": "AtU_CpCHbVJyY45CAAAf",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T15:47:50.144Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bba6a1e5d00403bbd9c7",
        "socket_id": "lVkF48Ct4L5qIeZmAAAV",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T15:47:50.718Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bba7a1e5d00403bbd9cc",
        "socket_id": "iN0DUHPxs54GkDbcAAAb",
        "type": "disconnect",
        "device": "Windows 10",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T15:47:51.164Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1beeba1e5d00403bbd9e5",
        "socket_id": "vGhfLMMnGsXfuYrxAAAn",
        "type": "connect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:01:47.435Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bf8da1e5d00403bbd9ff",
        "socket_id": "vGhfLMMnGsXfuYrxAAAn",
        "type": "disconnect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:04:29.276Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bf8da1e5d00403bbda01",
        "socket_id": "AtU_CpCHbVJyY45CAAAf",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:04:29.281Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bf8ea1e5d00403bbda03",
        "socket_id": "IM71HtZ_1XavSVjsAAAz",
        "type": "connect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:04:30.159Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bf90a1e5d00403bbda05",
        "socket_id": "MBXekiMkYW61AIj7AAA0",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:04:32.261Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bf9ba1e5d00403bbda0b",
        "socket_id": "IM71HtZ_1XavSVjsAAAz",
        "type": "disconnect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:04:43.420Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bf9da1e5d00403bbda0d",
        "socket_id": "LjeO4u0faL4GqF73AAA2",
        "type": "connect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:04:45.248Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bf9da1e5d00403bbda12",
        "socket_id": "OjWtE9VB7xS4V7lcAAA4",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:04:45.808Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bfa2a1e5d00403bbda22",
        "socket_id": "OjWtE9VB7xS4V7lcAAA4",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:04:50.511Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bfa3a1e5d00403bbda29",
        "socket_id": "v_jZ0XoMNmkEbQ5AAAA6",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:04:51.755Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bfa5a1e5d00403bbda36",
        "socket_id": "LjeO4u0faL4GqF73AAA2",
        "type": "disconnect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:04:53.565Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bfa6a1e5d00403bbda3d",
        "socket_id": "EqB8Q9gLQ6owZkZ5AAA8",
        "type": "connect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:04:54.390Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bfc9a1e5d00403bbda51",
        "socket_id": "MBXekiMkYW61AIj7AAA0",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:05:29.370Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1bfc9a1e5d00403bbda53",
        "socket_id": "v_jZ0XoMNmkEbQ5AAAA6",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:05:29.638Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1c01b8d254a4ea24d7d8d",
        "socket_id": "QBGzlx1tCL88szB2AAAB",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:06:51.267Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1c01c8d254a4ea24d7d8f",
        "socket_id": "Sf2Gn7WfTBWvJmJHAAAD",
        "type": "connect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:06:52.092Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1c1308d254a4ea24d7d93",
        "socket_id": "QBGzlx1tCL88szB2AAAB",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:11:28.397Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1c1368d254a4ea24d7d9f",
        "socket_id": "m8TxRQ2_Ye8MKodmAAAH",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:11:34.036Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1c2cb8d254a4ea24d7db3",
        "socket_id": "Sf2Gn7WfTBWvJmJHAAAD",
        "type": "disconnect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:18:19.643Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1c2cb8d254a4ea24d7db5",
        "socket_id": "m8TxRQ2_Ye8MKodmAAAH",
        "type": "disconnect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:18:19.651Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1c2cb8d254a4ea24d7db7",
        "socket_id": "_G2i6-vISC7S4kQKAAAT",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66b38701d207b4ef9ceea276",
        "timestamp": "2024-09-11T16:18:19.833Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66b38701d207b4ef9ceea276",
            "name": "Ahmed Okasha",
            "email": "<EMAIL>",
            "username": "ahmed",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1c2d08d254a4ea24d7dbb",
        "socket_id": "MhY7yZa2DQREqADRAAAV",
        "type": "connect",
        "device": "Mac OS 10.15.7",
        "browser": "Chrome",
        "user_id": "6695a511e084e4d023c17535",
        "timestamp": "2024-09-11T16:18:24.170Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "6695a511e084e4d023c17535",
            "name": "Neil Sobin",
            "email": "<EMAIL>",
            "username": "neil",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    },
    {
        "_id": "66e1c2e58d254a4ea24d7dc9",
        "socket_id": "ppUeu1l-Zbr0qOs_AAAX",
        "type": "connect",
        "device": "Linux x86_64",
        "browser": "Chrome",
        "user_id": "66942a54a7f848634a00990a",
        "timestamp": "2024-09-11T16:18:45.135Z",
        "environment": "release",
        "__v": 0,
        "user": {
            "_id": "66942a54a7f848634a00990a",
            "name": "Mahsam Abbas",
            "email": "<EMAIL>",
            "username": "mahsam",
            "role_id": 1,
            "creation_timestamp": "2024-09-03T19:04:20.000Z",
            "deletable": false,
            "is_deleted": false
        }
    }
]

module.exports = {
    sessionLogsList
}