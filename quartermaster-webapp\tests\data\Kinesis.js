const streamsList = [
    {
        "DeviceName": null,
        "StreamName": "prototype-24",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-24/1724069687123",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "ww1ljAmwqPscFzFHDxRO",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:14:47.123Z",
        "DataRetentionInHours": 1,
        "Tags": {},
        "IsLive": false
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-25",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-25/1724069706666",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "xGPWabBw6AS63YRyFOzh",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:15:06.666Z",
        "DataRetentionInHours": 1,
        "Tags": {},
        "IsLive": false
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-32",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-32/1724069756053",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "bj1nRPzYoTNugPhjeomk",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:15:56.053Z",
        "DataRetentionInHours": 720,
        "Tags": {
            "Name": "BRP Malapascua MRRV-4403",
            "Thumbnail": "https://portal.quartermaster.us/4403.jpg"
        },
        "IsLive": false
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-33",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-33/1724069779670",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "VoYAl4N1oHPfWR9QQI8h",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:16:19.670Z",
        "DataRetentionInHours": 720,
        "Tags": {
            "Name": "BRP Teresa Magbanua MRRV-9701",
            "Thumbnail": "https://portal.quartermaster.us/9701.jpeg"
        },
        "IsLive": true
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-36",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-36/1724069813231",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "0VagnvNF87ywfD71vMgo",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:16:53.231Z",
        "DataRetentionInHours": 720,
        "Tags": {
            "Name": "BRP Cabra MRRV-4409",
            "Thumbnail": "https://portal.quartermaster.us/4409.jpeg"
        },
        "IsLive": false
    },
    {
        "DeviceName": null,
        "StreamName": "prototype-37",
        "StreamARN": "arn:aws:kinesisvideo:ap-southeast-1:369445629693:stream/prototype-37/1724069836072",
        "MediaType": null,
        "KmsKeyId": "arn:aws:kms:ap-southeast-1:369445629693:alias/aws/kinesisvideo",
        "Version": "Jn67enedwnagUh69RKkN",
        "Status": "ACTIVE",
        "CreationTime": "2024-08-19T12:17:16.072Z",
        "DataRetentionInHours": 720,
        "Tags": {
            "Name": "BRP Cape Engaño MRRV-4411",
            "Thumbnail": "https://portal.quartermaster.us/4411.jpeg"
        },
        "IsLive": true
    }
]

module.exports = { streamsList }