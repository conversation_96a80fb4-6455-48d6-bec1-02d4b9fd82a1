import { Grid, MenuItem, TextField } from "@mui/material";
import { ExpandMore, ExpandLess } from "@mui/icons-material";
import theme from "../../../theme";

const Filters = ({ category, setCategory, categories }) => {
    return (
        <Grid container gap={2} alignItems={"center"}>
            <Grid>
                <TextField
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    variant="standard"
                    InputProps={{
                        sx: {
                            height: "40px",
                        },
                        inputProps: {
                            sx: {
                                fontSize: "12px",
                                paddingLeft: 1,
                                color: "primary.contrastText",
                            },
                        },
                    }}
                    select
                    SelectProps={{
                        MenuProps: {
                            PaperProps: {
                                sx: {
                                    "& .MuiMenuItem-root": {
                                        display: "flex",
                                        alignItems: "center",
                                    },
                                    "& .MuiList-root": {
                                        padding: 0,
                                    },
                                },
                            },
                        },
                        IconComponent: (props) =>
                            category ? (
                                <ExpandMore {...props} sx={{ right: "10px !important" }} />
                            ) : (
                                <ExpandLess {...props} sx={{ right: "10px !important" }} />
                            ),
                    }}
                    sx={{
                        minWidth: 150,
                        height: "30px",
                        backgroundColor: "#464F59",
                        borderRadius: "5px",
                        "& .MuiInputBase-root": {
                            fontSize: "16px",
                            border: "none",
                            paddingX: "10px",
                            "&::after, &::before": {
                                borderBottom: "none !important",
                            },
                        },
                        "& .MuiSelect-select": {
                            borderBottom: "none",
                            paddingY: 0,
                        },
                        "& .MuiInputBase-root::before": {
                            border: 0,
                        },
                    }}
                >
                    {categories.map((type, i) => (
                        <MenuItem
                            key={i}
                            value={type}
                            sx={{
                                borderBottom: i < categories.length - 1 ? `1px solid ${theme.palette.custom.borderColor}` : "none",
                                padding: "10px 20px",
                                color: "white",
                                "&.Mui-selected": {
                                    backgroundColor: "rgba(255, 255, 255, 0.08)",
                                },
                                "&:hover": {
                                    backgroundColor: "rgba(255, 255, 255, 0.12)",
                                },
                            }}
                        >
                            {type}
                        </MenuItem>
                    ))}
                </TextField>
            </Grid>
        </Grid>
    );
};

export default Filters;
