import { forwardRef, useEffect, useImperativeHandle, useState } from "react";
import { alpha, Grid, Typography, Tooltip } from "@mui/material";
import { useApp } from "../hooks/AppHook";
import theme from "../theme";

const disabledStyle = { pointerEvents: "none", opacity: 0.8 };

// eslint-disable-next-line react/display-name
const CustomSwitch = forwardRef(
    ({ initialState, textOn, textOff, iconOn, iconOff, action, icon = false, disabled = false, tooltip = undefined }, ref) => {
        const [isChecked, setIsChecked] = useState(initialState);
        const [isDisabled, setIsDisabled] = useState(disabled);
        const { screenSize } = useApp();

        const handleToggle = () => {
            setIsChecked((prev) => !prev);
            if (action) {
                action(isChecked);
            }
        };

        useImperativeHandle(ref, () => ({
            toggle: handleToggle,
            setDisabled: (state) => {
                setIsDisabled(state);
            },
        }));

        useEffect(() => {
            setIsChecked(initialState);
        }, [initialState]);

        return (
            <div style={isDisabled ? disabledStyle : {}}>
                <Tooltip title={tooltip}>
                    <Grid
                        container
                        onClick={handleToggle}
                        sx={{
                            alignItems: "center",
                            cursor: "pointer",
                            backgroundColor: alpha(theme.palette.background.default, 0.2),
                            borderRadius: "25px",
                            justifyContent: "space-between",
                            gap: { lg: "10px", xs: "5px" },
                            "&:hover": {
                                ".switch-circle": {
                                    backgroundColor: "primary.contrastText",
                                },
                                ".switch-text": {
                                    color: "primary.contrastText",
                                },
                            },
                        }}
                    >
                        <Grid sx={{ flex: 1, textAlign: "center", ml: { lg: "15px", xs: "10px" } }}>
                            <Typography
                                sx={{
                                    color: theme.palette.custom.mediumGrey,
                                    fontSize: { sm: "12px", xs: "8px" },
                                    fontWeight: 400,
                                    transition: "all 0.3s ease",
                                }}
                                className="switch-text"
                            >
                                {isChecked ? textOn : textOff}
                            </Typography>
                        </Grid>
                        <Grid>
                            <Grid
                                sx={{
                                    width: { sm: "40px", xs: "25px" },
                                    height: { sm: "40px", xs: "25px" },
                                    borderRadius: "50%",
                                    backgroundColor: theme.palette.custom.mediumGrey,
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    transition: "all 0.3s ease",
                                }}
                                className="switch-circle"
                            >
                                {icon ? (
                                    isChecked ? (
                                        iconOn
                                    ) : (
                                        iconOff
                                    )
                                ) : (
                                    <img src={isChecked ? iconOn : iconOff} alt="Switch Icon" width={screenSize.xs ? "15px" : "27px"} />
                                )}
                            </Grid>
                        </Grid>
                    </Grid>
                </Tooltip>
            </div>
        );
    },
);

export default CustomSwitch;
