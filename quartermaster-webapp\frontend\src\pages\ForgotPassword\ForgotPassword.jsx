import { useState } from "react";
import { <PERSON><PERSON>ield, Button, Typography, Link, Grid, CircularProgress } from "@mui/material";
import axiosInstance from "../../axios";

const ForgotPassword = () => {
    const [email, setEmail] = useState("");
    const [message, setMessage] = useState("");
    const [messageColor, setMessageColor] = useState("error");
    const [submitting, setSubmitting] = useState(false);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setMessage("");
        setSubmitting(true);
        try {
            const response = await axiosInstance.post("/users/forgot-password", { email }, { meta: { showSnackbar: false } });
            setMessageColor(response.status === 200 ? "success.main" : "error");
            setMessage(response.data.message);
            setSubmitting(false);
        } catch (err) {
            setMessageColor("error");
            setMessage(err.response.data.message);
            setSubmitting(false);
        }
    };

    return (
        <Grid container flexDirection={"column"} maxWidth={591} gap={4}>
            <Grid container flexDirection={"column"} color={"#FFFFFF"}>
                <Grid>
                    <Typography variant="h3" fontWeight={"bold"}>{`Forgot Password`}</Typography>
                </Grid>
            </Grid>
            <Grid container flexDirection={"column"} component={"form"} onSubmit={handleSubmit} gap={4}>
                <Grid>
                    <TextField
                        className="input-login"
                        type="email"
                        placeholder="Enter your email"
                        variant="outlined"
                        fullWidth
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        required
                    />
                </Grid>
                <Grid display={message ? "block" : "none"}>
                    <Typography color={messageColor}>{message}</Typography>
                </Grid>
                <Grid>
                    <Button
                        className="btn-login"
                        type="submit"
                        variant="contained"
                        color="primary"
                        fullWidth
                        disabled={submitting}
                        endIcon={submitting && <CircularProgress />}
                    >
                        Send Reset Link
                    </Button>
                </Grid>
            </Grid>
            <Grid color={"#FFFFFF"}>
                <Typography fontSize="18px" lineHeight="30px" fontWeight={"light"}>
                    Remembered your password?{" "}
                    <Link
                        href={"/login"}
                        color="#FFFFFF"
                        fontWeight="bold"
                        sx={{ textDecoration: "none", ":hover": { textDecoration: "underline" } }}
                    >
                        Login
                    </Link>
                </Typography>
            </Grid>
        </Grid>
    );
};

export default ForgotPassword;
