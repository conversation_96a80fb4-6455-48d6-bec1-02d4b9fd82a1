import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { AppProvider } from '../../src/providers/AppProvider';
import { useApp } from '../../src/hooks/AppHook';
import { useJsApiLoader } from '@react-google-maps/api';
import { useMediaQuery } from '@mui/material';
import idb from '../../src/indexedDB';
import { useTheme } from '@emotion/react';

jest.mock('@react-google-maps/api', () => ({
    useJsApiLoader: jest.fn(),
}));

jest.mock('@emotion/react', () => ({
    useTheme: jest.fn().mockReturnValue({
        breakpoints: {
            down: jest.fn(),
        }
    }),
}));

jest.mock('@mui/material', () => ({
    useMediaQuery: jest.fn(),
}));

jest.mock('../../src/indexedDB', () => ({
    clearIndexedDB: jest.fn(),
}));

jest.mock('../../environment', () => ({
    VITE_GOOGLE_MAPS_API_KEY: 'mock-api-key',
}));

jest.mock('../../package.json', () => ({
    version: '1.0.0',
}));

const TestingComponent = () => {
    const { region, setRegion, selectedVessel, setSelectedVessel, deviceHeight, isMobile } = useApp();

    return (
        <div>
            <p data-testid="region">{region}</p>
            <p data-testid="selected-vessel">{selectedVessel}</p>
            <p data-testid="device-height">{deviceHeight}</p>
            <p data-testid="is-mobile">{isMobile ? 'true' : 'false'}</p>
            <button onClick={() => setRegion('us-east-1')}>Set Region</button>
            <button onClick={() => setSelectedVessel('Vessel 1')}>Select Vessel</button>
        </div>
    );
};

describe('AppProvider', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        window.innerHeight = 800;
        useJsApiLoader.mockReturnValue({ isLoaded: true });
        useMediaQuery.mockReturnValue(false);
    });

    it('should detect screen size', async () => {
        useMediaQuery.mockImplementation((query) => {
            if (query === 'screen and (max-width:600px)') return true;
            return false;
        });

        render(
            <AppProvider>
                <TestingComponent />
            </AppProvider>
        );

        expect(screen.getByTestId('is-mobile').textContent).toBe('false');
    });

    it('should update deviceHeight on window resize', async () => {
        useMediaQuery.mockReturnValue(true);
        render(
            <AppProvider>
                <TestingComponent />
            </AppProvider>
        );

        expect(screen.getByTestId('device-height').textContent).toBe('800');

        window.innerHeight = 600;
        fireEvent.resize(window);

        await waitFor(() => expect(screen.getByTestId('device-height').textContent).toBe('600'));
    });

    it('should update region when button is clicked', async () => {
        render(
            <AppProvider>
                <TestingComponent />
            </AppProvider>
        );

        expect(screen.getByTestId('region').textContent).toBe('ap-southeast-1');

        fireEvent.click(screen.getByText('Set Region'));

        await waitFor(() => expect(screen.getByTestId('region').textContent).toBe('us-east-1'));
    });

    it('should update selected vessel when button is clicked', async () => {
        render(
            <AppProvider>
                <TestingComponent />
            </AppProvider>
        );

        expect(screen.getByTestId('selected-vessel').textContent).toBe('');

        fireEvent.click(screen.getByText('Select Vessel'));

        await waitFor(() => expect(screen.getByTestId('selected-vessel').textContent).toBe('Vessel 1'));
    });
});
