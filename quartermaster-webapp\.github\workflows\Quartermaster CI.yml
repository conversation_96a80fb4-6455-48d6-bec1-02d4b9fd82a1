name: Quartermaster CI

on:
  push:
    branches: [ "main", "dev", "staging" ]

jobs:
  build:

    runs-on: quartermaster-ec2
    permissions:
      contents: read
      actions: read
      statuses: write
    strategy:
      matrix:
        node-version: [18.x ]
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

##############################################################################################
################################### Staging CI ###############################################
##############################################################################################

    - name:
        Deploy to staging.quartermaster.us - Navigate to Directory, Reset and Pull Changes
      if: github.ref == 'refs/heads/staging'
      run: |
        echo "Deploying to staging.quartermaster.us"
        cd ~/staging.quartermaster.us/
        git restore .
        git pull

    - name:
        Deploy to staging.quartermaster.us - Install Backend Dependencies
      if: github.ref == 'refs/heads/staging'
      run: |
        cd ~/staging.quartermaster.us/
        npm install

    - name:
        Deploy to staging.quartermaster.us - Install Frontend Dependencies
      if: github.ref == 'refs/heads/staging'
      run: |
        cd ~/staging.quartermaster.us/frontend/
        npm install

    - name:
        Deploy to staging.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/staging'
      run: |
        cd ~/staging.quartermaster.us/frontend/
        rm -rf .env
        touch .env

    - name:
        Deploy to staging.quartermaster.us - Populate Frontend .env File with Secrets
      if: github.ref == 'refs/heads/staging'
      run: |
        cd ~/staging.quartermaster.us/frontend/
        echo "${{ secrets.STAGING_QUARTERMASTER_US_FRONTEND }}" > .env

    - name:
        Deploy to staging.quartermaster.us - Build Frontend
      if: github.ref == 'refs/heads/staging'
      run: |
        cd ~/staging.quartermaster.us/frontend/
        npm run build

    - name:
        Deploy to staging.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/staging'
      run: |
        cd ~/staging.quartermaster.us/
        rm -rf .env
        touch .env

    - name:
        Deploy to staging.quartermaster.us - Populate Backend .env File with Secrets
      if: github.ref == 'refs/heads/staging'
      run: |
        cd ~/staging.quartermaster.us/
        echo "${{ secrets.STAGING_QUARTERMASTER_US }}" > .env

    - name:
        Deploy to staging.quartermaster.us - Restart PM2 Process
      if: github.ref == 'refs/heads/staging'
      run: |
        pm2 restart 'Quartermaster Staging'


##############################################################################################
################################### DEV CI ###################################################
##############################################################################################

    - name:
        Deploy to dev.quartermaster.us - Navigate to Directory, Reset and Pull Changes
      if: github.ref == 'refs/heads/dev'
      run: |
        echo "Deploying to dev.quartermaster.us"
        cd ~/dev.quartermaster.us/
        git restore .
        git pull

    - name:
        Deploy to dev.quartermaster.us - Install Backend Dependencies
      if: github.ref == 'refs/heads/dev'
      run: |
        cd ~/dev.quartermaster.us/
        npm install

    - name:
        Deploy to dev.quartermaster.us - Install Frontend Dependencies
      if: github.ref == 'refs/heads/dev'
      run: |
        cd ~/dev.quartermaster.us/frontend/
        npm install

    - name:
          Deploy to dev.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/dev'
      run: |
        cd ~/dev.quartermaster.us/frontend/
        rm -rf .env
        touch .env

    - name:
        Deploy to dev.quartermaster.us - Populate Frontend .env File with Secrets
      if: github.ref == 'refs/heads/dev'
      run: |
        cd ~/dev.quartermaster.us/frontend/
        echo "${{ secrets.DEV_QUARTERMASTER_US_FRONTEND }}" > .env

    - name:
        Deploy to dev.quartermaster.us - Build Frontend
      if: github.ref == 'refs/heads/dev'
      run: |
        cd ~/dev.quartermaster.us/frontend/
        npm run build

    - name:
        Deploy to dev.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/dev'
      run: |
        cd ~/dev.quartermaster.us/
        rm -rf .env
        touch .env

    - name:
        Deploy to dev.quartermaster.us - Populate Backend .env File with Secrets
      if: github.ref == 'refs/heads/dev'
      run: |
        cd ~/dev.quartermaster.us/
        echo "${{ secrets.DEV_QUARTERMASTER_US }}" > .env

    - name:
        Deploy to dev.quartermaster.us - Restart PM2 Process
      if: github.ref == 'refs/heads/dev'
      run: |
        pm2 restart 'Quartermaster Dev'

##############################################################################################
################################ Production CI ###############################################
##############################################################################################
    - name:
        Deploy to portal.quartermaster.us - Navigate to Directory, Reset and Pull Changes
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to quartermaster-webapp"
        cd ~/quartermaster-webapp/
        git restore .
        git pull

    - name:
        Deploy to portal.quartermaster.us - Install Backend Dependencies
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-webapp/
        npm install

    - name:
        Deploy to portal.quartermaster.us - Install Frontend Dependencies
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-webapp/frontend/
        npm install

    - name:
        Deploy to portal.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-webapp/frontend/
        rm -rf .env
        touch .env

    - name:
        Deploy to portal.quartermaster.us - Populate Frontend .env File with Secrets
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-webapp/frontend/
        echo "${{ secrets.PORTAL_QUARTERMASTER_US_FRONTEND }}" > .env

    - name:
        Deploy to portal.quartermaster.us - Build Frontend
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-webapp/frontend/
        npm run build

    - name:
        Deploy to portal.quartermaster.us - Clean and Create .env File
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-webapp/
        rm -rf .env
        touch .env

    - name:
        Deploy to portal.quartermaster.us - Populate Backend .env File with Secrets
      if: github.ref == 'refs/heads/main'
      run: |
        cd ~/quartermaster-webapp/
        echo "${{ secrets.PORTAL_QUARTERMASTER_US }}" > .env

    - name:
        Deploy to portal.quartermaster.us - Restart PM2 Process
      if: github.ref == 'refs/heads/main'
      run: |
        pm2 restart 'Quartermaster App'

##############################################################################################
################################ Update Slack  ###############################################
##############################################################################################
    
    - name: Set URL based on branch
      run: |
        if [ "${{ github.ref }}" == "refs/heads/dev" ]; then
          echo "URL=https://dev.quartermaster.us" >> $GITHUB_ENV
        elif [ "${{ github.ref }}" == "refs/heads/staging" ]; then
          echo "URL=https://staging.quartermaster.us" >> $GITHUB_ENV
        else
          echo "URL=https://portal.quartermaster.us" >> $GITHUB_ENV
        fi

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify Slack on success
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: custom
        fields: workflow,job,commit,repo,ref,author,took
        custom_payload: |
          {
            "attachments": [
              {
                "color": '${{ job.status }}' == 'success' ? 'good' : '${{ job.status }}' == 'failure' ? 'danger' : 'warning',
                "text": `\nCommit: (${process.env.AS_COMMIT}) @ ${process.env.AS_REF} for Repository: ${process.env.AS_REPO} \n by ${process.env.AS_AUTHOR} at \n ${{ env.URL }} with status: ${{ job.status }}`
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_CHAT }}