require('dotenv').config();
const db = require('../modules/db');

const BATCH_SIZE = 1000;

let vesselMapping = {};

async function buildVesselMapping() {
    console.log('Building vessel mapping cache...');
    try {
        const vessels = await db.qmShared.collection('vessels').find({}, { unit_id: 1, _id: 1 });
        vessels.forEach(vessel => {
            vesselMapping[vessel.unit_id] = vessel._id;
        });
        console.log(`Built mapping for ${Object.keys(vesselMapping).length} vessels`);
        return vesselMapping;
    } catch (error) {
        console.error('Error building vessel mapping:', error);
        throw error;
    }
}

async function updateLocationCollection(collectionName) {
    console.log(`\nProcessing collection: ${collectionName}`);

    // Extract unit_id from collection name (remove '_location' suffix)
    const unitId = collectionName.replace(/_location$/, '');
    const onboardVesselId = vesselMapping[unitId];

    if (!onboardVesselId) {
        console.log(`No vessel mapping found for unit_id: ${unitId}, skipping collection ${collectionName}`);
        return 0;
    }

    const collection = db.qm.collection(collectionName);

    const totalCount = await collection.countDocuments({
        onboardVesselId: { $exists: false }
    });

    if (totalCount === 0) {
        console.log(`No documents to update in ${collectionName}`);
        return 0;
    }

    console.log(`Found ${totalCount} documents to update in ${collectionName}`);

    let updatedCount = 0;
    let processedCount = 0;

    while (processedCount < totalCount) {
        const result = await collection.updateMany(
            {
                onboardVesselId: { $exists: false }
            },
            {
                $set: { onboardVesselId: onboardVesselId }
            },
            {
                limit: BATCH_SIZE
            }
        );

        updatedCount += result.modifiedCount;
        processedCount += BATCH_SIZE;

        const progress = Math.min(100, Math.round((updatedCount / totalCount) * 100));
        console.log(`Progress: ${updatedCount}/${totalCount} documents (${progress}%)`);

        // Break if no more documents were modified
        if (result.modifiedCount === 0) {
            break;
        }
    }

    console.log(`Completed ${collectionName}: ${updatedCount} documents updated`);
    return updatedCount;
}

async function updateOnboardVesselIds() {
    const startTime = Date.now();
    let totalUpdated = 0;

    try {
        console.log('Starting onboardVesselId migration...');

        // Build vessel mapping cache
        await buildVesselMapping();

        // Get all collections that end with '_location'
        const collections = await db.qm.db.listCollections().toArray();
        const locationCollections = collections
            .filter(c => c.name.endsWith('_location'))
            .map(c => c.name);

        console.log(`Found ${locationCollections.length} location collections to process`);

        // Process each collection
        for (const collectionName of locationCollections) {
            const updatedCount = await updateLocationCollection(collectionName);
            totalUpdated += updatedCount;
        }

        const timeElapsed = (Date.now() - startTime) / 1000;
        console.log(`\nMigration completed successfully!`);
        console.log(`Total documents updated: ${totalUpdated}`);
        console.log(`Total time: ${timeElapsed.toFixed(2)} seconds`);

    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    }
}

// Wait for all database connections to be ready
Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once('open', resolve);
        db.qm.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    })
]).then(() => {
    console.log('Database connections established, starting migration...');
    updateOnboardVesselIds()
        .then(() => {
            console.log('Migration script completed successfully');
            process.exit(0);
        })
        .catch(err => {
            console.error('Migration script failed:', err);
            process.exit(1);
        });
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});
