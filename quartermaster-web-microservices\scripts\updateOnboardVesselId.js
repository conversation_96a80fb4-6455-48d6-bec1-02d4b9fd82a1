require('dotenv').config();
const db = require('../modules/db');

const BATCH_SIZE = 1000;

let vesselUnitsMapping = {};

async function buildVesselMapping() {
    console.log('Building vessel units mapping cache...');
    try {
        const vessels = await db.qmShared.collection('vessels').find({}, {
            _id: 1,
            name: 1,
            unit_id: 1,
            units_history: 1
        }).toArray();

        vessels.forEach(vessel => {
            if (vessel.units_history && vessel.units_history.length > 0) {
                vessel.units_history.forEach(unitHistory => {
                    if (!vesselUnitsMapping[unitHistory.unit_id]) {
                        vesselUnitsMapping[unitHistory.unit_id] = [];
                    }
                    vesselUnitsMapping[unitHistory.unit_id].push({
                        vesselId: vessel._id,
                        vesselName: vessel.name,
                        mountTimestamp: unitHistory.mount_timestamp,
                        unmountTimestamp: unitHistory.unmount_timestamp
                    });
                });
            }
        });

        console.log(`Built mapping for ${Object.keys(vesselUnitsMapping).length} units across ${vessels.length} vessels`);
        return vesselUnitsMapping;
    } catch (error) {
        console.error('Error building vessel mapping:', error);
        throw error;
    }
}

async function updateLocationCollection(collectionName) {
    console.log(`\nProcessing collection: ${collectionName}`);

    // Extract unit_id from collection name (remove '_location' suffix)
    const unitId = collectionName.replace(/_location$/, '');
    const unitMappings = vesselUnitsMapping[unitId];

    if (!unitMappings || unitMappings.length === 0) {
        console.log(`No vessel mapping found for unit_id: ${unitId}, skipping collection ${collectionName}`);
        return 0;
    }

    const collection = db.qm.collection(collectionName);
    let totalUpdatedCount = 0;

    // Process each vessel mapping for this unit
    for (const mapping of unitMappings) {
        const { vesselId, vesselName, mountTimestamp, unmountTimestamp } = mapping;

        console.log(`Processing vessel: ${vesselName} (${vesselId}) for unit: ${unitId}`);
        console.log(`Mount: ${mountTimestamp}, Unmount: ${unmountTimestamp || 'Still mounted'}`);

        // Build timestamp query
        const timestampQuery = {};
        if (mountTimestamp) {
            timestampQuery.$gte = new Date(mountTimestamp);
        }
        if (unmountTimestamp) {
            timestampQuery.$lt = new Date(unmountTimestamp);
        } else {
            // If unmount is null, take from mount timestamp till now
            timestampQuery.$lt = new Date();
        }

        // Build the query to find documents without onboard_vessel_id in the timestamp range
        const query = {
            onboard_vessel_id: { $exists: false },
            timestamp: timestampQuery
        };

        const totalCount = await collection.countDocuments(query);

        if (totalCount === 0) {
            console.log(`No documents to update for vessel ${vesselName} in the specified time range`);
            continue;
        }

        console.log(`Found ${totalCount} documents to update for vessel ${vesselName}`);

        let updatedCount = 0;
        let processedCount = 0;

        while (processedCount < totalCount) {
            const result = await collection.updateMany(
                query,
                {
                    $set: { onboard_vessel_id: vesselId }
                },
                {
                    limit: BATCH_SIZE
                }
            );

            updatedCount += result.modifiedCount;
            processedCount += BATCH_SIZE;

            const progress = Math.min(100, Math.round((updatedCount / totalCount) * 100));
            console.log(`Progress for ${vesselName}: ${updatedCount}/${totalCount} documents (${progress}%)`);

            // Break if no more documents were modified
            if (result.modifiedCount === 0) {
                break;
            }
        }

        console.log(`Completed vessel ${vesselName}: ${updatedCount} documents updated`);
        totalUpdatedCount += updatedCount;
    }

    console.log(`Completed ${collectionName}: ${totalUpdatedCount} total documents updated`);
    return totalUpdatedCount;
}

async function updateOnboardVesselIds() {
    const startTime = Date.now();
    let totalUpdated = 0;

    try {
        console.log('Starting onboard_vessel_id migration with timestamp filtering...');

        // Build vessel units mapping cache
        await buildVesselMapping();

        // Get all collections that end with '_location'
        const collections = await db.qm.db.listCollections().toArray();
        const locationCollections = collections
            .filter(c => c.name.endsWith('_location'))
            .map(c => c.name);

        console.log(`Found ${locationCollections.length} location collections to process`);

        // Process each collection
        for (const collectionName of locationCollections) {
            const updatedCount = await updateLocationCollection(collectionName);
            totalUpdated += updatedCount;
        }

        const timeElapsed = (Date.now() - startTime) / 1000;
        console.log(`\nMigration completed successfully!`);
        console.log(`Total documents updated: ${totalUpdated}`);
        console.log(`Total time: ${timeElapsed.toFixed(2)} seconds`);

    } catch (error) {
        console.error('Migration failed:', error);
        throw error;
    }
}

// Wait for all database connections to be ready
Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once('open', resolve);
        db.qm.on('error', reject);
    }),
    new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    })
]).then(() => {
    console.log('Database connections established, starting migration...');
    updateOnboardVesselIds()
        .then(() => {
            console.log('Migration script completed successfully');
            process.exit(0);
        })
        .catch(err => {
            console.error('Migration script failed:', err);
            process.exit(1);
        });
}).catch(err => {
    console.error('Failed to establish database connections:', err);
    process.exit(1);
});
