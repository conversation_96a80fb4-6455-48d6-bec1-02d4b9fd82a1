const linkedUnits = [["QSX0028", "QSX0003"]];

const getLinkedUnits = (unitId) => {
    const linkedUnit = linkedUnits.find((itm) => itm.includes(unitId));
    return linkedUnit ? linkedUnit : [unitId];
};

const populateLinkedUnitsInArray = (unitIds) => {
    const newUnitIds = unitIds;

    unitIds.forEach((unitId) => {
        const linkedUnits = getLinkedUnits(unitId);
        linkedUnits.forEach((linkedUnitId) => {
            if (!newUnitIds.includes(linkedUnitId)) {
                newUnitIds.push(linkedUnitId);
            }
        });
    });

    return newUnitIds;
};

module.exports = {
    getLinkedUnits,
    populateLinkedUnitsInArray,
};
