import React from 'react';
import ReactDOM from 'react-dom/client';
import { isEnvironment } from '../src/utils.js';

jest.mock('react-dom/client', () => ({
    createRoot: jest.fn().mockReturnValue({
        render: jest.fn(),
    }),
}));

jest.mock('../src/utils', () => ({
    isEnvironment: jest.fn().mockReturnValue(true),
}));

jest.mock('../src/Router.jsx', () => () => <div>Mock Router</div>);

describe('main.js', () => {
    let rootElement;
    let splashElement;

    beforeEach(() => {
        jest.clearAllMocks();
        jest.useFakeTimers()
    });

    afterEach(() => {
        document.body.removeChild(rootElement);
        if (splashElement.parentNode) {
            document.body.removeChild(splashElement);
        }
    });

    it('should render the Router component in the root element', () => {
        rootElement = document.createElement('div');
        rootElement.id = 'root';
        document.body.appendChild(rootElement);

        splashElement = document.createElement('div');
        splashElement.id = 'splash';
        splashElement.remove = jest.fn();
        document.body.appendChild(splashElement);

        isEnvironment.mockReturnValueOnce(true);

        require('../src/main.jsx');
        console.log();
        jest.advanceTimersByTime(250);
        expect(ReactDOM.createRoot).toHaveBeenCalledWith(rootElement);
        expect(ReactDOM.createRoot().render).toHaveBeenCalled();
        expect(splashElement.remove).toHaveBeenCalled();
    });
});
