const mongoose = require("mongoose");
const User = require("./User");
const db = require('../modules/db');


const NotificationSummarySchema = new mongoose.Schema({
    unit_id: { type: [String], required: true },
    preference: { type: [String], required: true, enum: ['daily', 'weekly', 'monthly'] },
    receivers: [{
        type: String,
        default: []
    }],
    title: { type: [String], required: true },
    created_by: { type: mongoose.Schema.Types.ObjectId, ref: User },
    created_at: { type: Date, default: () => new Date().toISOString() },
    is_enabled: { type: Boolean, default: true },
    updated_at: { type: Date, default: () => new Date().toISOString() },
});

const NotificationSummary = db.qm.model('NotificationSummary', NotificationSummarySchema, 'notifications_summary');

module.exports = NotificationSummary;