import { render, screen, waitFor, act } from '@testing-library/react';
import InsetMap from '../../src/components/InsetMap';
import { useApp } from '../../src/hooks/AppHook';
import gps_socket from '../../src/gps_socket';
import { GoogleMap } from '@react-google-maps/api';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

jest.mock('../../src/axios', () => ({
    post: jest.fn(),
}));
jest.mock('../../src/gps_socket', () => ({
    on: jest.fn(),
    off: jest.fn(),
}));
jest.mock('@react-google-maps/api', () => ({
    GoogleMap: jest.fn()
}));
jest.mock('../../src/hooks/AppHook', () => ({
    useApp: jest.fn(),
}));

describe('InsetMap Component', () => {
    let vessel;
    let mockUseApp = {
        google: {
            maps: {
                event: {
                    clearListeners: jest.fn(),
                },
                Marker: jest.fn().mockImplementation(() => ({
                    setPosition: jest.fn(),
                    setMap: jest.fn(),
                    addListener: jest.fn().mockImplementation((a, b) => b()),
                })),
                InfoWindow: jest.fn().mockImplementation(() => ({
                    setContent: jest.fn(),
                    open: jest.fn(),
                    close: jest.fn(),
                })),
            },
        }
    };
    let initialZoom = 10;

    beforeEach(() => {
        vessel = { id: 'vessel1' };
        useApp.mockReturnValue(mockUseApp);

        jest.spyOn(global, 'setInterval').mockImplementation((callback) => {
            callback();
            return jest.fn();
        });

        GoogleMap.mockImplementation(({ children, onLoad, onUnmount, onZoomChanged }) => {
            if (onLoad) {
                onLoad({ setZoom: jest.fn() });
            }
            return (
                <div data-testid="google-map">
                    {children}
                </div>
            );
        })
        gps_socket.on.mockImplementation((event, callback) => {
            act(() => {
                callback({
                    latitude: 12.34,
                    longitude: 56.78,
                    timestamp: '2024-11-15T12:00:00Z'
                });
            });
        });
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should render the map skeleton when no vessel id or coordinates', async () => {
        render(<InsetMap initialZoom={null} />);
        expect(gps_socket.on).toHaveBeenCalled();
    });

    it('should render the map skeleton when vessel id and coordinates is null', async () => {
        render(<InsetMap initialZoom={null} vessel={{ id: null }} newCoordinate={null} />);
        expect(gps_socket.on).toHaveBeenCalled();
    });

    it('should render "No coordinates found" when coordinates are missing', async () => {
        render(<InsetMap vessel={vessel} initialZoom={initialZoom} />);
        expect(gps_socket.on).toHaveBeenCalled();
    });

    it('should render the map when coordinates are available', async () => {
        const coordinate = {
            latitude: 12.34,
            longitude: 56.78,
            timestamp: '2024-11-15T12:00:00Z'
        };

        render(
            <InsetMap
                vessel={vessel}
                initialZoom={initialZoom}
                newCoordinate={coordinate}
            />
        );

        await waitFor(() => {
            expect(screen.getByTestId('google-map')).toBeInTheDocument();
        });
    });

    it('should render the map when coordinates are available and call onUnmount', async () => {
        GoogleMap.mockImplementation(({ children, onLoad, onUnmount, onZoomChanged }) => {
            if (onUnmount) {
                onUnmount();
            }
            if (onZoomChanged) {
                onZoomChanged();
            }
            return (
                <div data-testid="google-map">
                    {children}
                </div>
            );
        })

        const coordinate = {
            latitude: null,
            longitude: 56.78,
            timestamp: '2024-11-15T12:00:00Z'
        };

        render(
            <InsetMap
                vessel={vessel}
                initialZoom={initialZoom}
                newCoordinate={coordinate}
            />
        );

        await waitFor(() => {
            expect(screen.getByTestId('google-map')).toBeInTheDocument();
        });
    });

    it('should update the coordinates when gps_socket emits data', async () => {
        render(<InsetMap vessel={vessel} initialZoom={initialZoom} />);
        gps_socket.on.mock.calls[0][1]({ latitude: 20.34, longitude: 70.12, timestamp: '2024-11-15T12:01:00Z' });
        gps_socket.on.mock.calls[0][1]({ latitude: null, longitude: 70.12, timestamp: '2024-11-15T12:01:00Z' });
        expect(gps_socket.on).toHaveBeenCalled();
    });

    it('should handle unmounting and cleaning up', async () => {
        const { unmount } = render(<InsetMap vessel={vessel} initialZoom={initialZoom} />);
        unmount();
        expect(gps_socket.off).toHaveBeenCalledWith('vessel1/gps', expect.any(Function));
    });

    it('should handle if google is null', async () => {
        useApp.mockReturnValue({ google: null });
        render(<InsetMap vessel={vessel} initialZoom={initialZoom} />);
        expect(gps_socket.off).not.toHaveBeenCalled();
    });

    it('should initialize the InfoWindow if google API is loaded', async () => {
        render(<InsetMap vessel={vessel} initialZoom={initialZoom} />);
        expect(mockUseApp.google.maps.InfoWindow).toHaveBeenCalledWith({ disableAutoPan: true });
    });

    it('should not call setMarker or center map if coordinate is missing', async () => {
        render(<InsetMap vessel={vessel} initialZoom={initialZoom} />);
        await waitFor(() => {
            expect(mockUseApp.google.maps.Marker).not.toHaveBeenCalled();
        });
    });
});
