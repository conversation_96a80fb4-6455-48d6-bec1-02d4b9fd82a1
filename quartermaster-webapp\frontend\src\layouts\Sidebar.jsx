import { Map, Public, SupervisorAccount, Videocam, WifiOff, Settings, DirectionsBoat } from "@mui/icons-material";
import NotificationsIcon from "@mui/icons-material/Notifications";
import { Grid, Typography } from "@mui/material";
import UpgradeIcon from "@mui/icons-material/Upgrade";
import { useLocation, useNavigate } from "react-router-dom";
import { useUser } from "../hooks/UserHook";
import { useEffect, useState } from "react";
import { useApp } from "../hooks/AppHook";
import { permissions } from "../utils";
import { getSocket } from "../socket";
import StatsIcon from "/icons/stats-icon.svg";
import LogsIcon from "/icons/logs-icon.svg";
import ApiKeysIcon from "/icons/api-keys-icon.svg";
import EventsIcon from "/icons/event-icon.svg";
import theme from "../theme";
// import RegionsMenu from "./RegionsMenu";
import ProfileMenu from "./ProfileMenu";

const Sidebar = ({ drawerOpen, setDrawerOpen }) => {
    const { screenSize } = useApp();
    const { user } = useUser();
    const navigateHook = useNavigate();
    const { pathname } = useLocation();
    const [isOnline, setIsOnline] = useState(true);
    const [menuOpen, setMenuOpen] = useState(false);

    const navigate = (path) => {
        if (setDrawerOpen) setDrawerOpen(false);
        navigateHook(path);
    };

    useEffect(() => {
        const socket = getSocket();

        const onConnect = () => setIsOnline(true);
        const onDisconnect = () => setIsOnline(false);

        socket.on("connect", onConnect);
        socket.on("disconnect", onDisconnect);

        return () => {
            socket.off("connect", onConnect);
            socket.off("disconnect", onDisconnect);
        };
    }, []);

    const menuItems = [
        // {
        //     label: 'Regions',
        //     icon: <Public />,
        //     permissions: [permissions.accessAllUnits],
        //     onClick: () => setRegionsAnchorEl(true),
        //     isVisible: user?.hasPermissions([permissions.accessAllUnits]) || userRegions.length > 1,
        // },
        {
            label: "Streams",
            icon: <Videocam />,
            path: "/dashboard/stream",
            isVisible: true,
        },
        {
            label: "Events",
            icon: <img src={EventsIcon} alt="Events Icon" style={{ width: "24px", height: "24px" }} />,
            path: "/dashboard/events",
            isVisible: true,
        },
        {
            label: "Map",
            icon: <Map />,
            path: "/dashboard/map",
            isVisible: true,
        },
        {
            label: "Users",
            icon: <SupervisorAccount />,
            path: "/dashboard/users",
            permissions: [permissions.manageUsers, permissions.manageRoles],
            isVisible: user?.hasPermissions([permissions.manageUsers, permissions.manageRoles], "OR"),
        },
        {
            label: "Logs",
            icon: <img src={LogsIcon} alt="Logs Icon" style={{ width: "24px", height: "24px" }} />,
            path: "/dashboard/logs",
            permissions: [permissions.viewSessionLogs],
            isVisible: user?.hasPermissions([permissions.viewSessionLogs]),
        },
        {
            label: "Region Groups",
            icon: <Public />,
            path: "/dashboard/region-groups",
            isVisible: user?.hasPermissions([permissions.manageRegionsGroups]),
        },
        {
            label: "Api Keys",
            icon: <img src={ApiKeysIcon} alt="Api Key Icon" style={{ width: "24px", height: "24px" }} />,
            path: "/dashboard/api-keys",
            isVisible: user?.hasPermissions([permissions.manageApiKeys]),
        },
        {
            label: "Stats",
            icon: <img src={StatsIcon} alt="Stats Icon" style={{ width: "24px", height: "24px" }} />,
            path: "/dashboard/statistics",
            isVisible: user?.hasPermissions([permissions.viewStatistics]),
        },
        {
            label: "Alerts",
            icon: <NotificationsIcon />,
            path: "/dashboard/notification",
            isVisible: user?.hasPermissions([permissions.manageNotificationsAlerts]) || false,
        },
        {
            label: "Vessels",
            icon: <DirectionsBoat />,
            path: "/dashboard/vessel-management",
            isVisible: user?.hasPermissions([permissions.manageVessels]),
        },
    ];

    const mobileMenuItems = [
        {
            icon: <Settings onClick={() => navigate("/dashboard/settings")} sx={{ fontSize: "24px", color: "#FFFFFF", cursor: "pointer" }} />,
            label: "Settings",
            path: "/dashboard/settings",
        },
        {
            icon: <ProfileMenu logoutOnly={screenSize.xs || screenSize.sm || screenSize.md} />,
            label: "Logout",
        },
    ];

    return (
        <Grid
            container
            className="dashboard-step-2"
            flexDirection={"column"}
            height={"auto"}
            sx={{ backgroundColor: "primary.main" }}
            color={theme.palette.custom.unfocused}
            padding={{ xs: 0.5, lg: 1 }}
            paddingY={{ xs: 0.5, lg: 1 }}
            gap={3}
            flexWrap={"nowrap"}
            overflow={"auto"}
        >
            {menuItems.map(
                (item, index) =>
                    item.isVisible && (
                        <Grid
                            key={index}
                            container
                            onClick={item.path ? () => navigate(item.path) : item.onClick}
                            gap={2}
                            padding={1}
                            justifyContent={"flex-start"}
                            alignItems={"center"}
                            color={"primary.contrastText"}
                            flexWrap={"nowrap"}
                            sx={{
                                transition: "width 0.5s, background-color 0.5s",
                                backgroundColor: pathname === item.path ? theme.palette.custom.mainBlue : "transparent",
                                borderRadius: "5px",
                                overflow: "hidden",
                                width: drawerOpen || menuOpen ? "150px !important" : "40px !important", // Removed `!important`
                                cursor: "pointer",
                            }}
                            size="auto"
                        >
                            <Grid display={"flex"} alignItems={"center"}>
                                {item.icon}
                            </Grid>
                            {item.label && (
                                <Grid
                                    sx={{
                                        textWrap: "nowrap",
                                        opacity: drawerOpen || menuOpen ? 1 : 0,
                                        transition: "opacity 0.5s, transform 0.5s !important",
                                        transform: drawerOpen || menuOpen ? "translateX(0)" : "translateX(-10px)", // Adds a slight slide-in effect
                                    }}
                                >
                                    <Typography fontSize={"12px"}>{item.label}</Typography>
                                </Grid>
                            )}
                        </Grid>
                    ),
            )}
            <Grid container flexDirection={"column"} gap={1} alignItems={"center"} marginTop={"auto"}>
                <Grid display={isOnline ? "none" : "block"}>
                    <WifiOff color="warning" fontSize="large" />
                </Grid>
                <Grid container alignItems={"center"} flexDirection={"column"} display={{ xs: "flex", lg: "none" }} gap={3}>
                    {mobileMenuItems.map((item, index) => (
                        <Grid
                            key={index}
                            container
                            onClick={item.path ? () => navigate(item.path) : item.onClick}
                            gap={2}
                            padding={1}
                            justifyContent={"flex-start"}
                            alignItems={"center"}
                            color={"primary.contrastText"}
                            sx={{
                                transition: "0.2s",
                                backgroundColor: pathname === item.path ? theme.palette.custom.mainBlue : "transparent",
                                borderRadius: "5px",
                                minWidth: drawerOpen || menuOpen ? "150px" : "0",
                                ":hover": {
                                    color: "primary.contrastText",
                                    cursor: "pointer",
                                    transition: "0.2s",
                                },
                            }}
                            size="auto"
                        >
                            <Grid display={"flex"} alignItems={"center"}>
                                {item.icon}
                            </Grid>
                            {(drawerOpen || menuOpen) && item.label && (
                                <Grid>
                                    <Typography fontSize={"12px"}>{item.label}</Typography>
                                </Grid>
                            )}
                        </Grid>
                    ))}
                </Grid>
                {!drawerOpen && (
                    <Grid
                        container
                        onClick={() => setMenuOpen((prev) => !prev)}
                        gap={2}
                        padding={1}
                        justifyContent={"flex-end"}
                        alignItems={"center"}
                        color={"primary.contrastText"}
                        flexWrap={"nowrap"}
                        sx={{
                            transition: "width 0.5s, background-color 0.5s",
                            borderRadius: "5px",
                            overflow: "hidden",
                            width: drawerOpen || menuOpen ? "150px !important" : "40px !important", // Removed `!important`
                            cursor: "pointer",
                        }}
                        size="auto"
                    >
                        <UpgradeIcon
                            sx={{
                                transform: menuOpen ? "rotate(270deg)" : "rotate(90deg)",
                            }}
                        />
                    </Grid>
                )}
            </Grid>
            {/* <RegionsMenu regionsAnchorEl={regionsAnchorEl} setRegionsAnchorEl={setRegionsAnchorEl} menuOpen={menuOpen} /> */}
        </Grid>
    );
};

export default Sidebar;
