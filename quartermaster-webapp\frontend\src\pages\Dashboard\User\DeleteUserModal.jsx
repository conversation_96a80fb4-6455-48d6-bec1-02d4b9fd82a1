import { Button, Grid, Modal, Typography } from "@mui/material";
import axiosInstance from "../../../axios";
import ModalContainer from "../../../components/ModalContainer";

const DeleteUserModal = ({ deleteUser, setDeleteUser, setDeleting, onSuccess }) => {
    const handleClose = () => {
        setDeleteUser();
    };

    const onDelete = () => {
        handleClose();
        setDeleting(deleteUser._id);
        axiosInstance
            .delete("/users/" + deleteUser._id, { meta: { showSnackbar: true } })
            .then(() => onSuccess && onSuccess())
            .catch(console.error)
            .finally(() => setDeleting());
    };

    return (
        <Modal open={Boolean(deleteUser)} onClose={handleClose}>
            <ModalContainer title={"Delete User"} onClose={handleClose}>
                <Grid container flexDirection={"column"} gap={2} width={{ xs: 300, sm: "auto" }}>
                    <Grid>
                        <Typography>Are you sure you want to delete user &quot;{deleteUser?.name}&quot;?</Typography>
                    </Grid>
                    <Grid container gap={1} justifyContent={"center"}>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" onClick={handleClose} className="btn-cancel">
                                Cancel
                            </Button>
                        </Grid>
                        <Grid justifyContent={"center"} display={"flex"}>
                            <Button variant="contained" color="error" onClick={onDelete} sx={{ textTransform: "none", padding: "10px 24px" }}>
                                Delete
                            </Button>
                        </Grid>
                    </Grid>
                </Grid>
            </ModalContainer>
        </Modal>
    );
};

export default DeleteUserModal;
