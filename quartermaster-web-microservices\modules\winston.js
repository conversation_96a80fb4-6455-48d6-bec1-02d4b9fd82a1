const fs = require('fs');
const path = require('path');
const winston = require('winston');

// Store created loggers to reuse
const loggers = new Map();

// Function to create or reuse a logger for each vessel and region
const createLoggerWithPath = (_path) => {
    const logDir = path.join('logs', _path);
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }

    // Check if logger already exists
    if (loggers.has(logDir)) {
        return loggers.get(logDir);
    }

    const logger = winston.createLogger({
        level: 'info',
        format: winston.format.combine(
            winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
            winston.format.printf(({ timestamp, level, message }) => {
                return `${timestamp} [${level}]: ${typeof message === 'object' ? JSON.stringify(message) : message}`;
            }),
        ),
        transports: [
            new winston.transports.File( { filename: path.join(logDir, 'info.log'), level: 'info' }),
            new winston.transports.File({ filename: path.join(logDir, 'error.log'), level: 'error' }),
            new winston.transports.Console({ level: 'debug' }),
        ],
    });

    // Store the created logger
    loggers.set(logDir, logger);

    return logger;
};

module.exports = {
    createLoggerWithPath
}