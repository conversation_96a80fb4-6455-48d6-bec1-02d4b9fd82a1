import { render, screen, fireEvent } from "@testing-library/react";
import RevokeKeyModal from "../../src/pages/Dashboard/ApiKeys//RevokeKeyModal";
import axiosInstance from "../../src/axios";

jest.mock("../../src/axios", () => ({
    patch: jest.fn(),
}));

describe("RevokeKeyModal Component", () => {
    const setRevokeKey = jest.fn();
    const setRevoking = jest.fn();

    afterEach(() => {
        jest.clearAllMocks();
    });

    it("renders correctly when revokeKey is provided for revoking", () => {
        render(
            <RevokeKeyModal
                revokeKey={{ _id: "123", serial: 1, is_revoked: false }}
                setRevokeKey={setRevokeKey}
                setRevoking={setRevoking}
            />
        );

        expect(screen.getByText("Revoke Key")).toBeInTheDocument();
        expect(screen.getByText("Are you sure you want to revoke API key #1?")).toBeInTheDocument();
        expect(screen.getByText("Cancel")).toBeInTheDocument();
        expect(screen.getByText("Revoke")).toBeInTheDocument();
    });

    it("renders correctly when revokeKey is provided for restoring", () => {
        render(
            <RevokeKeyModal
                revokeKey={{ _id: "123", serial: 2, is_revoked: true }}
                setRevokeKey={setRevokeKey}
                setRevoking={setRevoking}
            />
        );

        expect(screen.getByText("Restore Key")).toBeInTheDocument();
        expect(screen.getByText("Are you sure you want to restore API key #2?")).toBeInTheDocument();
        expect(screen.getByText("Cancel")).toBeInTheDocument();
        expect(screen.getByText("Restore")).toBeInTheDocument();
    });

    it("does not render when revokeKey is not provided", () => {
        render(
            <RevokeKeyModal
                revokeKey={null}
                setRevokeKey={setRevokeKey}
                setRevoking={setRevoking}
            />
        );

        expect(screen.queryByText("Revoke Key")).not.toBeInTheDocument();
        expect(screen.queryByText("Restore Key")).not.toBeInTheDocument();
    });

    it("calls setRevokeKey with undefined when Cancel button is clicked", () => {
        render(
            <RevokeKeyModal
                revokeKey={{ _id: "123", serial: 1, is_revoked: false }}
                setRevokeKey={setRevokeKey}
                setRevoking={setRevoking}
            />
        );

        const cancelButton = screen.getByText("Cancel");
        fireEvent.click(cancelButton);

        expect(setRevokeKey).toHaveBeenCalled();
    });

    it("calls axios.patch with correct API key and updates revoking state when Revoke button is clicked", async () => {
        axiosInstance.patch.mockResolvedValueOnce({});
        render(
            <RevokeKeyModal
                revokeKey={{ _id: "123", serial: 1, is_revoked: false }}
                setRevokeKey={setRevokeKey}
                setRevoking={setRevoking}
            />
        );

        const revokeButton = screen.getByText("Revoke");
        fireEvent.click(revokeButton);

        expect(setRevokeKey).toHaveBeenCalled();
        expect(setRevoking).toHaveBeenCalled();
        expect(axiosInstance.patch).toHaveBeenCalledWith(
            "/apiKeys/123/revoke",
            { revoke: true },
            { meta: { showSnackbar: true } }
        );

        await Promise.resolve();
        expect(setRevoking).toHaveBeenCalled();
    });
});
