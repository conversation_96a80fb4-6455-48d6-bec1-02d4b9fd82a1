import React from "react";
import { Table, TableBody, TableRow, TableCell, Typography, Avatar } from "@mui/material";
import dayjs from "dayjs";
import theme from "../../../theme";
import { userValues } from "../../../utils.js";

const RenderRow = ({ user, index, style, data }) => {
    const { logs, username } = data;
    const log = logs[index];

    return (
        log && (
            <Table>
                <TableBody style={style}>
                    <TableRow
                        key={log._id}
                        sx={{
                            display: "flex",
                            width: "100%",
                            paddingY: 2,
                            borderBottom: logs.length - 1 === index ? "none" : `1px solid ${theme.palette.custom.borderColor}`,
                        }}
                    >
                        {["name", "connect_timestamp", "disconnect_timestamp", "device", "browser"].map((field, idx) => (
                            <TableCell
                                key={idx}
                                sx={{
                                    color: "#FFFFFF",
                                    flex: 1,
                                    padding: 0,
                                    border: "none",
                                    ...(idx === 0 && { display: "flex", alignItems: "center", gap: 2 }),
                                }}
                            >
                                {field === "name" ? (
                                    <>
                                        <Avatar sx={{ width: "35px", height: "10px", visibility: "hidden" }} />
                                        <Typography variant="h6" fontSize={"16px !important"} color="#fff" fontWeight={500}>
                                            {log.user?.name ?? username}
                                        </Typography>
                                    </>
                                ) : (
                                    <Typography variant="h6" fontSize={"16px !important"} color="#fff">
                                        {field.endsWith("connect_timestamp")
                                            ? log[field]
                                                ? dayjs(log[field]).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))
                                                : "--"
                                            : (log[field] ?? "--")}
                                    </Typography>
                                )}
                            </TableCell>
                        ))}
                    </TableRow>
                </TableBody>
            </Table>
        )
    );
};

export default RenderRow;
