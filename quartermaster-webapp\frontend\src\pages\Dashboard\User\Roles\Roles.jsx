import { alpha, Button, CircularProgress, FormControl, Grid, MenuItem, Pagination, Select, Typography } from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { useEffect, useState } from "react";
import axiosInstance from "../../../../axios";
import dayjs from "dayjs";
import { userValues } from "../../../../utils";
import { SentimentVeryDissatisfied } from "@mui/icons-material";
import { getSocket } from "../../../../socket";
import { useApp } from "../../../../hooks/AppHook";
import AddRoleModal from "./AddRoleModal";
import DeleteRoleModal from "./DeleteRoleModal";
import PermissionsField from "./PermissionsField";
import { useUser } from "../../../../hooks/UserHook";
import ReorderRolesModal from "./ReorderRolesModal";
import FilterRoleModal from "./FilterRoleModal";
import { useToaster } from "../../../../hooks/ToasterHook";
import theme from "../../../../theme";
import DeleteButton from "../../../../components/DeleteButton";

export default function Roles({ showAddRole, setShowAddRole, showReorderModal, setShowReorderModal, showFilterModal, setShowFilterModal }) {
    const { isMobile, timezone } = useApp();
    const toaster = useToaster();
    const { user } = useUser();
    const [roles, setRoles] = useState([]);
    const [filteredRoles, setFilteredRoles] = useState([]);
    const [permissions, setPermissions] = useState([]);

    const [allowSave, setAllowSave] = useState(false);
    const [allowReset, setAllowReset] = useState(false);
    const [updatedRoles, setUpdatedRoles] = useState([]);
    const [saving, setSaving] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    // eslint-disable-next-line no-unused-vars
    const [adding, setAdding] = useState(false);

    const [deleteRole, setDeleteRole] = useState(null);

    // eslint-disable-next-line no-unused-vars
    const [deleting, setDeleting] = useState(null);
    const [rowHeights, setRowHeights] = useState({});

    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    useEffect(() => {
        fetchRoles();
        fetchPermissions();

        const socket = getSocket();

        socket.on("roles/changed", fetchRoles);

        return () => {
            socket.off("roles/changed", fetchRoles);
        };
    }, []);

    useEffect(() => {
        if (Array.isArray(roles) && roles.length > 0) {
            // setFilteredRoles(roles);
            setIsLoading(false);
        }
    }, [roles]);

    useEffect(() => {
        const maxPage = Math.ceil(filteredRoles.length / rowsPerPage);
        if (page > maxPage && maxPage > 0) {
            setPage(1);
        }
    }, [page, rowsPerPage, filteredRoles]);

    const fetchRoles = async () => {
        try {
            const { data } = await axiosInstance.get("/roles");
            if (Array.isArray(data) && data.length > 0) {
                setRoles(
                    data.sort((a, b) => {
                        if (!a || a.hierarchy_number === undefined) return 1;
                        if (!b || b.hierarchy_number === undefined) return -1;

                        return a.hierarchy_number - b.hierarchy_number;
                    }),
                );
            } else {
                setRoles([]);
                toaster("No data found for roles", { variant: "warning" });
            }
        } catch (err) {
            setRoles([]);
            toaster("Something went wrong", { variant: "error" });
            console.error("An error occurred while fetching roles on the Roles Page", err);
        }
    };

    const fetchPermissions = async () => {
        try {
            const { data } = await axiosInstance.get("/permissions");
            if (Array.isArray(data) && data.length > 0) {
                setPermissions(data);
            } else {
                toaster("No data found for permissions", { variant: "warning" });
                setPermissions([]);
            }
        } catch (err) {
            setPermissions([]);
            toaster("Something went wrong", { variant: "error" });
            console.error("An error occurred while fetching permissions on the Roles Page", err);
        }
    };

    const onReorderRoles = (newOrder) => {
        setRoles(newOrder);
    };

    const onReset = () => {
        fetchRoles();
        setUpdatedRoles([]);
        setAllowReset(false);
        setAllowSave(false);
    };

    const onSave = () => {
        setSaving(true);
        const update_roles = roles.filter((role) => (updatedRoles.includes(role._id) ? role : false));
        axiosInstance
            .patch("roles/permissionUpdate", { roles_permissions: update_roles }, { meta: { showSnackbar: true } })
            .then(() => {
                setUpdatedRoles([]);
                setAllowReset(false);
                setAllowSave(false);
                setSaving(false);
            })
            .catch((err) => {
                setSaving(false);
                console.error(err);
                if (err.response?.status === 403) fetchRoles(); // Re-fetch roles if unauthorized
            });
    };

    const setRowHeight = (rowId, height) => {
        setRowHeights((prev) => ({ ...prev, [rowId]: height })); // Store row height by row ID
    };

    const getRowHeight = (params) => {
        // Use the height from the state, or fall back to a default height
        return rowHeights[params.model._id] || 52; // Default to 52px if no height is recorded yet
    };

    // Custom Footer Component
    const CustomFooter = ({ page, rowsPerPage, totalRows, onPageChange, onRowsPerPageChange }) => {
        const startIndex = (page - 1) * rowsPerPage + 1;
        const endIndex = Math.min(page * rowsPerPage, totalRows);

        return (
            <>
                {allowSave && allowReset && (
                    <Grid
                        container
                        justifyContent={"flex-end"}
                        alignItems={"center"}
                        marginBottom={2}
                        padding={"10px"}
                        backgroundColor={alpha(theme.palette.custom.offline, 0.08)}
                        gap={2}
                        sx={{
                            borderRadius: "5px",
                        }}
                    >
                        <Grid>
                            <Button
                                disabled={!allowReset}
                                onClick={onReset}
                                variant="outlined"
                                sx={{
                                    border: `1px solid ${theme.palette.custom.borderColor}`,
                                    color: "#9A9CA2",
                                    padding: "8px 40px",
                                    "&:hover": {
                                        color: "#9A9CA2",
                                        border: `1px solid ${theme.palette.custom.borderColor}`,
                                    },
                                }}
                            >
                                Undo
                            </Button>
                        </Grid>
                        <Grid>
                            <Button
                                disabled={!allowSave || saving}
                                startIcon={saving && <CircularProgress size={18} />}
                                variant="contained"
                                onClick={onSave}
                                sx={{
                                    backgroundColor: theme.palette.custom.mainBlue,
                                    color: "#FFFFFF",
                                    padding: "8px 40px",
                                    "&:hover": {
                                        color: "#FFFFFF",
                                        backgroundColor: theme.palette.custom.mainBlue,
                                    },
                                }}
                            >
                                Save
                            </Button>
                        </Grid>
                    </Grid>
                )}
                <Grid
                    container
                    justifyContent={{ sm: "space-between", xs: "center" }}
                    alignItems={"center"}
                    padding={"10px"}
                    backgroundColor={alpha(theme.palette.custom.offline, 0.08)}
                    gap={2}
                    sx={{
                        borderRadius: "5px",
                    }}
                >
                    <Grid padding={"10px 20px"} size="auto">
                        <Typography fontSize={{ xs: "12px", lg: "14px" }} fontWeight={600}>
                            {`${endIndex == 0 ? 0 : startIndex} - ${endIndex} of ${totalRows}`}
                        </Typography>
                    </Grid>
                    <Grid size="auto">
                        <Pagination
                            count={Math.ceil(totalRows / rowsPerPage)}
                            page={page}
                            onChange={onPageChange}
                            shape="rounded"
                            siblingCount={isMobile ? 0 : 1}
                            boundaryCount={1}
                            sx={{
                                "& .MuiButtonBase-root, .MuiPaginationItem-root": {
                                    color: "#FFFFFF",
                                    minHeight: "30px",
                                    fontSize: isMobile ? "9px" : "14px",
                                    borderRadius: "8px",
                                    minWidth: "32px",
                                    display: "flex",
                                    justifyContent: "center",
                                    alignItems: "center",
                                    backgroundColor: alpha(theme.palette.custom.offline, 0.2),
                                },
                                "& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected": {
                                    color: "#FFFFFF",
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                        />
                    </Grid>
                    <Grid justifyContent="flex-end" display={"flex"} size="auto">
                        <FormControl variant="outlined">
                            <Select
                                value={rowsPerPage}
                                onChange={onRowsPerPageChange}
                                sx={{
                                    "& .MuiOutlinedInput-notchedOutline": {
                                        border: "none",
                                    },
                                    "& .MuiSelect-select": {
                                        padding: "10px",
                                        fontSize: isMobile ? "12px" : "16px",
                                        backgroundColor: theme.palette.custom.mainBlue,
                                        borderRadius: "5px",
                                        color: "#FFFFFF",
                                        minWidth: isMobile ? 0 : "80px",
                                    },
                                }}
                            >
                                {[5, 10, 20].map((size) => (
                                    <MenuItem key={size} value={size}>
                                        {isMobile ? size : `${size} / Page`}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                </Grid>
            </>
        );
    };

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handlePageSizeChange = (event) => {
        setRowsPerPage(event.target.value);
    };

    const checkRoleUpdation = (role) => {
        if (user.role_id === role.role_id || !role.editable) return true;
        if (user.role.hierarchy_number >= role.hierarchy_number) return true;
        return false;
    };

    /**
     * @type {Array<import("@mui/x-data-grid").GridColDef>}
     */
    const columns = [
        { field: "role_id", headerName: "ID", minWidth: 100 },
        { field: "role_name", headerName: "Role", minWidth: 150 },
        {
            field: "denied_permissions",
            headerName: "Permissions",
            flex: 1,
            minWidth: 300,
            renderCell: (params) =>
                permissions && (
                    <PermissionsField
                        setRowHeight={setRowHeight}
                        setUpdatedRoles={setUpdatedRoles}
                        setRoles={setRoles}
                        setAllowSave={setAllowSave}
                        setAllowReset={setAllowReset}
                        role={params.row}
                        permissions={permissions}
                    />
                ),
            valueGetter: (arr) => permissions?.filter((p) => !arr.includes(p.permission_id)).map((p) => p.permission_name) || [],
        },
        {
            field: "creation_timestamp",
            headerName: "Created",
            minWidth: 150,
            valueGetter: (v) =>
                dayjs(v)
                    .tz(timezone)
                    .format(userValues.dateTimeFormat(user, { exclude_seconds: true, exclude_hours: true, exclude_minutes: true })),
        },
        {
            field: "actions",
            headerName: "",
            minWidth: 50,
            renderCell: (params) => (
                <Grid container justifyContent="center" alignItems={"center"} height={"inherit"}>
                    <DeleteButton onClick={() => setDeleteRole(params.row)} disabled={checkRoleUpdation(params.row)} />
                </Grid>
            ),
        },
    ];

    const columnsWithouFilters = [
        ...columns.map((col) => ({
            ...col,
            filterable: false,
            sortable: true,
            resizable: false,
            disableColumnMenu: true,
            disableReorder: true,
            disableExport: true,
        })),
    ];

    return (
        <Grid container flexDirection={"column"} height={"100%"}>
            <Grid overflow={"auto"} size="grow">
                <DataGrid
                    loading={isLoading}
                    disableRowSelectionOnClick
                    rows={filteredRoles.slice((page - 1) * rowsPerPage, page * rowsPerPage)}
                    columns={columnsWithouFilters}
                    getRowId={(row) => row._id}
                    slots={{
                        footer: () => (
                            <CustomFooter
                                page={page}
                                rowsPerPage={rowsPerPage}
                                totalRows={filteredRoles.length}
                                onPageChange={handlePageChange}
                                onRowsPerPageChange={handlePageSizeChange}
                            />
                        ),
                        noRowsOverlay: () => (
                            <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"} height={"100%"}>
                                <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
                                <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                                    No data available
                                </Typography>
                            </Grid>
                        ),
                    }}
                    getRowHeight={getRowHeight}
                />
            </Grid>
            <AddRoleModal showAddRole={showAddRole} setShowAddRole={setShowAddRole} setAdding={setAdding} onSuccess={fetchRoles} />
            <DeleteRoleModal deleteRole={deleteRole} setDeleteRole={setDeleteRole} setDeleting={setDeleting} onSuccess={fetchRoles} />
            <ReorderRolesModal
                open={showReorderModal}
                onClose={() => setShowReorderModal(false)}
                roles={roles}
                onReorder={onReorderRoles}
                fetchRoles={fetchRoles}
                user={user}
            />
            <FilterRoleModal
                showFilterModal={showFilterModal}
                setShowFilterModal={setShowFilterModal}
                permissions={permissions}
                roles={roles}
                setFilteredRoles={setFilteredRoles}
                setPage={setPage}
            />
        </Grid>
    );
}
