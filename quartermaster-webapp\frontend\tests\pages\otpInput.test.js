import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material';
import OTPInput from '../../src/pages/OTPInput/OTPInput';
import { useUser } from '../../src/hooks/UserHook';
import { useNavigate } from 'react-router-dom';
import axiosInstance from '../../src/axios';

jest.mock('../../src/hooks/UserHook');
jest.mock('../../src/axios', () => ({
    post: jest.fn(),
}));
jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: jest.fn(),
}));

const theme = createTheme();

describe('OTPInput Component', () => {
    let loginMock;
    const mockNavigate = jest.fn();

    beforeEach(() => {
        jest.useFakeTimers();
        loginMock = jest.fn();
        useUser.mockReturnValue({ login: loginMock });
        useNavigate.mockReturnValue(mockNavigate);
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.useRealTimers();
    });

    it('should display error message for missing username and password and clear it after 3000ms', async () => {
        render(
            <MemoryRouter initialEntries={[{ state: null }]}>
                <ThemeProvider theme={theme}>
                    <OTPInput />
                </ThemeProvider>
            </MemoryRouter>
        );

        fireEvent.click(screen.getByRole('button', { name: /Confirm OTP/i }));

        await waitFor(() => expect(screen.getByText('Username and password required. Please navigate to login page')).toBeInTheDocument());

        jest.advanceTimersByTime(3000);

        await waitFor(() => expect(screen.queryByText('Username and password required. Please navigate to login page')).not.toBeInTheDocument());
    });

    it('should handle OTP input change and move focus to next input', () => {
        render(
            <MemoryRouter initialEntries={[{ state: { username: 'testuser', password: 'password' } }]}>
                <ThemeProvider theme={theme}>
                    <OTPInput />
                </ThemeProvider>
            </MemoryRouter>
        );

        const otpInputs = screen.getAllByRole('textbox');

        fireEvent.change(otpInputs[0], { target: { value: '1' } });
        expect(otpInputs[0].value).toBe('1');
        expect(document.activeElement).toBe(otpInputs[1]);

        fireEvent.change(otpInputs[1], { target: { value: '2' } });
        expect(otpInputs[1].value).toBe('2');
        expect(document.activeElement).toBe(otpInputs[2]);

        fireEvent.change(otpInputs[2], { target: { value: 't' } });
        expect(otpInputs[2].value).toBe('');
        expect(document.activeElement).toBe(otpInputs[2]);
    });

    it('should handle backspace key to move focus back to previous input', () => {
        render(
            <MemoryRouter initialEntries={[{ state: { username: 'testuser', password: 'password' } }]}>
                <ThemeProvider theme={theme}>
                    <OTPInput />
                </ThemeProvider>
            </MemoryRouter>
        );

        const otpInputs = screen.getAllByRole('textbox');

        fireEvent.change(otpInputs[0], { target: { value: '1' } });

        fireEvent.change(otpInputs[1], { target: { value: '' } });
        otpInputs[1].focus();

        fireEvent.keyDown(otpInputs[1], { key: 'Backspace' });

        expect(document.activeElement).toBe(otpInputs[0]);
    });

    it('should handle backspace key to not move focus in there is nothing in field', () => {
        render(
            <MemoryRouter initialEntries={[{ state: { username: 'testuser', password: 'password' } }]}>
                <ThemeProvider theme={theme}>
                    <OTPInput />
                </ThemeProvider>
            </MemoryRouter>
        );

        const otpInputs = screen.getAllByRole('textbox');

        fireEvent.change(otpInputs[0], { target: { value: '' } });
        otpInputs[0].focus();

        fireEvent.keyDown(otpInputs[0], { key: 'Backspace' });

        expect(document.activeElement).toBe(otpInputs[0]);
    });

    it('should handle pasting a full OTP code', () => {
        render(
            <MemoryRouter initialEntries={[{ state: { username: 'testuser', password: 'password' } }]}>
                <ThemeProvider theme={theme}>
                    <OTPInput />
                </ThemeProvider>
            </MemoryRouter>
        );

        const otpInputs = screen.getAllByRole('textbox');

        fireEvent.paste(otpInputs[0], {
            clipboardData: {
                getData: () => '123456',
            }
        });

        otpInputs.forEach((input, index) => {
            expect(input.value).toBe(String(index + 1));
        });

        fireEvent.paste(otpInputs[0], {
            clipboardData: {
                getData: () => 't',
            }
        });

        expect(otpInputs[0].value).not.toBe('t');
    });

    it('should submit OTP and call login function on successful axios call', async () => {
        axiosInstance.post.mockResolvedValue({});

        render(
            <MemoryRouter initialEntries={[{ state: { username: 'testuser', password: 'password' } }]}>
                <ThemeProvider theme={theme}>
                    <OTPInput />
                </ThemeProvider>
            </MemoryRouter>
        );

        const otpInputs = screen.getAllByRole('textbox');
        otpInputs.forEach((input, index) => fireEvent.change(input, { target: { value: index + 1 } }));

        const confirmButton = screen.getByRole('button', { name: /Confirm OTP/i });
        fireEvent.click(confirmButton);

        await waitFor(() => expect(loginMock).toHaveBeenCalledWith({ username: 'testuser', password: 'password' }));
    });

    it('should handle axios error with response.data (Otp Code failed)', async () => {
        axiosInstance.post.mockRejectedValue({
            response: {
                data: {
                    message: 'Otp Code failed',
                }
            }
        });

        render(
            <MemoryRouter initialEntries={[{ state: { username: 'testuser', password: 'password' } }]}>
                <ThemeProvider theme={theme}>
                    <OTPInput />
                </ThemeProvider>
            </MemoryRouter>
        );

        const confirmButton = screen.getByRole('button', { name: /Confirm OTP/i });
        fireEvent.click(confirmButton);

        await waitFor(() => expect(screen.getByText(/Otp Code failed/i)).toBeInTheDocument());
    });

    it('should handle axios error with error.message (Otp Code failed)', async () => {
        axiosInstance.post.mockRejectedValue({
            error: {
                message: 'Otp Code failed',
            }
        });

        render(
            <MemoryRouter initialEntries={[{ state: { username: 'testuser', password: 'password' } }]}>
                <ThemeProvider theme={theme}>
                    <OTPInput />
                </ThemeProvider>
            </MemoryRouter>
        );

        const confirmButton = screen.getByRole('button', { name: /Confirm OTP/i });
        fireEvent.click(confirmButton);

        await waitFor(() => expect(screen.getByText(/An error occurred/i)).toBeInTheDocument());
    });

    it('should show resend timer and disable resend button for 60 seconds', async () => {
        axiosInstance.post.mockResolvedValue({});

        render(
            <MemoryRouter initialEntries={[{ state: { username: 'testuser', password: 'password' } }]}>
                <ThemeProvider theme={theme}>
                    <OTPInput />
                </ThemeProvider>
            </MemoryRouter>
        );

        const resendButton = screen.getByRole('button', { name: /Resend Code/i });
        fireEvent.click(resendButton);

        await waitFor(() => expect(resendButton).toBeDisabled());

        jest.advanceTimersByTime(60000);

        await waitFor(() => expect(resendButton).not.toBeDisabled());

        axiosInstance.post.mockRejectedValue({
            response: {
                data: {
                    message: 'Resend Code failed',
                }
            }
        });
        fireEvent.click(resendButton);

        axiosInstance.post.mockRejectedValue({
            error: {
                message: 'Resend Code failed',
            }
        });
        fireEvent.click(resendButton);
        expect(screen.getByText(/Resend Code/i)).toBeInTheDocument();
    });
});
