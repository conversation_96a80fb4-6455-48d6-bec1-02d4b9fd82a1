// const { listThings } = require("../modules/awsIot")
const mongoose = require('mongoose');
const db = require("../modules/db");

let VesselLocation = {}

// listThings().then(regions => {

//     regions.forEach(data => {
//         const region = data.region
//         const things = data.things.filter(thing => thing.thingTypeName === 'smartmast')

//         if (things.length === 0) return console.info(`No applicable things in region ${region}`)

//         things.forEach(thing => {
//             const vesselName = thing.thingName
//             const collection = `${vesselName}_location`

//             const VesselLocationSchema = new mongoose.Schema({
//                 latitude: { type: Number, required: true },
//                 longitude: { type: Number, required: true },
//                 groundSpeed: { type: Number, required: true },
//                 timestamp: { type: Date, required: false, default: () => new Date().toISOString(), index: true }
//             }, { minimize: false });

//             VesselLocation[collection] = db.qm.model(collection, VesselLocationSchema, collection);
//         })

//     })
// }).catch(err => {
//     console.error(`things list error ${err}`)
// })

const getLocationCollection = (vesselName) => {
    const collection = `${vesselName}_location`

    if (VesselLocation[collection]) return VesselLocation[collection]

    const VesselLocationSchema = new mongoose.Schema({
        latitude: { type: Number, required: true },
        longitude: { type: Number, required: true },
        groundSpeed: { type: Number, required: true },
        isStationary: { type: Boolean, required: true, default: false },
        headingMotion: { type: Number, required: false },
        accuracyHeading: { type: Number, required: false },
        onboard_vessel_id: { type: mongoose.Schema.Types.ObjectId, required: false },
        metadata: { type: Object, required: false },
        timestamp: { type: Date, required: false, default: () => new Date().toISOString(), index: true }
    }, { minimize: false });

    VesselLocation[collection] = db.qm.model(collection, VesselLocationSchema, collection);

    return VesselLocation[collection]
}

module.exports = {
    getLocationCollection
};
