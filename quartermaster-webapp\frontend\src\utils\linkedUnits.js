import { isDevUnit } from "../utils";

const linkedUnits = [["QSX0028", "QSX0003"]];

const getLinkedUnits = (unitId) => {
    const linkedUnit = linkedUnits.find((itm) => itm.includes(unitId));
    return linkedUnit ? linkedUnit : [unitId];
};

const populateLinkedUnitsInArray = (unitIds) => {
    const newUnitIds = unitIds;

    unitIds.forEach((unitId) => {
        const linkedUnits = getLinkedUnits(unitId);
        linkedUnits.forEach((linkedUnitId) => {
            if (!newUnitIds.includes(linkedUnitId)) {
                newUnitIds.push(linkedUnitId);
            }
        });
    });

    return newUnitIds;
};

const getLinkedVesselName = (vesselInfo) => {
    if (!vesselInfo) throw new Error("Vessel info is required");
    if (!vesselInfo.name) throw new Error("Vessel name is required");

    if (isDevUnit(vesselInfo.name) && vesselInfo.linked_units?.length) {
        return vesselInfo.linked_units.find((lu) => !isDevUnit(lu.name))?.name || vesselInfo.unit_id;
    }

    return vesselInfo?.name || vesselInfo?.unit_id;
};

export { getLinkedUnits, populateLinkedUnitsInArray, getLinkedVesselName };
