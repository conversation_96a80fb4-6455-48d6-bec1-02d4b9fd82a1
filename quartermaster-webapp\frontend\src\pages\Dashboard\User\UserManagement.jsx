import { Grid, InputAdornment, OutlinedInput, Tab, Tabs, Button } from "@mui/material";
import React, { useEffect, useMemo, useState } from "react";
import Users from "./Users";
import Organizations from "./Organizations/Organizations";
import Roles from "./Roles/Roles";
import { useUser } from "../../../hooks/UserHook";
import { permissions } from "../../../utils";
import { Search, Add, Reorder } from "@mui/icons-material";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";
import useDebounce from "../../../hooks/useDebounce.js";

export default function UserManagement() {
    const { user } = useUser();
    const [tab, setTab] = useState("");

    const [showAddUser, setShowAddUser] = useState(false);
    const [showUserFilterModal, setShowUserFilterModal] = useState(false);

    const [searchQuery, setSearchQuery] = useState({});

    const [showAddNewRole, setShowAddNewRole] = useState(false);
    const [showReorderRole, setShowReorderRole] = useState(false);
    const [showRoleFilterModal, setShowRoleFilterModal] = useState(false);
    const [showAddOrganization, setShowAddOrganization] = useState(false);

    const { isMobile } = useApp();

    const [searchTerm, setSearchTerm] = React.useState(searchQuery.full_name_or_email || "");
    const debouncedInputValue = useDebounce(searchTerm, 600);

    useEffect(() => {
        if (debouncedInputValue !== undefined && debouncedInputValue !== searchQuery.full_name_or_email) {
            console.log("Debounced search:", debouncedInputValue);
            handleSearchChange({ target: { name: "full_name_or_email", value: debouncedInputValue } });
        }
    }, [debouncedInputValue]);

    useEffect(() => {
        if (searchQuery.full_name_or_email !== searchTerm) {
            setSearchTerm(searchQuery.full_name_or_email || "");
        }
    }, [searchQuery.full_name_or_email]);

    const handleInputChange = (event) => {
        setSearchTerm(event.target.value);
    };

    const tabs = useMemo(
        () => [
            {
                value: "users",
                label: "Users",
                component: (
                    <Users
                        showAddUser={showAddUser}
                        setShowAddUser={setShowAddUser}
                        showFilterModal={showUserFilterModal}
                        setShowFilterModal={setShowUserFilterModal}
                        searchQuery={searchQuery}
                    />
                ),
                display: user?.hasPermissions([permissions.manageUsers]),
            },
            {
                value: "roles",
                label: "Roles",
                component: (
                    <Roles
                        showAddRole={showAddNewRole}
                        setShowAddRole={setShowAddNewRole}
                        showReorderModal={showReorderRole}
                        setShowReorderModal={setShowReorderRole}
                        showFilterModal={showRoleFilterModal}
                        setShowFilterModal={setShowRoleFilterModal}
                    />
                ),
                display: user?.hasPermissions([permissions.manageRoles]),
            },
            {
                value: "organizations",
                label: "Organizations",
                component: <Organizations showAddOrganization={showAddOrganization} setShowAddOrganization={setShowAddOrganization} />,
                display: user?.hasPermissions([permissions.manageOrganizations]),
            },
        ],
        [user, showAddUser, showUserFilterModal, showAddNewRole, showReorderRole, showRoleFilterModal, searchQuery, showAddOrganization],
    );

    const handleSearchChange = (event) => {
        setSearchQuery({ full_name_or_email: event.target.value });
    };

    useEffect(() => {
        if (!tab) {
            setTab(tabs.find((t) => t.display)?.value || "");
        }
    }, [tab, tabs]);

    return (
        user &&
        tabs.some((t) => t.display) &&
        tab && (
            <Grid
                container
                color={"#FFFFFF"}
                flexDirection={"column"}
                width={"100%"}
                height={"100%"}
                overflow={"auto"}
                sx={{ backgroundColor: theme.palette.custom.darkBlue }}
            >
                <Grid container padding={2} display={"flex"} rowGap={2} justifyContent={"space-between"} alignItems={"center"} flexWrap={"wrap"}>
                    <Grid
                        size={{
                            xs: 12,
                            lg: 3.9,
                        }}
                    >
                        <Tabs
                            value={tab}
                            onChange={(e, v) => setTab(v)}
                            sx={{
                                width: "100%",
                                padding: "4px",
                                border: `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                                "& .MuiTabs-flexContainer": {
                                    height: "100%",
                                },
                                "& .MuiButtonBase-root": {
                                    width: 100 / tabs.filter((t) => t.display).length + "%",
                                    borderRadius: "8px",
                                },
                                "& .MuiButtonBase-root.Mui-selected": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                        >
                            {tabs
                                .filter((t) => t.display)
                                .map((t) => (
                                    <Tab
                                        key={t.value}
                                        label={t.label}
                                        value={t.value}
                                        sx={{
                                            maxWidth: "none",
                                        }}
                                    />
                                ))}
                        </Tabs>
                    </Grid>
                    <Grid
                        container
                        columnGap={2}
                        justifyContent={tab === "users" ? "space-between" : "flex-end"}
                        size={{
                            xs: 12,
                            lg: 8,
                        }}
                    >
                        {tab === "users" && (
                            <Grid
                                size={{
                                    xs: "grow",
                                    lg: 5.8,
                                }}
                            >
                                <OutlinedInput
                                    type="text"
                                    value={searchTerm}
                                    onChange={handleInputChange}
                                    startAdornment={
                                        <InputAdornment position="start">
                                            <Search sx={{ color: "#FFFFFF" }} />
                                        </InputAdornment>
                                    }
                                    placeholder="Search by name or email"
                                    sx={{
                                        color: "#FFFFFF",
                                        width: "100%",
                                        "& .MuiOutlinedInput-notchedOutline": {
                                            border: "2px solid",
                                            borderColor: theme.palette.custom.borderColor + " !important",
                                            borderRadius: "8px",
                                        },
                                    }}
                                />
                            </Grid>
                        )}
                        {tab === "roles" && (
                            <Grid
                                alignItems={"center"}
                                display={"flex"}
                                justifyContent={"flex-end"}
                                gap={2}
                                width={{ xs: "100%", lg: "fit-content" }}
                                minHeight={50}
                                size="auto"
                            >
                                <Button
                                    fullWidth={isMobile}
                                    variant="outlined"
                                    startIcon={<img src={"/icons/filter_icon.svg"} width={20} height={20} />}
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            borderColor: theme.palette.custom.borderColor,
                                            color: "#FFFFFF",
                                            height: { xs: "100%", lg: "auto" },
                                            padding: { xs: "0", lg: "10px 20px" },
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    onClick={() => setShowRoleFilterModal(true)}
                                >
                                    {!isMobile && "Filter"}
                                </Button>
                                <Button
                                    fullWidth={isMobile}
                                    variant="contained"
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            color: "#FFFFFF",
                                            height: { xs: "100%", lg: "auto" },
                                            padding: { xs: 0, lg: "10px 20px" },
                                            backgroundColor: theme.palette.custom.mainBlue,
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    startIcon={<Reorder />}
                                    onClick={() => setShowReorderRole(true)}
                                >
                                    {!isMobile && "Reorder Roles"}
                                </Button>
                                <Button
                                    fullWidth={isMobile}
                                    variant="contained"
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            color: "#FFFFFF",
                                            height: { xs: "100%", lg: "auto" },
                                            padding: { xs: 0, lg: "10px 20px" },
                                            backgroundColor: theme.palette.custom.mainBlue,
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    startIcon={<Add />}
                                    onClick={() => setShowAddNewRole(true)}
                                >
                                    {!isMobile && "Add New Role"}
                                </Button>
                            </Grid>
                        )}
                        {tab === "organizations" && (
                            <Grid
                                alignItems={"center"}
                                display={"flex"}
                                justifyContent={"flex-end"}
                                gap={2}
                                width={{ xs: "100%", lg: "fit-content" }}
                                minHeight={50}
                                size="auto"
                            >
                                <Button
                                    fullWidth={isMobile}
                                    variant="contained"
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            color: "#FFFFFF",
                                            height: { xs: "100%", lg: "auto" },
                                            padding: { xs: 0, lg: "10px 20px" },
                                            backgroundColor: theme.palette.custom.mainBlue,
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    startIcon={<Add />}
                                    onClick={() => setShowAddOrganization((p) => !p)}
                                >
                                    {!isMobile && "Add New Organization"}
                                </Button>
                            </Grid>
                        )}
                        {tab === "users" && (
                            <Grid
                                alignItems={"center"}
                                display={"flex"}
                                justifyContent={"flex-end"}
                                gap={2}
                                size={{
                                    xs: "auto",
                                    lg: 5.8,
                                }}
                            >
                                <Button
                                    variant="outlined"
                                    startIcon={<img src={"/icons/filter_icon.svg"} width={20} height={20} />}
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            borderColor: theme.palette.custom.borderColor,
                                            height: { xs: "100%", lg: "auto" },
                                            color: "#FFFFFF",
                                            padding: { xs: "0", lg: "10px 20px" },
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    onClick={() => setShowUserFilterModal(true)}
                                >
                                    {!isMobile && "Filter"}
                                </Button>
                                <Button
                                    variant="contained"
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            color: "#FFFFFF",
                                            height: { xs: "100%", lg: "auto" },
                                            padding: { xs: 0, lg: "10px 20px" },
                                            backgroundColor: theme.palette.custom.mainBlue,
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    startIcon={<Add />}
                                    onClick={() => setShowAddUser(true)}
                                >
                                    {!isMobile && "Invite User"}
                                </Button>
                            </Grid>
                        )}
                    </Grid>
                </Grid>
                {tabs
                    .filter((t) => t.display)
                    .map((t) => (
                        <Grid key={t.value} display={tab !== t.value && "none"} paddingX={2} paddingBottom={2} width={"100%"} size="grow">
                            {t.component}
                        </Grid>
                    ))}
            </Grid>
        )
    );
}
