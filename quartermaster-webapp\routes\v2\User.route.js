const express = require("express");
const UserRoute = require("../../models/User");
const { validateError } = require("../../utils/functions");
const hasPermission = require("../../middlewares/hasPermission");
const { permissions } = require("../../utils/permissions");
const isAuthenticated = require("../../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../../middlewares/assignEndpointId");
const { endpointIds } = require("../../utils/endpointIds");
const { Types } = require("mongoose");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_USERS_LIST_V2),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageUsers]),
    async (req, res) => {
        try {
            let page = Math.max(1, Number(req.query.page) || 1);
            let limit = Math.min(50, Number(req.query.rowsPerPage) || 10);

            const query = {
                is_deleted: false,
            };

            if (!req.user.organization.is_internal) {
                query.organization_id = req.user.organization._id;
            } else {
                if (req.query.organizations) {
                    query.organization_id = { $in: req.query.organizations.split(",").map((v) => Types.ObjectId(v)) };
                }
            }

            if (req.query.roles) {
                query.role_id = { $in: req.query.roles.split(",").map(Number) };
            }

            if (req.query.vessels) {
                query.allowed_units.unit_id = { $in: req.query.vessels.split(",") };
            }

            if (req.query.hasEmail !== undefined) {
                if (req.query.hasEmail === "true") {
                    query.email = { $exists: true, $not: { $in: ["", null] } };
                } else {
                    query.email = { $exists: false };
                }
            }

            if (req.query.full_name_or_email) {
                query.$or = [
                    { name: { $regex: req.query.full_name_or_email, $options: "i" } },
                    { email: { $regex: req.query.full_name_or_email, $options: "i" } },
                ];
            }

            if (req.query.created_after) {
                const date = new Date(Number(req.query.created_after));
                query.creation_timestamp = { $gte: date };
            }

            const skip = (page - 1) * limit;
            const totalCount = await UserRoute.countDocuments(query);
            const totalPages = Math.ceil(totalCount / limit);

            const users = await UserRoute.aggregate([
                { $match: query },
                {
                    $lookup: {
                        from: "organizations",
                        localField: "organization_id",
                        foreignField: "_id",
                        as: "organization",
                    },
                },
                {
                    $addFields: {
                        organization: { $arrayElemAt: ["$organization", 0] },
                    },
                },
                {
                    $project: {
                        password: 0,
                        jwt_tokens: 0,
                        reset_password_token: 0,
                        reset_password_expire: 0,
                    },
                },
                { $skip: skip },
                { $limit: limit },
            ]);

            res.json({ users, totalCount, totalPages });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;

/**
 * @swagger
 * tags:
 *   - name: Auth
 *     description: Authentication endpoint
 * components:
 *   securitySchemes:
 *     basicAuth:
 *       type: http
 *       scheme: basic
 *     apiKeyAuth:
 *       type: apiKey
 *       in: header
 *       name: qm-api-key
 *     bearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *   schemas:
 *     AuthToken:
 *       type: object
 *       properties:
 *         jwt_token:
 *           type: string
 *           description: The JWT token
 *           example: eyJhbGciOiJIUzI1Ni....eyJzdWIiOiIxMjM0NTY3ODk....SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJ...
 *         expires:
 *           type: string
 *           format: date-time
 *           description: Expiration date and time of the token
 *           example: '2023-09-25T10:20:30Z'
 *     User:
 *       type: object
 *       properties:
 *         _id:
 *           type: string
 *           description: Document Id of the user
 *           example: 67942a74a7f838634a00190a
 *         name:
 *           type: string
 *           description: The user's full name
 *           example: John Doe
 *         email:
 *           type: string
 *           description: The user's email address
 *           example: <EMAIL>
 *         username:
 *           type: string
 *           description: The unique username for the user
 *           example: johndoe123
 *         role_id:
 *           type: number
 *           description: The role ID of the user
 *           example: 1
 *         allowed_units:
 *           type: array
 *           description: The units that the user is allowed to access
 *           example: ["prototype-32","prototype-33"]
 *         region:
 *           type: string
 *           description: The region of the user
 *           example: "ap-southeast-1"
 *           deprecated: true
 *         email_verification_enabled:
 *           type: boolean
 *           description: Whether the user's email is verified
 *           example: false
 *         email_verified_device_ids:
 *           type: array
 *           description: The devices that the user has verified their email on
 *           items:
 *             type: string
 *             example: "762486b8-d22b-4813-b488-a4242017a47b"
 *         deletable:
 *           type: boolean
 *           description: Whether the user can be deleted
 *           example: false
 *         is_deleted:
 *           type: boolean
 *           description: Whether the user has been soft deleted
 *           example: false
 *         creation_timestamp:
 *           type: string
 *           format: date-time
 *           description: Timestamp when the user was created
 *           example: '2023-09-25T10:20:30Z'
 */

/**
 * @swagger
 * /users/auth:
 *   get:
 *     summary: Fetch JWT token
 *     description: Rate limited to 15 requests every 5 seconds
 *     tags: [Auth]
 *     security:
 *       - basicAuth: []
 *       - apiKeyAuth: []
 *     responses:
 *       200:
 *         description: JWT token and expiration date
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               $ref: '#/components/schemas/AuthToken'
 *       400:
 *         description: Invalid credentials
 *       302:
 *         description: Email verification required
 *       500:
 *         description: Server error
 */
