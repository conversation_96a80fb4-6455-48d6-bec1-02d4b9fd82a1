import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter, useNavigate } from 'react-router-dom';
import DashboardLayout from '../../src/layouts/DashboardLayout';
import { useApp } from '../../src/hooks/AppHook';
import { useUser } from '../../src/hooks/UserHook';
import { registerSessionExpiryCallback } from '../../src/axios';
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';

jest.mock('react-router-dom', () => ({
    ...jest.requireActual('react-router-dom'),
    useNavigate: jest.fn(),
    Outlet: () => <div data-testid="outlet">Outlet Content</div>
}));

jest.mock('../../src/hooks/AppHook');
jest.mock('../../src/hooks/UserHook');
jest.mock('@mui/material', () => ({
    ...jest.requireActual('@mui/material'),
    Drawer: ({ children, onClose }) => {
        if (onClose) onClose();
        return <div>{children}</div>
    }
}));
jest.mock('../../src/layouts/Appbar', () => ({ setDrawerOpen }) =>
    <div data-testid="appbar" onClick={() => setDrawerOpen(true)}>Appbar</div>
);
jest.mock('../../src/layouts/Sidebar', () => ({ drawerOpen, setDrawerOpen }) =>
    <div data-testid="sidebar" onClick={() => setDrawerOpen && setDrawerOpen(false)}>Sidebar</div>
);
jest.mock('../../src/components/TourGuide', () => ({ isMobile }) =>
    <div data-testid="tour-guide">TourGuide {isMobile ? 'Mobile' : 'Desktop'}</div>
);
jest.mock('../../src/axios', () => ({
    registerSessionExpiryCallback: jest.fn()
}));

const mockNavigate = jest.fn();

describe('DashboardLayout', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        useNavigate.mockReturnValue(mockNavigate);
        window.innerHeight = 1000;
    });

    const renderWithProviders = (screenSize = {}, userProps = {}) => {
        useApp.mockReturnValue({
            screenSize: { xs: false, sm: false, md: false, lg: true, ...screenSize },
            isMobile: Boolean(screenSize.xs || screenSize.sm)
        });

        useUser.mockReturnValue({
            user: { name: 'Test User' },
            fetchUser: jest.fn().mockResolvedValue(),
            userFetched: true,
            logout: jest.fn(cb => cb && cb()),
            ...userProps
        });

        return render(
            <ThemeProvider theme={theme}>
                <BrowserRouter>
                    <DashboardLayout />
                </BrowserRouter>
            </ThemeProvider>
        );
    };

    it('should fetch user data on mount', () => {
        const mockFetchUser = jest.fn().mockResolvedValue();
        renderWithProviders({}, { fetchUser: mockFetchUser });
        expect(mockFetchUser).toHaveBeenCalled();
    });

    it('should register session expiry callback', () => {
        renderWithProviders();
        expect(registerSessionExpiryCallback).toHaveBeenCalled();
    });

    it('should navigate to login when session expires', () => {
        const mockLogout = jest.fn(cb => cb());
        renderWithProviders({}, { logout: mockLogout });

        const sessionExpiryCallback = registerSessionExpiryCallback.mock.calls[0][0];
        sessionExpiryCallback();

        expect(mockLogout).toHaveBeenCalled();
        expect(mockNavigate).toHaveBeenCalledWith('/login');
    });

    it('should navigate to login when user is not authenticated', async () => {
        renderWithProviders({}, { user: null, userFetched: true });
        await waitFor(() => {
            expect(mockNavigate).toHaveBeenCalledWith('/login');
        });
    });

    it('should show drawer on mobile when appbar is clicked', () => {
        renderWithProviders({ xs: true });
        fireEvent.click(screen.getByTestId('appbar'));
        expect(screen.getAllByTestId('sidebar')).toHaveLength(2);
    });

    it('should close drawer when clicking sidebar on mobile', () => {
        renderWithProviders({ xs: true });
        fireEvent.click(screen.getByTestId('appbar'));
        fireEvent.click(screen.getAllByTestId('sidebar')[1]);
        expect(screen.getAllByTestId('sidebar')).toHaveLength(2);
    });

    it('should auto-close drawer when screen size changes to desktop', async () => {
        renderWithProviders({ xs: true });
        fireEvent.click(screen.getByTestId('appbar'));

        useApp.mockReturnValue({
            screenSize: { xs: false, sm: false, md: false, lg: true },
            isMobile: false
        });

        fireEvent.resize(window);

        await waitFor(() => {
            expect(screen.getAllByTestId('sidebar')).toHaveLength(2);
        });
    });

    it('should handle fetchUser error', async () => {
        const mockError = new Error('Fetch failed');
        const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

        renderWithProviders({}, {
            fetchUser: jest.fn().mockRejectedValue(mockError)
        });

        await waitFor(() => {
            expect(consoleSpy).toHaveBeenCalledWith(mockError);
        });

        consoleSpy.mockRestore();
    });
});
