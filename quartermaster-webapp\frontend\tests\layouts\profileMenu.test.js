import { render, screen, fireEvent, within } from '@testing-library/react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../../src/hooks/UserHook';
import ProfileMenu from '../../src/layouts/ProfileMenu';
import { ThemeProvider } from '@mui/material';
import theme from '../../src/theme';

jest.mock('react-router-dom', () => ({
    useNavigate: jest.fn()
}));

jest.mock('../../src/hooks/UserHook', () => ({
    useUser: jest.fn()
}));

describe('ProfileMenu', () => {
    const mockNavigate = jest.fn();
    const mockLogout = jest.fn();
    const mockUser = { name: 'Test User' };

    beforeEach(() => {
        jest.clearAllMocks();
        useNavigate.mockReturnValue(mockNavigate);
        useUser.mockReturnValue({
            user: mockUser,
            logout: mockLogout
        });
    });

    const renderProfileMenu = (props = {}) => {
        return render(
            <ThemeProvider theme={theme}>
                <ProfileMenu {...props} />
            </ThemeProvider>
        );
    };

    it('should render logout button only when logoutOnly is true', () => {
        renderProfileMenu({ logoutOnly: true });
        expect(screen.getByTestId('LogoutIcon')).toBeInTheDocument();
        expect(screen.queryByRole('button', { name: /settings/i })).not.toBeInTheDocument();
    });

    it('should render avatar only when avatarOnly is true', () => {
        renderProfileMenu({ avatarOnly: true });
        expect(screen.queryByText(mockUser.name)).not.toBeInTheDocument();
    });


    it('should show "Anonymous" when no user is present', () => {
        useUser.mockReturnValue({ user: null, logout: mockLogout });
        renderProfileMenu();
        expect(screen.getByText('Anonymous')).toBeInTheDocument();
    });

    it('should open menu when clicked', () => {
        renderProfileMenu();
        const menuButton = screen.getByTestId('KeyboardArrowDownIcon').parentElement;
        fireEvent.click(menuButton);

        expect(screen.getByRole('menu')).toBeInTheDocument();
        expect(screen.getByTestId('KeyboardArrowUpIcon')).toBeInTheDocument();
    });

    it('should navigate to settings when settings option is clicked', () => {
        renderProfileMenu();
        const menuButton = screen.getByTestId('KeyboardArrowDownIcon').parentElement;
        fireEvent.click(menuButton);

        const settingsOption = screen.getByText('Settings');
        fireEvent.click(settingsOption);

        expect(mockNavigate).toHaveBeenCalledWith('/dashboard/settings');
    });

    it('should logout when logout option is clicked', () => {
        renderProfileMenu();
        const menuButton = screen.getByTestId('KeyboardArrowDownIcon').parentElement;
        fireEvent.click(menuButton);

        const logoutOption = screen.getByText('Logout');
        fireEvent.click(logoutOption);

        expect(mockLogout).toHaveBeenCalled();
    });

    it('should render menu with correct styles', () => {
        renderProfileMenu();
        const container = screen.getByText(mockUser.name).closest('.MuiGrid-container');
        expect(container).toHaveStyle({ gap: '10px' });
    });

    it('should show menu items with correct styling', () => {
        renderProfileMenu();
        fireEvent.click(screen.getByTestId('KeyboardArrowDownIcon').parentElement);

        const menuItems = screen.getAllByRole('menuitem');
        menuItems.forEach(item => {
            expect(item).toHaveStyle({
                minWidth: '250px',
                display: 'flex',
                justifyContent: 'space-between'
            });
        });
    });

    it('should handle missing user data gracefully', () => {
        useUser.mockReturnValue({ logout: mockLogout });
        renderProfileMenu();
        expect(screen.getByText('Anonymous')).toBeInTheDocument();
    });
});
