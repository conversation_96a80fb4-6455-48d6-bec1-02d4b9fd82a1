
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { LockOutlined, SwapVertOutlined } from '@mui/icons-material';
import { Grid, Typography } from '@mui/material';
import theme from "../../../../theme";

const SortableItem = ({ role, isDraggable }) => {
    const { attributes, listeners, setNodeRef, transform, transition } = useSortable({ id: role._id, disabled: !isDraggable });

    const style = {
        transform: CSS.Transform.toString(transform),
        transition,
        padding: '8px',
        display: 'flex',
        alignItems: 'center',
        backgroundColor: theme.palette.custom.darkBlue,
        border: `1px solid ${theme.palette.custom.borderColor}`,
        borderRadius: "5px",
        color: isDraggable ? '#FFFFFF' : "grey"
    };

    return (
        <Grid ref={setNodeRef} style={style} container alignItems="center" {...attributes} {...listeners}>
            {isDraggable ? <SwapVertOutlined style={{ marginRight: 8, cursor: "grab" }} /> : <LockOutlined style={{ marginRight: 8, cursor: "not-allowed" }} />}
            <Typography sx={{ minWidth: { xs: 150, sm: 300 } }} style={{ borderRadius: "200px" }}>{role.role_name}</Typography>
        </Grid>
    );
};

export default SortableItem;