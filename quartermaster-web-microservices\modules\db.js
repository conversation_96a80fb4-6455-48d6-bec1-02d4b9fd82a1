const mongoose = require('mongoose');

mongoose.set('strictQuery', false)

const db = {
    qm: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'quartermaster-dev' }),
    qmai: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'artifact_processor' }),
    qmShared: mongoose.createConnection(process.env.MONGO_URI, { dbName: 'quartermaster-shared' }),
}

db.qm.on('open', () => console.log('DB connected to Quartermaster'))
db.qmai.on('open', () => console.log('DB connected to QMAI'))
db.qmShared.on('open', () => console.log('DB connected to Quartermaster-Shared'));

db.qm.on('error', (err) => console.error(err))
db.qmai.on('error', (err) => console.error(err))
db.qmShared.on('error', (err) => console.error(err));

module.exports = db