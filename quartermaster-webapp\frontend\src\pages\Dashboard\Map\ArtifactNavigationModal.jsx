import { useCallback, useEffect, useState } from "react";
import { <PERSON>alog, DialogContent, Button, Modal, Box, Grid, IconButton, Typography, Skeleton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useApp } from "../../../hooks/AppHook";
import dayjs from "dayjs";
import { userValues } from "../../../utils";
import s3Controller from "../../../controllers/S3.controller";
import VideoPlayer from "../../../components/VideoPlayer";
import { useUser } from "../../../hooks/UserHook.jsx";

const ArtifactNavigationModal = ({ artifacts, open, onClose }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [mediaUrls, setMediaUrls] = useState(() => Array(artifacts.length).fill(null)); // Array to store URLs
    const [isLoading, setIsLoading] = useState(false);
    const currentArtifact = artifacts[currentIndex];
    const [videoViewModalOpen, setVideoViewModalOpen] = useState(false);
    const { screenSize, timezone } = useApp();
    const { user } = useUser();
    // Function to fetch the URL for a specific artifact
    const fetchArtifactUrl = useCallback(
        async (index) => {
            if (!mediaUrls[index]) {
                // Fetch only if not already fetched
                setIsLoading(true);
                try {
                    const url = await s3Controller.fetchSignedUrl(artifacts[index], "cachedSrc");
                    setMediaUrls((prevUrls) => {
                        const updatedUrls = [...prevUrls];
                        updatedUrls[index] = url; // Store the fetched URL at the corresponding index
                        return updatedUrls;
                    });
                } catch (err) {
                    console.error("Error fetching URL:", err);
                } finally {
                    setIsLoading(false);
                }
            }
        },
        [mediaUrls, artifacts],
    );

    // Fetch URL when modal opens or the current artifact changes
    useEffect(() => {
        if (open && currentArtifact) {
            fetchArtifactUrl(currentIndex);
        }
    }, [currentIndex]);

    const handlePrev = () => {
        setCurrentIndex((prevIndex) => (prevIndex > 0 ? prevIndex - 1 : artifacts.length - 1));
    };

    const handleNext = () => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % artifacts.length);
    };

    const currentMediaUrl = mediaUrls[currentIndex];

    return (
        <Dialog
            open={open}
            onClose={onClose}
            sx={{
                "& .MuiPaper-root": {
                    backgroundColor: "#25292f",
                    borderRadius: "8px",
                },
            }}
        >
            <DialogContent>
                <Grid container display={"flex"} flexDirection={"row"} justifyContent={"space-between"} alignItems="center" marginBottom={2}>
                    <Grid>
                        <Typography variant="h6" style={{ color: "white" }}>
                            Artifact {currentIndex + 1} / {artifacts.length}
                        </Typography>
                    </Grid>
                    <Grid>
                        <IconButton
                            onClick={onClose}
                            sx={{
                                border: "1px solid #FFFFFF",
                                borderRadius: "50%",
                                transition: "all .3s",
                                padding: "5px",
                                ":hover": {
                                    backgroundColor: "#FFFFFF",
                                    "& .MuiSvgIcon-root": {
                                        stroke: "#343B44",
                                    },
                                },
                            }}
                        >
                            <CloseIcon
                                sx={{
                                    color: "white",
                                    fontSize: "16px",
                                    strokeWidth: 2,
                                    fill: "none",
                                    stroke: "#FFFFFF",
                                }}
                            />
                        </IconButton>
                    </Grid>
                </Grid>
                <Grid sx={{ marginBottom: 2 }}>
                    {isLoading ? (
                        <Skeleton animation="wave" variant="rectangular" height={"200px"} width={"100%"} />
                    ) : currentMediaUrl && currentArtifact.video_path ? (
                        <>
                            <VideoPlayer
                                src={currentMediaUrl}
                                onFullscreen={() => setVideoViewModalOpen(true)}
                                styles={{
                                    width: "100%",
                                    height: "200px",
                                    objectFit: "cover",
                                    maxWidth: "300px",
                                }}
                            />
                            {videoViewModalOpen && (
                                <Modal open={videoViewModalOpen} onClose={() => setVideoViewModalOpen(false)}>
                                    <Box
                                        sx={{
                                            width: "100%",
                                            height: "100%",
                                            backgroundColor: "#000",
                                        }}
                                    >
                                        <VideoPlayer
                                            src={currentMediaUrl}
                                            isFullscreen
                                            onFullscreen={() => setVideoViewModalOpen(false)}
                                            styles={{ width: "100%", height: "100%" }}
                                        />
                                    </Box>
                                </Modal>
                            )}
                        </>
                    ) : currentMediaUrl ? (
                        <img
                            src={currentMediaUrl}
                            alt="Artifact"
                            style={{
                                width: "100%",
                                height: "200px",
                                objectFit: "cover",
                                maxWidth: "300px",
                                borderRadius: "5px",
                            }}
                        />
                    ) : (
                        <Skeleton animation="wave" variant="rectangular" height={"200px"} width={"100%"} sx={{ borderRadius: "5px" }} />
                    )}
                </Grid>
                {currentArtifact.category && (
                    <p
                        style={{
                            color: "white",
                            fontWeight: 300,
                            width: screenSize.xs ? "200px" : "300px",
                        }}
                    >
                        <strong>Category:</strong> {currentArtifact.category}
                    </p>
                )}
                {currentArtifact.super_category && (
                    <p
                        style={{
                            color: "white",
                            fontWeight: 300,
                            width: screenSize.xs ? "200px" : "300px",
                        }}
                    >
                        <strong>Super Category:</strong> {currentArtifact.super_category || "Unspecified category"}
                    </p>
                )}
                {currentArtifact.color && (
                    <p
                        style={{
                            color: "white",
                            fontWeight: 300,
                            width: screenSize.xs ? "200px" : "300px",
                        }}
                    >
                        <strong>Color:</strong> {currentArtifact.color}
                    </p>
                )}
                {currentArtifact.size && (
                    <p
                        style={{
                            color: "white",
                            fontWeight: 300,
                            width: screenSize.xs ? "200px" : "300px",
                        }}
                    >
                        <strong>Size:</strong> {currentArtifact.size}
                    </p>
                )}
                <p
                    style={{
                        color: "white",
                        fontWeight: 300,
                        width: screenSize.xs ? "200px" : "300px",
                    }}
                >
                    <strong>Timestamp:</strong> {dayjs(currentArtifact.timestamp).tz(timezone).format(userValues.dateTimeFormat(user))}
                </p>
                <div
                    style={{
                        display: "flex",
                        justifyContent: "space-between",
                        marginTop: 20,
                    }}
                >
                    <Button onClick={handlePrev} disabled={artifacts.length <= 1} style={{ color: "white" }}>
                        Previous
                    </Button>
                    <Button onClick={handleNext} disabled={artifacts.length <= 1} style={{ color: "white" }}>
                        Next
                    </Button>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default ArtifactNavigationModal;
