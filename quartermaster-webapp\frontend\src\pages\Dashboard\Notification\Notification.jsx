import { Grid, Typo<PERSON>, MenuItem, Tooltip, CircularProgress, Box, Chip, IconButton, Menu, Skeleton } from "@mui/material";
import { useEffect, useState } from "react";
import axiosInstance from "../../../axios";
import NotificationsIcon from "@mui/icons-material/Notifications";
import NotificationsOffIcon from "@mui/icons-material/NotificationsOff";
import theme from "../../../theme";
import { ArrowDropDown, SentimentVeryDissatisfied } from "@mui/icons-material";
import { DataGrid } from "@mui/x-data-grid";
import DeleteNotificationModal from "./DeleteNotificationModal";
import EnableNotificationModal from "./EnableNotificationModal";
import NotificationCustomFooter from "./NotificationCustomFooter";
import * as FlagIcon from "country-flag-icons/react/3x2";
import DeleteButton from "../../../components/DeleteButton";
import EditButton from "../../../components/EditButton";
import CreateEditNotificationModal from "./CreateEditNotificationModal";

const Notification = ({ vessels, showCreateEditModal, setShowCreateEditModal, isEditModal, emailDomains, setIsEditModal }) => {
    const [notification, setNotification] = useState([]);
    const [filteredNotification, setFilteredNotification] = useState([]);
    const [isLoading, setIsLoading] = useState(true);

    const [totalCount, setTotalCount] = useState(0);
    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    const [editNotifcation, setEditNotificaiton] = useState();

    const [filterItem, setFilterItem] = useState({});

    const [deleteKey, setDeleteKey] = useState();
    const [deleting, setDeleting] = useState();

    const [enableKey, setEnableKey] = useState();
    const [enable, setEnable] = useState();

    const [notificationAddLoad, setNotificationAddLoad] = useState(false);
    // const [parentSize, setParentSize] = useState({ width: 0, height: 0 });

    // useEffect(() => {
    //     const updateSize = () => {
    //         // Find the child with class `MuiDataGrid-main`
    //         const mainElement = document.querySelector("#parent-ref .MuiDataGrid-main");
    //         if (mainElement) {
    //             const { clientWidth, clientHeight } = mainElement;
    //             setParentSize({ width: clientWidth, height: clientHeight });
    //         }
    //     };

    //     updateSize();
    //     window.addEventListener("resize", updateSize);
    //     return () => window.removeEventListener("resize", updateSize);
    // }, []);

    const fetchFilters = async () => {
        try {
            const filterItems = await axiosInstance.get("/artifacts/filters").then((res) => res.data);
            // .catch(err => {
            //     console.error(`Error fetching filters in Notification`, err)
            // });
            if (Object.keys(filterItems).length !== 0) {
                setFilterItem(filterItems);
            }
        } catch (err) {
            console.error(`Error fetching filters in Notification`, err);
        }
    };

    const fetchNotification = async () => {
        setIsLoading(true);
        try {
            const params = new URLSearchParams({ page, page_size: rowsPerPage });

            const { data, total_documents } = await axiosInstance.get(`/notificationsAlerts?${params.toString()}`).then((res) => res.data);
            // .catch(err => {
            //     console.error(`Error fetching notifications`, err)
            // });

            if (Array.isArray(data) && data.length > 0) {
                const addedKey = data.map((e, i) => ({ serial: i + 1, ...e }));
                setNotification(addedKey);
                setTotalCount(total_documents);
            } else {
                setNotification([]);
            }
            setIsLoading(false);
        } catch (err) {
            setIsLoading(false);
            console.error(`Error fetching artifacts in Events`, err);
        }
    };

    useEffect(() => {
        async function fetchData() {
            await fetchFilters();
        }

        fetchData();
    }, []);

    useEffect(() => {
        setFilteredNotification(notification);
    }, [notification]);

    useEffect(() => {
        fetchNotification();
    }, [filterItem, page, rowsPerPage, deleting, notificationAddLoad, enable]); //Instead of refresing the api we have to emit event from backend

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handlePageSizeChange = (event) => {
        setRowsPerPage(event.target.value);
        setPage(1);
    };

    const columns = [
        {
            field: "serial",
            headerName: "",
            maxWidth: 50,
            renderCell: (index) => {
                return (page - 1) * rowsPerPage + index.row.serial;
            },
        },
        {
            field: "unit_id",
            headerName: "Vessels",
            minWidth: 250,
            renderCell: (params) => {
                // eslint-disable-next-line react-hooks/rules-of-hooks
                const [anchorEl, setAnchorEl] = useState(null);

                if (!vessels || vessels.length === 0) {
                    return <Skeleton variant="text" animation="pulse" width={250} sx={{ display: "flex", alignItems: "center", height: "100%" }} />;
                }

                const unit_ids = params.row.unit_id;
                const unit_names = vessels.filter((v) => unit_ids.includes("all") || unit_ids.includes(v.unit_id)).map((v) => v.name);
                // if (!Array.isArray(params.row.title)) {
                //     params.row.title = params.row.title ? [params.row.title] : [];
                // }
                // const title =
                //     params.row.title && params.row.title.length > 0
                //         ? params.row.title.includes("all")
                //             ? vessels.map((vessel) => vessel.name)
                //             : params.row.title
                //         : [];
                const handleClick = (event) => {
                    setAnchorEl(event.currentTarget);
                };
                const handleClose = () => {
                    setAnchorEl(null);
                };
                return (
                    <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>
                        <Chip
                            sx={{
                                paddingLeft: 0.5,
                                paddingRight: 2,
                                display: "flex",
                                flexDirection: "row-reverse",
                                width: "fit-content",
                                minWidth: { xs: "100%", xl: "50%" },
                                borderRadius: "5px",
                                justifyContent: "space-between",
                                cursor: "pointer",
                            }}
                            icon={<ArrowDropDown fontSize="small" sx={{ cursor: "pointer", opacity: 0.5 }} />}
                            label="Observing Vessels"
                            onClick={handleClick}
                        />
                        <Menu
                            anchorEl={anchorEl}
                            open={Boolean(anchorEl)}
                            onClose={handleClose}
                            sx={{
                                maxHeight: "400px",
                                "& .MuiMenu-paper": {
                                    maxWidth: `${anchorEl?.offsetWidth}px !important`,
                                    width: `${anchorEl?.offsetWidth}px !important`,
                                    transition: "none !important",
                                },
                                "& .MuiMenuItem-root": {
                                    whiteSpace: "normal",
                                    wordBreak: "break-word",
                                },
                            }}
                        >
                            {unit_names.map((name, index) => (
                                <MenuItem key={index} onClick={handleClose}>
                                    {name}
                                </MenuItem>
                            ))}
                        </Menu>
                    </Box>
                );
            },
        },
        {
            field: "super_category",
            headerName: "Category",
            minWidth: 150,
            renderCell: (params) => {
                // eslint-disable-next-line react-hooks/rules-of-hooks
                const [anchorEl, setAnchorEl] = useState(null);
                if (!Array.isArray(params.row.super_category)) {
                    params.row.super_category = params.row.super_category ? [params.row.super_category] : [];
                }
                const superCtg =
                    params.row.super_category && params.row.super_category.length > 0
                        ? params.row.super_category.includes("all")
                            ? filterItem["superCategories"]
                            : params.row.super_category
                        : [];
                const handleClick = (event) => {
                    setAnchorEl(event.currentTarget);
                };
                const handleClose = () => {
                    setAnchorEl(null);
                };
                return (
                    <>
                        {superCtg && superCtg.length > 0 && (
                            <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>
                                <Chip
                                    sx={{
                                        paddingLeft: 0.5,
                                        paddingRight: 2,
                                        display: "flex",
                                        flexDirection: "row-reverse",
                                        width: "fit-content",
                                        minWidth: { xs: "100%", xl: "50%" },
                                        borderRadius: "5px",
                                        justifyContent: "space-between",
                                        cursor: "pointer",
                                    }}
                                    icon={<ArrowDropDown fontSize="small" sx={{ cursor: "pointer", opacity: 0.5 }} />}
                                    label="Category"
                                    onClick={handleClick}
                                />
                                <Menu
                                    anchorEl={anchorEl}
                                    open={Boolean(anchorEl)}
                                    onClose={handleClose}
                                    sx={{
                                        maxHeight: "400px",
                                        "& .MuiMenu-paper": {
                                            maxWidth: `${anchorEl?.offsetWidth}px !important`,
                                            width: `${anchorEl?.offsetWidth}px !important`,
                                            transition: "none !important",
                                        },
                                        "& .MuiMenuItem-root": {
                                            whiteSpace: "normal",
                                            wordBreak: "break-word",
                                        },
                                    }}
                                >
                                    {superCtg.map((sup, index) => (
                                        <MenuItem key={index} onClick={handleClose}>
                                            {sup}
                                        </MenuItem>
                                    ))}
                                </Menu>
                            </Box>
                        )}
                    </>
                );
            },
            valueGetter: (value) => value.row?.super_category || "",
        },
        // {
        //     field: 'sub_category', headerName: 'Sub Category', minWidth: 150,
        //     renderCell: (params) => {
        //         const [anchorEl, setAnchorEl] = useState(null);
        //         if (!Array.isArray(params.row.sub_category)) {
        //             params.row.sub_category = params.row.sub_category ? [params.row.sub_category] : []
        //         }
        //         const subCtg = (params.row.sub_category && params.row.sub_category.length > 0) ? params.row.sub_category.includes('all') ? filterItem['categories'] : params.row.sub_category : [];
        //         const handleClick = (event) => {
        //             setAnchorEl(event.currentTarget);
        //         };
        //         const handleClose = () => {
        //             setAnchorEl(null);
        //         };

        //         return (
        //             <>
        //                 {
        //                     subCtg && subCtg.length > 0 &&
        //                     <Box sx={{ display: 'flex', alignItems: 'center', height: '100%' }}>
        //                         <Chip
        //                             sx={{
        //                                 paddingLeft: 0.5,
        //                                 paddingRight: 2,
        //                                 display: 'flex',
        //                                 flexDirection: 'row-reverse',
        //                                 width: 'fit-content',
        //                                 minWidth: { xs: '100%', xl: '50%' },
        //                                 borderRadius: '5px',
        //                                 justifyContent: 'space-between',
        //                                 cursor: 'pointer',
        //                             }}
        //                             icon={<ArrowDropDown fontSize="small" sx={{ cursor: 'pointer', opacity: 0.5 }} />}
        //                             label="Sub Ctg"

        //                             onClick={handleClick}
        //                         />
        //                         <Menu
        //                             anchorEl={anchorEl}
        //                             open={Boolean(anchorEl)}
        //                             onClose={handleClose}
        //                             sx={{
        //                                 width: '100%'
        //                             }}
        //                         >
        //                             {
        //                                 subCtg.map((sub, index) => (
        //                                     <MenuItem key={index} onClick={handleClose}>
        //                                         {sub}
        //                                     </MenuItem>
        //                                 ))
        //                             }

        //                         </Menu>
        //                     </Box>
        //                 }
        //             </>
        //         );
        //     },
        //     valueGetter: (value) => (value.row?.sub_category || ''),
        // },
        {
            field: "country_flags",
            headerName: "Flags",
            minWidth: 250,
            renderCell: (params) => {
                // eslint-disable-next-line react-hooks/rules-of-hooks
                const [anchorEl, setAnchorEl] = useState(null);

                const flags =
                    params.row.country_flags?.length > 0
                        ? params.row.country_flags.includes("all")
                            ? filterItem?.countryFlags
                            : params.row.country_flags.map((flag) => {
                                  return {
                                      name: flag,
                                      code: filterItem.countryFlags ? filterItem.countryFlags.find((e) => e.name === flag)?.code : "",
                                  };
                              })
                        : [];

                const handleClick = (event) => {
                    setAnchorEl(event.currentTarget);
                };
                const handleClose = () => {
                    setAnchorEl(null);
                };

                return (
                    <>
                        {
                            flags && flags.length > 0 && (
                                <Box sx={{ display: "flex", alignItems: "center", height: "100%", width: "100%" }}>
                                    <Chip
                                        sx={{
                                            paddingLeft: 0.5,
                                            paddingRight: 2,
                                            display: "flex",
                                            flexDirection: "row-reverse",
                                            width: { xs: "100%", sm: "300px" },
                                            minWidth: { xs: "100%", xl: "200px" },
                                            borderRadius: "5px",
                                            justifyContent: "space-between",
                                            cursor: "pointer",
                                        }}
                                        icon={<ArrowDropDown fontSize="small" sx={{ cursor: "pointer", opacity: 0.5 }} />}
                                        label="Flags"
                                        onClick={handleClick}
                                    />
                                    <Menu
                                        anchorEl={anchorEl}
                                        open={Boolean(anchorEl)}
                                        onClose={handleClose}
                                        sx={{
                                            maxHeight: "400px",
                                            "& .MuiMenu-paper": {
                                                maxWidth: `${anchorEl?.offsetWidth}px !important`,
                                                width: `${anchorEl?.offsetWidth}px !important`,
                                                transition: "none !important",
                                            },
                                            "& .MuiMenuItem-root": {
                                                whiteSpace: "normal",
                                                wordBreak: "break-word",
                                            },
                                        }}
                                    >
                                        {flags.map((flag, index) => {
                                            const ComponentToRender = FlagIcon[flag.code];

                                            return (
                                                <MenuItem key={index} onClick={handleClose}>
                                                    {ComponentToRender && (
                                                        <ComponentToRender
                                                            title={flag.name}
                                                            style={{
                                                                marginRight: "8px",
                                                                height: "20px",
                                                                minWidth: "30px",
                                                            }}
                                                        />
                                                    )}
                                                    {flag.name}
                                                </MenuItem>
                                            );
                                        })}
                                    </Menu>
                                </Box>
                            )
                            //     :
                            // <Chip
                            //     sx={{
                            //         paddingLeft: 0.5,
                            //         paddingRight: 2,
                            //         display: 'flex',
                            //         flexDirection: 'row-reverse',
                            //         width: 'fit-content',
                            //         minWidth: { xs: '100%', xl: '50%' },
                            //         borderRadius: '5px',
                            //         justifyContent: 'left',
                            //         cursor: 'pointer',
                            //     }}
                            //     label="No Flag Select"
                            // />
                        }
                    </>
                );
            },
            valueGetter: (value) => value.row?.country_flags || "",
        },
        {
            field: "type",
            headerName: "Notification Preference",
            minWidth: 100,
            renderCell: (params) => (
                <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>{params.value === "both" ? "email / app" : params.value}</Box>
            ),
        },
        // {
        //     field: "receivers",
        //     headerName: "Configured Emails",
        //     minWidth: 100,
        //     renderCell: ({ row }) => <Box sx={{ display: "flex", alignItems: "center", height: "100%" }}>{row.receivers?.length + 1}</Box>,
        // },
        {
            field: "actions",
            headerName: "Actions",
            minWidth: 150,
            headerAlign: "center",
            renderCell: (params) => (
                <Grid container justifyContent={"center"} spacing={1}>
                    <Grid>
                        <EditButton
                            onClick={() => {
                                setEditNotificaiton(params.row);
                                setShowCreateEditModal(true);
                                setIsEditModal(true);
                            }}
                        />
                    </Grid>
                    <Grid>
                        {enable === params.row._id ? (
                            <CircularProgress size={18} />
                        ) : (
                            <Tooltip enterDelay={300} title={params.row.is_enabled ? "Enabled" : "Disabled"} placement="bottom">
                                <IconButton
                                    onClick={() => setEnableKey(params.row)}
                                    sx={{
                                        background: "#E600000D",
                                        border: `1px solid ${theme.palette.custom.borderColor}`,
                                        borderRadius: "5px",
                                        padding: "8px",
                                    }}
                                >
                                    {params.row.is_enabled ? (
                                        <NotificationsIcon color="success" sx={{ fontSize: "18px" }} />
                                    ) : (
                                        <NotificationsOffIcon color="error" sx={{ fontSize: "18px" }} />
                                    )}
                                </IconButton>
                            </Tooltip>
                        )}
                    </Grid>
                    <Grid>
                        {deleting === params.row._id ? <CircularProgress size={18} /> : <DeleteButton onClick={() => setDeleteKey(params.row)} />}
                    </Grid>
                </Grid>
            ),
            sortable: false,
        },
    ];

    const columnsWithouFilters = [
        ...columns.map((col) => ({
            ...col,
            filterable: false,
            sortable: false,
            resizable: false,
            disableColumnMenu: true,
            disableReorder: true,
            disableExport: true,
            flex: 1,
        })),
    ];

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid overflow={"auto"} size="grow">
                <DataGrid
                    loading={isLoading}
                    disableRowSelectionOnClick
                    rows={filteredNotification}
                    columns={columnsWithouFilters}
                    getRowId={(row) => row._id}
                    slots={{
                        footer: () => (
                            <NotificationCustomFooter
                                page={page}
                                rowsPerPage={rowsPerPage}
                                totalRows={totalCount}
                                onPageChange={handlePageChange}
                                onRowsPerPageChange={handlePageSizeChange}
                            />
                        ),
                        noRowsOverlay: () => (
                            <Box
                                sx={{
                                    display: "flex",
                                    flexDirection: "column",
                                    alignItems: "center",
                                    justifyContent: "center",
                                    width: "100%",
                                    height: "100%",
                                }}
                            >
                                <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
                                <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                                    No data available
                                </Typography>
                            </Box>
                        ),
                    }}
                />
            </Grid>
            <DeleteNotificationModal deleteKey={deleteKey} setDeleteKey={setDeleteKey} setDeleting={setDeleting} />
            {enableKey && <EnableNotificationModal enableKey={enableKey} setEnableKey={setEnableKey} setEnable={setEnable} />}
            {editNotifcation !== undefined && isEditModal && (
                <CreateEditNotificationModal
                    showModal={showCreateEditModal}
                    setShowModal={setShowCreateEditModal}
                    editNotifcation={editNotifcation}
                    setEditNotificaiton={setEditNotificaiton}
                    notificationAddLoad={notificationAddLoad}
                    setNotificationAddLoad={setNotificationAddLoad}
                    filterItems={filterItem}
                    vessels={vessels}
                    emailDomains={emailDomains}
                    isEditModal={isEditModal}
                />
            )}
            {vessels.length > 0 && filterItem && !isEditModal && (
                <CreateEditNotificationModal
                    showModal={showCreateEditModal}
                    setShowModal={setShowCreateEditModal}
                    notificationAddLoad={notificationAddLoad}
                    setNotificationAddLoad={setNotificationAddLoad}
                    filterItems={filterItem}
                    vessels={vessels}
                    emailDomains={emailDomains}
                    isEditModal={isEditModal}
                />
            )}
        </Grid>
    );
};

export default Notification;
