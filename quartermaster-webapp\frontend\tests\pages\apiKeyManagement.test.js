import { render, screen, fireEvent } from '@testing-library/react';
import ApiKeyManagement from '../../src/pages/Dashboard/ApiKeys/ApiKeyManagement';
import { useUser } from '../../src/hooks/UserHook';
import { permissions } from '../../src/utils';
import React from 'react';
import { useApp } from '../../src/hooks/AppHook';

jest.mock('../../src/hooks/UserHook');
jest.mock('../../src/hooks/AppHook');
jest.mock('../../src/utils');
jest.mock('../../src/pages/Dashboard/ApiKeys//ApiKeys', () => "ApiKeys");

describe('ApiKeyManagement', () => {
    const mockSetTab = jest.fn();

    beforeEach(() => {
        useApp.mockReturnValue({ isMobile: true });
        jest.clearAllMocks();
    });

    it('should render the ApiKeyManagement component correctly when the user has the required permission', () => {
        useUser.mockReturnValue({ user: { hasPermissions: jest.fn(() => true) } });
        permissions.manageApiKeys = 'manageApiKeys';

        render(<ApiKeyManagement />);

        expect(screen.getByText('Api Keys')).toBeInTheDocument();
        expect(screen.getByRole('tab')).toBeInTheDocument();
    });

    it('should not render the Api Keys tab when the user does not have the required permission', () => {
        useUser.mockReturnValue({ user: { hasPermissions: jest.fn(() => false) } });
        permissions.manageApiKeys = 'manageApiKeys';

        render(<ApiKeyManagement />);

        expect(screen.queryByText('Api Keys')).not.toBeInTheDocument();
    });

    it('switches tabs correctly when clicked', () => {
        const mockUserWithPermissions = {
            hasPermissions: (permissionArray) => permissionArray.includes(permissions.manageApiKeys),
        };
        useUser.mockReturnValue({ user: mockUserWithPermissions });

        const originalUseMemo = jest.requireActual('react').useMemo;
        jest.spyOn(React, 'useMemo').mockImplementationOnce((fn) => {
            return originalUseMemo(() => [
                { value: 'api', label: 'Api', component: <div>Api Component</div>, display: true },
                { value: 'keys', label: 'Keys', component: <div>Keys Component</div>, display: true },
                { value: 'errors', label: 'Errors', component: <div>Errors Component</div>, display: true },
            ], []);
        });

        render(<ApiKeyManagement />);

        const tabs = ['Api', 'Keys', 'Errors'];

        tabs.forEach(tabLabel => {
            const tab = screen.getByRole('tab', { name: new RegExp(tabLabel, 'i') });
            fireEvent.click(tab);

            expect(tab).toHaveAttribute('aria-selected', 'true');
            expect(screen.getByText(new RegExp(`${tabLabel} component`, 'i'))).toBeVisible();
        });
    });

    it('should not display any tabs if no tabs are available', () => {
        useUser.mockReturnValue({ user: { hasPermissions: jest.fn(() => false) } });
        permissions.manageApiKeys = 'manageApiKeys';

        render(<ApiKeyManagement />);

        expect(screen.queryByRole('tab')).toBeNull();
    });

    it('should correctly handle the useEffect to set the active tab based on the permission', () => {
        useUser.mockReturnValue({ user: { hasPermissions: jest.fn(() => true) } });
        permissions.manageApiKeys = 'manageApiKeys';

        render(<ApiKeyManagement />);

        expect(screen.getByText('Api Keys')).toBeInTheDocument();
    });

    it('should render the ApiKeys component when the tab is selected', () => {
        useUser.mockReturnValue({ user: { hasPermissions: jest.fn(() => true) } });
        permissions.manageApiKeys = 'manageApiKeys';

        render(<ApiKeyManagement />);

        const apiKeyTab = screen.getByText('Api Keys');
        fireEvent.click(apiKeyTab);

        expect(screen.getByText('Api Keys')).toBeInTheDocument();
    });

    it('handles search input changes', () => {
        useUser.mockReturnValue({ user: { hasPermissions: jest.fn(() => true) } });
        render(<ApiKeyManagement />);

        const searchInput = screen.getByPlaceholderText('Search');
        fireEvent.change(searchInput, { target: { value: 'test search' } });

        expect(searchInput).toHaveValue('test search');
    });

    it('toggles filter modal on filter button click', () => {
        useUser.mockReturnValue({ user: { hasPermissions: jest.fn(() => true) } });
        useApp.mockReturnValue({ isMobile: false });

        render(<ApiKeyManagement />);

        const filterButton = screen.getByRole('button', { name: /Add API Key/i });
        fireEvent.click(filterButton);

        expect(screen.getByText('Api Keys')).toBeInTheDocument();
    });
});
