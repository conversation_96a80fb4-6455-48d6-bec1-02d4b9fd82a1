import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import StatisticsPastWeeks from "../../src/pages/Dashboard/Statistics/StatisticsPastWeeks";
import axiosInstance from "../../src/axios";
import { ThemeProvider, createTheme } from "@mui/material/styles";
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

jest.mock('../../src/axios');
jest.mock('../../src/components/KPICard', () => "KPICard");
jest.mock('../../src/components/Charts/BarChart', () => "BarChart");
jest.mock('../../src/components/Charts/PieChart', () => "PieChart");
jest.mock('../../src/components/Charts/LineChart', () => "LineChart");
jest.mock('../../src/components/Charts/HeatmapChart', () => "HeatmapChart");

const mockTheme = createTheme();

const renderWithTheme = (component) => {
    return render(<ThemeProvider theme={mockTheme}>{component}</ThemeProvider>);
};

describe("StatisticsPastWeeks", () => {
    const mockVesselsInfo = [
        { unit_id: "vessel1", name: "Vessel One" },
        { unit_id: "vessel2", name: "Vessel Two" },
    ];

    const mockStatistics = [
        {
            _id: "stat1",
            fromTimestamp: "2024-11-10T00:00:00.000Z",
            toTimestamp: "2024-11-16T23:59:59.999Z",
            stats: {
                totalArtifactsWithAtleastOneVessel: {
                    confidenceAbove40: 10,
                    confidenceAbove80: 5,
                },
                totalVesselsByHoursUTC: { "0": 10, "1": 5 },
                totalSensorsOnlineDuration: { vessel1: 3600000, vessel2: 7200000 },
                totalSensorsDurationAtSea: { vessel1: 1800000, vessel2: 3600000 },
                totalSmartmastsDistanceTraveled: { vessel1: 1000, vessel2: 2000 },
                totalSmartmastsDistanceTraveledbyAllMiles: 1.86,
                listOfTextsExtracted: ["Sample Text 1", "Sample Text 2"],
                totalVesselsSuperCategorized: { cat1: 5, cat2: 10 },
                totalVesselsSubCategorized: { subcat1: 7, subcat2: 8 },
                totalVesselsDetectedbySensors: { vessel1: 3, vessel2: 4 },
                totalVesselsWithCountryFlag: { USA: 6, Canada: 2 },
                totalVesselsByWeekDayHoursUTC: { "2024-11-10T00:00:00.000Z": 2 },
            },
        },
        {
            _id: "stat2",
            fromTimestamp: "2024-11-10T00:00:00.000Z",
            toTimestamp: "2024-11-16T23:59:59.999Z",
            stats: {
                totalArtifactsWithAtleastOneVessel: {
                    confidenceAbove40: 10,
                    confidenceAbove80: 5,
                },
                totalVesselsByHoursUTC: { "0": 10, "1": 5 },
                totalSensorsOnlineDuration: { vessel1: 3600000, vessel2: 7200000 },
                totalSensorsDurationAtSea: { vessel1: 1800000, vessel2: 3600000 },
                totalSmartmastsDistanceTraveled: { vessel1: 1000, vessel2: 2000 },
                totalSmartmastsDistanceTraveledbyAllMiles: 1.86,
                listOfTextsExtracted: ["Sample Text 1", "Sample Text 2"],
                totalVesselsSuperCategorized: { cat1: 5, cat2: 10 },
                totalVesselsSubCategorized: { subcat1: 7, subcat2: 8 },
                totalVesselsDetectedbySensors: { vessel1: 3, vessel2: 4 },
                totalVesselsWithCountryFlag: { USA: 6, Canada: 2 },
                totalVesselsByWeekDayHoursUTC: { "2024-11-10T00:00:00.000Z": 2 },
            },
        },
    ];


    afterEach(() => {
        jest.clearAllMocks();
    });



    it("displays extracted texts in the data grid", async () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === "/vessels/info") {
                return Promise.resolve({ data: mockVesselsInfo });
            }
            if (url === "/statistics") {
                return Promise.resolve({ data: mockStatistics });
            }
        });
        renderWithTheme(<StatisticsPastWeeks />);
        expect(axiosInstance.get).toHaveBeenCalled();
    });

    it("renders a loading indicator initially", () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === "/vessels/info") {
                return Promise.resolve({ data: mockVesselsInfo });
            }
            if (url === "/statistics") {
                return Promise.resolve({ data: mockStatistics });
            }
        });
        renderWithTheme(<StatisticsPastWeeks />);
        expect(screen.getByRole("progressbar")).toBeInTheDocument();
    });

    it("fetches and displays vessel and statistics data", async () => {
        axiosInstance.get.mockImplementation((url) => {
            if (url === "/vessels/info") {
                return Promise.resolve({ data: mockVesselsInfo });
            }
            if (url === "/statistics") {
                return Promise.resolve({ data: mockStatistics });
            }
        });
        renderWithTheme(<StatisticsPastWeeks />);
        await waitFor(() => {
            expect(
                screen.getByText((content, element) =>
                    content.includes('Data for the time period from:')
                )
            ).toBeInTheDocument();
        });

        fireEvent.click(screen.getByTestId('ArrowForwardIosIcon'));
        fireEvent.click(screen.getByTestId('ArrowBackIosIcon'));
    });

    it("should handle transformKeys with missing vessel info", async () => {
        const statsWithUnknownVessels = {
            ...mockStatistics[0],
            stats: {
                ...mockStatistics[0].stats,
                totalVesselsDetectedbySensors: {
                    vessel1: 3,
                    unknown_vessel: 5,
                    vessel2: 4,
                    another_unknown: 6
                }
            }
        };

        axiosInstance.get.mockImplementation((url) => {
            if (url === "/statistics") {
                return Promise.resolve({ data: [statsWithUnknownVessels] });
            }
            return Promise.resolve({ data: mockVesselsInfo });
        });

        renderWithTheme(<StatisticsPastWeeks />);

        await waitFor(() => {
            expect(axiosInstance.get).toHaveBeenCalled();
        });
    });

    it("should display 'No data available' message when stats are empty", async () => {
        const emptyStats = {
            _id: "stat1",
            fromTimestamp: "2024-11-10T00:00:00.000Z",
            toTimestamp: "2024-11-16T23:59:59.999Z",
            stats: {
                totalArtifactsWithAtleastOneVessel: {
                    confidenceAbove40: 0,
                    confidenceAbove80: 0,
                },
                totalVesselsByHoursUTC: {},
                totalSensorsOnlineDuration: {},
                totalSensorsDurationAtSea: {},
                totalSmartmastsDistanceTraveled: {},
                totalSmartmastsDistanceTraveledbyAllMiles: 0,
                listOfTextsExtracted: [],
                totalVesselsSuperCategorized: {},
                totalVesselsSubCategorized: {},
                totalVesselsDetectedbySensors: {},
                totalVesselsWithCountryFlag: {},
                totalVesselsByWeekDayHoursUTC: {},
                totalVesselsByHoursLocal: {},
                totalVesselsByWeekDayHoursLocal: {}
            }
        };

        axiosInstance.get.mockImplementation((url) => {
            if (url === "/vessels/info") {
                return Promise.resolve({ data: mockVesselsInfo });
            }
            if (url === "/statistics") {
                return Promise.resolve({ data: [emptyStats] });
            }
        });

        renderWithTheme(<StatisticsPastWeeks />);

        await waitFor(() => {
            const noDataMessages = screen.getAllByText('No data available for specified time range');
            expect(noDataMessages).toHaveLength(9);
        });
    });
});
