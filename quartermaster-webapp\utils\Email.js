const logo = "https://portal.quartermaster.us/email/header.png";
const ship = "https://portal.quartermaster.us/email/ship.png";
const privacy = "https://quartermaster.us/";
const terms = "https://quartermaster.us/";

const OTP_EMAIL_CONTENT = (opt, name, date) => {
    return `
    <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #C4C4C400;
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0;
            width: 100%;
            margin: 0 auto;
        }

        .date {
            color: black;
            font-size: 16px;
            font-weight: bold;
            text-align: right
        }



        .content {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            margin: 0 auto;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .content h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .content p {
            color: #666666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .otp-box {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            padding: 10px 20px;
            letter-spacing: 10px;
            text-align: center;
        }

        .footer {
            font-size: 12px;
            color: #999;
            text-align: center;
            padding: 20px;
        }

        .footer p {
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .content {
                width: 90%;
            }

            .content h1 {
                font-size: 20px;
            }

            .content p {
                font-size: 14px;
            }

            .otp-box {
                font-size: 22px;
            }

            .footer {
                font-size: 10px;
            }

            .date {
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <!-- Top Section -->
        <tr>
            <td>
                <img style="width:100% ; height:auto" src=${logo}
                    alt="Background" class="background">
            </td>
        </tr>
        <tr>
            <td>
                <table class="content"
                    style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; width: 80%; margin: 0 auto;">

                    <!-- Center Content -->
                    <tr>
                        <td class="date">${date}</td>
                    </tr>
                    <tr>
                        <td>
                            <table>
                                <tr>
                                    <td>
                                        <h1>Dear ${name},</h1>
                                        <p>Please use the Code <strong>${opt}</strong> to login. If you have not
                                            requested an OTP,
                                            please contact
                                            Quartermaster helpline at US: (202)-240-7792 || Philippines: +63 919 161 0232</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="otp-box">${opt}</td>
                                </tr>
                                <tr>
                                    <td style="text-align: center; font-size: 12px; color: #666666;">
                                        <p><strong>DISCLAIMER:</strong> This email and any files transmitted with it are
                                            intended
                                            only for the use of the recipient and may contain information which is
                                            privileged and/or
                                            confidential under applicable law. If you are not the intended recipient or
                                            such recipient's employee or
                                            agent, you are hereby notified that any unauthorized use, dissemination,
                                            distribution, copy, or
                                            disclosure of this communication is strictly prohibited.
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 10px; width: 80%;">

                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td>
                <table class="footer">
                    <tr>
                        <td>
                            <p>If you have any questions, feel free to message us at
                                <strong><EMAIL></strong>. All
                                rights reserved. Update email preferences or unsubscribe.
                            </p>
                            <p>
                                <a href=${terms} style="color: #666666;">
                                    Terms of use
                                </a>
                                |
                                <a href=${privacy} style="color: #666666;">
                                    Privacy Policy
                                </a>
                            </p>
                            &copy; 2025 Quartermaster. All Rights Reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
`;
};

const INVITE_EMAIL_CONTENT = (invite_link, date) => {
    return `
    

    <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #C4C4C400;
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0;
            width: 100%;
            margin: 0 auto;
        }

        .date {
            color: black;
            font-size: 16px;
            font-weight: bold;
            text-align: right
        }
        
        .content {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            margin: 0 auto;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .content h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .content p {
            color: #666666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .otp-box {
            font-size: 28px;
            font-weight: bold;
            text-align: center
        }

        .footer {
            font-size: 12px;
            color: #999;
            text-align: center;
            padding: 20px;
        }

        .footer p {
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .content {
                width: 90%;
            }

            .content h1 {
                font-size: 20px;
            }

            .content p {
                font-size: 14px;
            }

            .otp-box {
                font-size: 22px;
            }

            .footer {
                font-size: 10px;
            }

            .date {
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <!-- Top Section -->
        <tr>
            <td>
                <img style="width:100% ; height:auto" src=${logo}
                    alt="Background">
            </td>
        </tr>
        <tr>
            <td>
                <table class="content"
                    style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; width: 80%; margin: 0 auto;">

                    <!-- Center Content -->
                    <tr>
                        <td class="date">${date}</td>
                    </tr>
                    <tr>
                        <td>
                            <table>
                                <tr>
                                    <td>
                                        <h1 style="color:#1F1F1F;">You have been invited</h1>
                                        <p style="color:#9CA0A8;">Please click on the button below to join Quartermaster.</p>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="otp-box">
                                        <p> <a style="background-color:#1E293B; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none
                                    " href=${invite_link} > Join Quartermaster </a>
                                        </p>
                                        <img src=${ship}
                                            style="margin-top:10px ;padding:10px " width="80%"/>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <p style="color:#9CA0A8; width:80%;margin:auto">
                                            If you don’t want to join, simply ignore this email. You don’t have to do
                                            anything. So that’s easy.
                                        </p>
                                    </td>
                                </tr>

                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 20px; width: 80%;">

                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td>
                <table class="footer">
                    <tr>
                        <td>
                            <p>If you have any questions, feel free to message us at
                                <strong><EMAIL></strong>. All
                                rights reserved. Update email preferences or unsubscribe.
                            </p>
                            <p>
                                <a href=${terms} style="color: #666666;">
                                    Terms of use
                                </a>
                                |
                                <a href=${privacy} style="color: #666666;">
                                    Privacy Policy
                                </a>
                            </p>
                            &copy; 2025 Quartermaster. All Rights Reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>

`;
};

const WELCOME_EMAIL_CONTENT = (name, date) => {
    return `
    <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #C4C4C400;
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0;
            width: 100%;
            margin: 0 auto;
        }

        .date {
            color: black;
            font-size: 16px;
            font-weight: bold;
            text-align: right
        }



        .content {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            margin: 0 auto;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .content h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .content p {
            color: #666666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }



        .footer {
            font-size: 12px;
            color: #999;
            text-align: center;
            padding: 20px;
        }

        .footer p {
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .content {
                width: 90%;
            }

            .content h1 {
                font-size: 20px;
            }

            .content p {
                font-size: 14px;
            }



            .footer {
                font-size: 10px;
            }

            .date {
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <!-- Top Section -->
        <tr>
            <td>
                <img style="width:100% ; height:auto" src=${logo}
                    alt="Background">
            </td>
        </tr>
        <tr>
            <td>
                <table class="content"
                    style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; width: 80%; margin: 0 auto;">

                    <!-- Center Content -->
                    <tr>
                        <td class="date">${date}</td>
                    </tr>
                    <tr>
                        <td>
                            <table>
                                <tr>
                                    <td>
                                        <h1 style="color:#1F1F1F;">Welcome ${name}</h1>
                                        <p style="color:#9CA0A8; width:80%;margin:auto">
                                            Welcome to the team! We’re thrilled to have you on board and excited to serve you. 
                                            If you need any assistance,please feel free to let us know. We are here to support you every step of the way.
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="text-align:center">

                                        <img src=${ship}
                                            style="margin-top:10px ;padding:10px " width="80%" />

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <p style="color:#9CA0A8; width:80%;margin:auto">
                                            If you don’t want to join, simply ignore this email. You don’t have to do
                                            anything. So that’s easy.
                                        </p>
                                    </td>
                                </tr>

                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 20px; width: 80%;">

                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td>
                <table class="footer">
                    <tr>
                        <td>
                            <p>If you have any questions, feel free to message us at
                                <strong><EMAIL></strong>. All
                                rights reserved. Update email preferences or unsubscribe.
                            </p>
                            <p>
                                <a href=${terms} style="color: #666666;">
                                    Terms of use
                                </a>
                                |
                                <a href=${privacy} style="color: #666666;">
                                    Privacy Policy
                                </a>
                            </p>
                            &copy; 2025 Quartermaster. All Rights Reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
`;
};

const FORGET_PASSWORD_EMAIL_CONTENT = (reset_password_link, date) => {
    return `
    

    <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #C4C4C400;
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0;
            width: 100%;
            margin: 0 auto;
        }

        .date {
            color: black;
            font-size: 16px;
            font-weight: bold;
            text-align: right
        }
        
        .content {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            margin: 0 auto;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .content h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .content p {
            color: #666666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .otp-box {
            font-size: 28px;
            font-weight: bold;
            text-align: center
        }

        .footer {
            font-size: 12px;
            color: #999;
            text-align: center;
            padding: 20px;
        }

        .footer p {
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .content {
                width: 90%;
            }

            .content h1 {
                font-size: 20px;
            }

            .content p {
                font-size: 14px;
            }

            .otp-box {
                font-size: 22px;
            }

            .footer {
                font-size: 10px;
            }

            .date {
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <!-- Top Section -->
        <tr>
            <td>
                <img style="width:100% ; height:auto" src=${logo}
                    alt="Background">
            </td>
        </tr>
        <tr>
            <td>
                <table class="content"
                    style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; width: 80%; margin: 0 auto;">

                    <!-- Center Content -->
                    <tr>
                        <td class="date">${date}</td>
                    </tr>
                    <tr>
                        <td>
                            <table>
                                <tr>
                                    <td>
                                        <h1 style="color:#1F1F1F;">You have been invited</h1>
                                        <p style="color:#9CA0A8;">
                                        You requested a password reset. Please click the button below to reset your password
                                        </p>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="otp-box">
                                        <p> <a style="background-color:#1E293B; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none
                                    " href=${reset_password_link} > Reset Password </a>
                                        </p>
                                        <img src=${ship}
                                            style="margin-top:10px ;padding:10px " width="80%"/>

                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <p style="color:#9CA0A8; width:80%;margin:auto">
                                            If you don’t want to reset passoword, simply ignore this email. You don’t have to do
                                            anything. So that’s easy.
                                        </p>
                                    </td>
                                </tr>

                            </table>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 20px; width: 80%;">

                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td>
                <table class="footer">
                    <tr>
                        <td>
                            <p>If you have any questions, feel free to message us at
                                <strong><EMAIL></strong>. All
                                rights reserved. Update email preferences or unsubscribe.
                            </p>
                            <p>
                                <a href=${terms} style="color: #666666;">
                                    Terms of use
                                </a>
                                |
                                <a href=${privacy} style="color: #666666;">
                                    Privacy Policy
                                </a>
                            </p>
                            &copy; 2025 Quartermaster. All Rights Reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>

`;
};

const ARTIFACT_NOTIFICATION_EMAIL_CONTENT = (value, date, link = "") => {
    return `
    
    <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #C4C4C400;
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0;
            width: 100%;
            margin: 0 auto;
        }

        .date {
            color: black;
            font-size: 16px;
            font-weight: bold;
            text-align: right
        }

        .content {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            margin: 0 auto;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .content h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .content p {
            color: #666666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .heading_left {
            text-align: left;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            padding-top: 5px;
            padding-bottom: 3px;
            font-size: 20px;
        }

        .heading_right {
            text-align: right;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            font-size: 20px;
        }

        .data_left {
            text-align: left;
            color: #1F1F1F;
        }

        .data_right {
            text-align: right;
            color: #1F1F1F;
        }

        .footer {
            font-size: 12px;
            color: #999;
            text-align: center;
            padding: 20px;
        }

        .footer p {
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .content {
                width: 90%;
            }
            .content h1 {
                font-size: 20px;
            }
            .content p {
                font-size: 14px;
            }

            .heading_left {
                font-size: 16px;
            }

            .heading_right {
                font-size: 16px;
            }
            .footer {
                font-size: 10px;
            }
            .date {
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <!-- Top Section -->
        <tr>
            <td>
                <img style="width:100% ; height:auto" src=${logo} alt="Background">
            </td>
        </tr>
        <tr>
            <td>
                <table class="content" style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; width: 80%; margin: 0 auto;">

                    <!-- Center Content -->
                    <tr>
                        <td class="date">${date}</td>
                    </tr>


                    <tr>
                        <td>
                            <h1 style="color:#1F1F1F;">Detection Notification</h1>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <table style="width: 80%">
                                <tr>

                                    <td class="heading_left">
                                        Date & Time
                                    </td>
                                    <td class="heading_right">
                                        Vessel Last Observed </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${value}
                                    </td>
                                    <td class="data_right">
                                        ${value}
                                    </td>
                                </tr>
                                <tr>

                                    <td class="heading_left">
                                        Coordinates
                                    </td>
                                    <td class="heading_right">
                                        Observing Vessel
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${value}
                                    </td>
                                    <td class="data_right">
                                        ${value}
                                    </td>
                                </tr>
                                <tr>

                                    <td class="heading_left">
                                        Hull Number
                                    </td>
                                    <td class="heading_right">
                                        Vessel Flag
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${value}
                                    </td>
                                    <td class="data_right">
                                        ${value}
                                    </td>
                                </tr>
                                <tr>

                                    <td class="heading_left">
                                        Vessel Speed
                                    </td>
                                    <td class="heading_right">
                                        Target Vessel Heading
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${value}
                                    </td>
                                    <td class="data_right">
                                        ${value}
                                    </td>
                                </tr>
                                <tr>

                                    <td class="heading_left">
                                        VMS Supported
                                    </td>
                                    <td class="heading_right">
                                        AIS Supported
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${value}
                                    </td>
                                    <td class="data_right">
                                        ${value}
                                    </td>
                                </tr>
                                <tr>

                                    <td class="heading_left">
                                        Military
                                    </td>
                                    <td class="heading_right">
                                        Category
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${value}
                                    </td>
                                    <td class="data_right">
                                        ${value}
                                    </td>
                                </tr>
                                <tr>

                                    <td class="heading_left">
                                        Color
                                    </td>
                                    <td class="heading_right">
                                        Size
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${value}
                                    </td>
                                    <td class="data_right">
                                        ${value}
                                    </td>
                                </tr>
                                <tr>

                                    <td class="heading_left">
                                        Description
                                    </td>

                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${value}
                                    </td>

                                </tr>

                            </table>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <p> <a style="background-color:#1E293B; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none
                                    " href=${link}> View in Portal </a>
                        </td>
                    </tr>

                    <tr>
                        <td style="text-align:center">
                            <img src="https://i.ibb.co/HgRTWTm/ship.png" style="margin-top:10px ;padding:10px " width="80%" />

                        </td>
                    </tr>
                    <tr>
                        <td>
                            <p style="color:#9CA0A8; width:80%;margin:auto">
                                If you don’t want to join, simply ignore this email. You don’t have to do anything. So that’s easy.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 20px; width: 80%;">
                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td>
                <table class="footer">
                    <tr>
                        <td>
                            <p>If you have any questions, feel free to message us at
                                <strong><EMAIL></strong>. All rights reserved. Update email preferences or unsubscribe.
                            </p>
                            <p>
                                <a href=${link} style="color: #666666;">
                                    Terms of use
                                </a> |
                                <a href=${link} style="color: #666666;">
                                    Privacy Policy
                                </a>
                            </p>
                            &copy; 2025 Quartermaster. All Rights Reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>

    `;
};

// const sampleSummaryData = {
//     type: 'daily',
//     superCtg: '77 Vessels (Cargo : 66 , Passenger : 07 , Special Craft : 04)',
//     flags: '12 Vessels (China : 06 , Philippines : 02 , Unknown : 04) ',
//     highDetection: '02 Detections',
//     incursions: false,
//     totalDetection: 10,
//     link: 'https://quartermaster.us',
//     vessels: [{
//         name: 'BRP 123 Vessel',
//         value: 123
//     }, {
//         name: "BGTR 1232 Sea",
//         value: 100
//     }]

// }

const NOTIFICATION_SUMMARY_EMAIL_CONTENT = (data, date) => {
    const vesselRows = data.vessels
        .map(
            (vessel) => `
            <tr>
                <td class="data_left">
                    ${vessel.name}
                </td>
                <td class="data_right">
                    ${vessel.value}
                </td>
            </tr>`,
        )
        .join("");

    return `
     <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #C4C4C400;
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0;
            width: 100%;
            margin: 0 auto;
        }

        .date {
            color: black;
            font-size: 16px;
            font-weight: bold;
            text-align: right
        }

        .content {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            margin: 0 auto;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .content h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .content p {
            color: #666666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .heading_left {
            text-align: left;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            padding-top: 5px;
            padding-bottom: 3px;
            font-size: 20px;
        }

        .heading_right {
            text-align: right;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            font-size: 20px;
        }

        .data_left {
            text-align: left;
            color: #1F1F1F;
            font-size:18px
        }

        .data_right {
            text-align: right;
            color: #1F1F1F;
            font-size:18px
        }
        
        .heading_full_left {
            text-align: left;
            font-weight: bold;
            color: #0285FE;
            width: 100%;
            padding-top: 5px;
            padding-bottom: 3px;
            font-size: 25px;
            margin-top: 10px;
        }

        .heading_full_right {
            text-align: right;
            font-weight: bold;
            color: #0285FE;
            width: 100%;
            font-size: 25px;
        }

        .data_full_left {
            text-align: left;
            color: #1F1F1F;
            font-size:18px;
        }

        .data_full_right {
            text-align: right;
            color: #1F1F1F;
        }

        .footer {
            font-size: 12px;
            color: #999;
            text-align: center;
            padding: 20px;
        }

        .footer p {
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .content {
                width: 100%;
            }
            .content h1 {
                font-size: 20px;
            }
            .content p {
                font-size: 14px;
            }

            .heading_left {
                font-size: 16px;
            }

            .heading_right {
                font-size: 16px;
            }
            
            .data_left {
            font-size:14px
            }

            .data_right {
            font-size:14px
            }
            .heading_full_left {
                font-size: 16px;
            }

            .heading_full_right {
                font-size: 16px;
            }
            .data_full_left {
            font-size:14px;
        }
            .footer {
                font-size: 10px;
            }
            .date {
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <!-- Top Section -->
        <tr>
            <td>
                <img style="width:100% ; height:auto" src='https://portal.quartermaster.us/email/header.png' alt="Background">
            </td>
        </tr>
        <tr>
            <td>
                <table class="content" style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; margin: 0 auto;">

                    <!-- Center Content -->
                    <tr>
                        <td class="date">${date}</td>
                    </tr>


                    <tr>
                        <td>
                            <h1 style="color:#1F1F1F;">${data.type} Vessel Detection Report</h1>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <table style="width: 90%">
                                <tr>

                                    <td class="heading_full_left">
                                        Number of Vessels Detected by Super Category
                                    </td>
                                 
                                </tr>
                                <tr>
                                    <td class="data_full_left">
                                        ${data.superCtg}
                                    </td>
                                </tr>
                                 <tr>

                                    <td class="heading_full_left">
                                        Number of Vessels Detected by National Flag
                                    </td>
                                 
                                </tr>
                                <tr>
                                    <td class="data_full_left">
                                        ${data.flags}
                                    </td>
                                </tr>
                                 <tr>

                                    <td class="heading_full_left">
                                        Number of High-Interest Detections
                                    </td>
                                 
                                </tr>
                                <tr>
                                    <td class="data_full_left">
                                        ${data.highDetection}
                                    </td>
                                </tr>
                                 <tr>

                                    <td class="heading_full_left">
                                        Possible EEZ Incursions
                                    </td>
                                 
                                </tr>
                                <tr>
                                    <td class="data_full_left">
                                        ${data.incursions}
                                    </td>
                                </tr>
                                
                                <tr>

                                    <td class="heading_full_left">
                                        Number of Detections by Sensor
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                      Total Detections
                                    </td>
                                    <td class="data_right">
                                        ${data.totalDetection}
                                    </td>
                                </tr>
                                
                                 ${vesselRows}
                              
                               

                            </table>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <p> <a style="background-color:#1E293B; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none
                                    " href=${data.link}> View in Portal </a>
                        </td>
                    </tr>

                    <tr>
                        <td style="text-align:center">
                            <img src="https://portal.quartermaster.us/email/ship.png" style="margin-top:10px ;padding:10px " width="80%" />

                        </td>
                    </tr>
                    <tr>
                        <td>
                            <p style="color:#9CA0A8; width:80%;margin:auto">
                                If you no longer wish to receive these alerts, simply remove them from your notification summary settings.
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 20px; width: 80%;">
                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td>
                <table class="footer">
                    <tr>
                        <td>
                            <p>If you have any questions, feel free to message us at
                                <strong><EMAIL></strong>. All rights reserved. Update email preferences or unsubscribe.
                            </p>
                            <p>
                                <a href='https://quartermaster.us/' style="color: #666666;">
                                    Terms of use
                                </a> |
                                <a href='https://quartermaster.us/' style="color: #666666;">
                                    Privacy Policy
                                </a>
                            </p>
                            &copy; 2025 Quartermaster. All Rights Reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
    `;
};

const SUMMARY_SUBSCRIPTION_EMAIL_CONTENT = (data, date) => {
    return `
    
    <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #C4C4C400;
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0;
            width: 100%;
            margin: 0 auto;
        }

        .date {
            color: black;
            font-size: 16px;
            font-weight: bold;
            text-align: right
        }

        .content {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            margin: 0 auto;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .content h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .content p {
            color: #666666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .heading_left {
            text-align: left;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            padding-top: 5px;
            padding-bottom: 3px;
            font-size: 20px;
            margin-top:3px;
        }

        .heading_right {
            text-align: right;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            font-size: 20px;
            margin-top:3px;
        }

        .data_left {
            text-align: left;
            margin-top:3px;
            color: #1F1F1F;
        }

        .data_right {
            text-align: right;
         margin-top:3px;
            color: #1F1F1F;
        }

        .footer {
            font-size: 12px;
            color: #999;
            text-align: center;
            width:90%;
            padding: 20px;
        }

        .footer p {
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .content {
                width: 90%;
            }
            .content h1 {
                font-size: 20px;
            }
            .content p {
                font-size: 14px;
            }

            .heading_left {
                font-size: 16px;
            }

            .heading_right {
                font-size: 16px;
            }
            .footer {
                font-size: 10px;
            }
            .date {
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <!-- Top Section -->
        <tr>
            <td>
                <img style="width:100% ; height:auto" src=${logo} alt="Background">
            </td>
        </tr>
        <tr>
            <td>
                <table class="content" style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; width: 80%; margin: 0 auto;">

                    <!-- Center Content -->
                    <tr>
                        <td class="date">${date}</td>
                    </tr>


                    <tr>
                        <td>
                            <h1 style="color:#1F1F1F;">SUMMARY Alerts Subscription</h1>
                        </td>
                    </tr>
                    
                     <tr>
                        <td>
                            <p style=" width:90%;margin:auto; padding-bottom:15px">
                               You've been added by <strong>${data.addBy} </strong>to receive emails once a Smarmast <strong>${data.vessel}  </strong> detects new artifact please find the following details about what you will be notified.
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <table style="width: 80%">
                                <tr>

                                    <td class="heading_left">
                                        Created By
                                    </td>
                                    <td class="heading_right">
                                        Vessel Name
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${data.addBy}
                                    </td>
                                    <td class="data_right">
                                        ${data.vessel}
                                    </td>
                                </tr>
                                <tr>
                                    <td class="heading_left">
                                        Preference
                                    </td>
                                </tr>
                                <tr>
                                    <td class="data_left">
                                        ${data.preference}
                                    </td>
                                </tr>

                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <p style=" width:90%; margin:auto; padding-top:20px;padding-bottom:10px">
If you don't want to receive further emails please click the button below.
                            </p>
                        </td>
                    </tr>


                    <tr>
                        <td>
                            <p> <a style="background-color:#9CA0A8; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none" href=${data.link}> Unsubscribe </a>
                        </td>
                    </tr>

                
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 20px; width: 80%;">
                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td>
                <table class="footer">
                    <tr>
                        <td>
                            <p>If you have any questions, feel free to message us at
                                <strong><EMAIL></strong>. All rights reserved. Update email preferences or unsubscribe.
                            </p>
                            <p>
                                <a href=${terms} style="color: #666666;">
                                    Terms of use
                                </a> |
                                <a href=${privacy} style="color: #666666;">
                                    Privacy Policy
                                </a>
                            </p>
                            &copy; 2025 Quartermaster. All Rights Reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>
    `;
};

const NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT = (data, date) => {
    return `
    
    <!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #C4C4C400;
            margin: 0;
            padding: 0;
        }

        table {
            border-spacing: 0;
            width: 100%;
            margin: 0 auto;
        }

        .date {
            color: black;
            font-size: 16px;
            font-weight: bold;
            text-align: right
        }

        .content {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 20px;
            width: 80%;
            margin: 0 auto;
            z-index: 2;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
        }

        .content h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 10px;
            text-align: center;
        }

        .content p {
            color: #666666;
            font-size: 16px;
            margin-bottom: 20px;
            text-align: center;
        }

        .heading_left {
            text-align: left;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            padding-top: 5px;
            padding-bottom: 3px;
            font-size: 20px;
            margin-top:3px;
        }

        .heading_right {
            text-align: right;
            font-weight: bold;
            color: #0285FE;
            width: 50%;
            font-size: 20px;
            margin-top:3px;
        }

        .data_left {
            text-align: left;
            margin-top:3px;
            color: #1F1F1F;
        }

        .data_right {
            text-align: right;
         margin-top:3px;
            color: #1F1F1F;
        }

        .footer {
            font-size: 12px;
            color: #999;
            text-align: center;
            width:90%;
            padding: 20px;
        }

        .footer p {
            margin: 5px 0;
        }

        @media (max-width: 768px) {
            .content {
                width: 90%;
            }
            .content h1 {
                font-size: 20px;
            }
            .content p {
                font-size: 14px;
            }

            .heading_left {
                font-size: 16px;
            }

            .heading_right {
                font-size: 16px;
            }
            .footer {
                font-size: 10px;
            }
            .date {
                font-size: 14px;
            }
        }
    </style>
</head>

<body>
    <table style="width: 100%;" cellpadding="0" cellspacing="0">
        <!-- Top Section -->
        <tr>
            <td>
                <img style="width:100% ; height:auto" src=${logo} alt="Background">
            </td>
        </tr>
        <tr>
            <td>
                <table class="content" style="background: #FFFFFF; border-radius: 8px; border: 1px solid #ddd; padding: 20px; width: 80%; margin: 0 auto;">

                    <!-- Center Content -->
                    <tr>
                        <td class="date">${date}</td>
                    </tr>


                    <tr>
                        <td>
                            <h1 style="color:#1F1F1F;">Notification Alerts Subscription</h1>
                        </td>
                    </tr>
                    
                     <tr>
                        <td>
                            <p style=" width:90%;margin:auto; padding-bottom:15px">
                               You've been added by <strong>${data.addBy} </strong>to receive emails once a Smarmast <strong>${data.vessel}  </strong> detects new artifact please find the following details about what you will be notified.
                            </p>
                        </td>
                    </tr>

                    <tr>
                        <td>
                            <table style="width: 80%">
                                <tr>

                                    <td class="heading_left">
                                        Super Category
                                    </td>
                                    <td class="heading_right">
                                        Sub Category
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${data.super}
                                    </td>
                                    <td class="data_right">
                                        ${data.sub}
                                    </td>
                                </tr>
                                <tr>

                                    <td class="heading_left">
                                        Country Flag
                                    </td>
                                    <td class="heading_right">
                                        Preference
                                    </td>
                                </tr>
                                <tr>

                                    <td class="data_left">
                                        ${data.flag}
                                    </td>
                                    <td class="data_right">
                                        ${data.preference}
                                    </td>
                                </tr>

                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <p style=" width:90%; margin:auto; padding-top:20px;padding-bottom:10px">
If you don't want to receive further emails please click the button below.
                            </p>
                        </td>
                    </tr>


                    <tr>
                        <td>
                            <p> <a style="background-color:#9CA0A8; color:#FFFFFF;padding:15px;border-radius:10px;cursor:pointer;text-decoration:none
                                    " href=${data.link}> Unsubscribe </a>
                        </td>
                    </tr>

                
                </table>
            </td>
        </tr>

        <tr>
            <td>
                <table style="border-bottom: 1px solid grey; margin: 10px auto; padding: 20px; width: 80%;">
                </table>
            </td>
        </tr>

        <!-- Footer Section -->
        <tr>
            <td>
                <table class="footer">
                    <tr>
                        <td>
                            <p>If you have any questions, feel free to message us at
                                <strong><EMAIL></strong>. All rights reserved. Update email preferences or unsubscribe.
                            </p>
                            <p>
                                <a href=${terms} style="color: #666666;">
                                    Terms of use
                                </a> |
                                <a href=${privacy} style="color: #666666;">
                                    Privacy Policy
                                </a>
                            </p>
                            &copy; 2025 Quartermaster. All Rights Reserved.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
</body>

</html>

    `;
};
module.exports = {
    OTP_EMAIL_CONTENT,
    INVITE_EMAIL_CONTENT,
    WELCOME_EMAIL_CONTENT,
    FORGET_PASSWORD_EMAIL_CONTENT,
    ARTIFACT_NOTIFICATION_EMAIL_CONTENT,
    NOTIFICATION_SUMMARY_EMAIL_CONTENT,
    NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT,
    SUMMARY_SUBSCRIPTION_EMAIL_CONTENT,
};
