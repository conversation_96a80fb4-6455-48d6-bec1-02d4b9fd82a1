

 const artifacts_mock_data = [
    {
      _id: "67f4668d91ab9b3a7862d01f",
      timestamp: "2025-04-07T23:49:54.974Z",
      bucket_name: "smartmast-prototype-37-ap",
      aws_region: "ap-southeast-1",
      unit_id: "prototype-37",
      onboard_vessel_name: "BRP Cape Engaño (MRRV-4411)",
      metadata_path:
        "artifacts/2025-04-06T01:36:03.274Z/image/prototype-37_cam-1_2025-04-07T23:49:54.974Z.json",
      image_path:
        "artifacts/2025-04-06T01:36:03.274Z/image/prototype-37_cam-1_2025-04-07T23:49:54.974Z.jpg",
      det_conf: 0.94092,
      det_nbbox: { x1: 0.17312, y1: 0.6163, x2: 0.33693, y2: 0.70202 },
      det_nbbox_area: 0.0140417932,
      location: { type: "Point", coordinates: [116.702062599999, 8.6361362] }, // Add coordinates manually if available
      vessel_presence: true,
      category: "Banca",
      super_category: "Fishing",
      text_extraction: [],
      imo_number: null,
      color: "White and red",
      size: "Small",
      weapons: null,
      country_flag: null,
      others: "Traditional outrigger canoe commonly used in the Philippines.",
    },
    {
      _id: "67f46a7791ab9b3a7862d020",
      timestamp: "2025-04-08T00:08:08.006Z",
      bucket_name: "smartmast-prototype-37-ap",
      aws_region: "ap-southeast-1",
      unit_id: "prototype-37",
      onboard_vessel_name: "BRP Cape Engaño (MRRV-4411)",
      metadata_path:
        "artifacts/2025-04-06T01:36:03.274Z/image/prototype-37_cam-1_2025-04-08T00:08:08.006Z.json",
      image_path:
        "artifacts/2025-04-06T01:36:03.274Z/image/prototype-37_cam-1_2025-04-08T00:08:08.006Z.jpg",
      det_conf: 0.91895,
      det_nbbox: { x1: 0.08796, y1: 0.64464, x2: 0.26866, y2: 0.96252 },
      det_nbbox_area: 0.057440916,
      location: { type: "Point", coordinates: [116.702062599999, 8.6361362] },
      vessel_presence: true,
      category: "Patrol Boat",
      super_category: "Military",
      text_extraction: [],
      imo_number: null,
      color: "White and dark blue",
      size: "Small",
      weapons: null,
      country_flag: null,
      others: null,
    },
  ];

  const testMode_mock_data = {
    mapUrl: "https://portal.quartermaster.us/api/notificationsAlerts/map?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJsb2NhdGlvbnMiOltdLCJ0aW1lc3RhbXAiOjE3NDQyOTgzNzUyMjMsImlhdCI6MTc0NDI5ODM3NSwiZXhwIjoxNzU5ODUwMzc1fQ.cBdo37KY3NlyvAr7l1QHJ4g-D503JwTNROqHIG4Mcq0"
  }



  module.exports ={
    artifacts_mock_data,
    testMode_mock_data
  }