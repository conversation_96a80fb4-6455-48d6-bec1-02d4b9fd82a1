const AWS = require('aws-sdk');
const Region = require('../models/Region');

AWS.config.update({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY
});

const handleClientLimitExceededException = async (err, retryFunction, ...args) => {
    const retryAfter = parseInt(err?.retryDelay, 10) || 2;
    console.warn(
        "[gps.handleClientLimitExceededException]  limit exceeded  for this API, waiting",
        retryAfter,
        "seconds and retrying",
    );
    await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000));
    console.log("[gps.handleClientLimitExceededException] Retrying...");
    return await retryFunction.apply(this, args);
};

const safeListThings = async ({ iot, nextToken }) => {
    try {
        return await iot.listThings({ nextToken }).promise();
    } catch (error) {
        if(error.code != "UnknownEndpoint" && error.code != "UnrecognizedClientException" && error.code !="AccessDeniedException"){
            console.error("listThings API error:", error.code, error.name, error);
        }
        if (error.code === "ThrottlingException" || error.name === "ThrottlingException") {
            return await handleClientLimitExceededException({retryDelay: 5}, safeListThings, { iot, nextToken });
        }
        throw error;
    }
};

async function listThings() {
    return new Promise(async (resolve, reject) => {
        try {
            const regions = await Region.find()

            Promise.allSettled(regions.map(region => (
                new Promise(async (resolve, reject) => {
                    try {
                        const iot = new AWS.Iot({ region: region.value });

                        let things = []
                        let nextToken = null

                        do {
                            // const res = await iot.listThings({ nextToken }).promise();
                            const res = await safeListThings({ iot, nextToken });
                            things = things.concat(res.things)
                            nextToken = res.nextToken
                        } while (nextToken)

                        resolve({
                            region: region.value,
                            things
                        })
                    } catch (err) {
                        reject(err)
                    }
                })
            ))).then(res => {
                resolve(res.filter(res => res.status === 'fulfilled').reduce((arr, data) => arr.concat(data.value), []))
            }).catch(err => reject(err))
        } catch (err) {
            reject(err)
        }
    })
}

module.exports = {
    listThings
}