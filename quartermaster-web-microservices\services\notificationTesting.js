const { artifacts_mock_data } = require("../utils/notificartionAlertMockData");
const { processNotificationAlerts } = require("./notificationAlertsProcessor");
const {
  generateDailySummaryReports,
  generateMonthlySummaryReports,
  generateWeeklySummaryReports,
} = require("./summaryReportsProcessor");

const sendTestNotificationsListener = (data) => {
  const parsedData = JSON.parse(data)
  console.log(parsedData, parsedData.test)
  const artifacts = artifacts_mock_data
  processNotificationAlerts({
    artifacts,
    userId: parsedData.user_id,
    testMode: true,
  });

  generateDailySummaryReports({
    userId: parsedData.user_id,
    testMode: true,
  });
  generateWeeklySummaryReports({
    userId: parsedData.user_id,
    testMode: true,
  });
  generateMonthlySummaryReports({
    userId: parsedData.user_id,
    testMode: true,
  });
};

module.exports = {
  sendTestNotificationsListener
}
