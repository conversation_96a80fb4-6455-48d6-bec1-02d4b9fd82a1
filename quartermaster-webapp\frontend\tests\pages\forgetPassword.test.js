import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material';
import ForgotPassword from '../../src/pages/ForgotPassword/ForgotPassword';
import axiosInstance from '../../src/axios';

jest.mock('../../src/axios', () => ({
    post: jest.fn(),
}));

const theme = createTheme();

describe('ForgotPassword Component', () => {
    beforeEach(() => {
        jest.useFakeTimers();
    });

    afterEach(() => {
        jest.clearAllMocks();
        jest.useRealTimers();
    });

    it('should render the Forgot Password form correctly', () => {
        render(
            <ThemeProvider theme={theme}>
                <ForgotPassword />
            </ThemeProvider>
        );

        expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument();
        expect(screen.getByText('Forgot Password')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /Send Reset Link/i })).toBeInTheDocument();
    });

    it('should show an error message if the email submission fails', async () => {
        axiosInstance.post.mockRejectedValue({
            response: {
                data: {
                    message: 'Invalid email address.',
                },
            },
        });

        render(
            <ThemeProvider theme={theme}>
                <ForgotPassword />
            </ThemeProvider>
        );

        fireEvent.change(screen.getByPlaceholderText('Enter your email'), { target: { value: '<EMAIL>' } });

        const submitButton = screen.getByRole('button', { name: /Send Reset Link/i });
        fireEvent.click(submitButton);

        await waitFor(() => expect(screen.getByText('Invalid email address.')).toBeInTheDocument());
    });

    it('should display success message when the email is submitted successfully', async () => {
        axiosInstance.post.mockResolvedValue({
            data: {
                message: 'Reset link sent to your email.',
            },
            status: 200,
        });

        render(
                <ThemeProvider theme={theme}>
                    <ForgotPassword />
                </ThemeProvider>
        );

        fireEvent.change(screen.getByPlaceholderText('Enter your email'), { target: { value: '<EMAIL>' } });
        fireEvent.click(screen.getByRole('button', { name: /Send Reset Link/i }));

        await waitFor(() => expect(screen.getByText('Reset link sent to your email.')).toBeInTheDocument());
    });

    it('should disable the "Send Reset Link" button while submitting', async () => {
        axiosInstance.post.mockResolvedValue({
            data: {
                message: 'Reset link sent to your email.',
            },
        });

        render(
            <ThemeProvider theme={theme}>
                <ForgotPassword />
            </ThemeProvider>
        );

        fireEvent.change(screen.getByPlaceholderText('Enter your email'), { target: { value: '<EMAIL>' } });

        const submitButton = screen.getByRole('button', { name: /Send Reset Link/i });
        fireEvent.click(submitButton);

        expect(submitButton).toBeDisabled();

        await waitFor(() => expect(submitButton).not.toBeDisabled());
    });
});
