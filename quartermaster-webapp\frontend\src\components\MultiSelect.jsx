import { <PERSON><PERSON>ox, CheckBoxOutlineBlank, IndeterminateCheckBox } from "@mui/icons-material";
import { Autocomplete, Checkbox, TextField, useTheme } from "@mui/material";

/**
 * MultiSelect component using MUI Autocomplete.
 *
 * @component
 * @param {import("@mui/material").AutocompleteProps} props - The props passed to the component.
 * @param {string} [props.label=""] - The label for the textfield.
 * @param {import("@mui/material").InputProps} [props.TextFieldProps] - The textfield props.
 * @param {import("@mui/material").SxProps<import("@mui/system").Theme> | undefined} [props.InputLabelStyle] - The textfield label style.
 * @param {number} [props.borderRadius] - The border radius of the textfield.
 * @returns {JSX.Element} The MultiSelect component.
 */

export default function MultiSelect({ InputLabelStyle, TextFieldProps, backgroundColor, borderRadius, ...props }) {
    const theme = useTheme();

    const handleGroupChecked = (group, checked) => {
        const groupItems = props.options.filter((option) => props.groupBy(option) === group);
        const newValues = checked
            ? [...props.value, ...groupItems]
            : props.value.filter((item) => !groupItems.find((groupItem) => props.isOptionEqualToValue(groupItem, item)));
        props.onChange(null, newValues);
    };

    return (
        <Autocomplete
            size="small"
            sx={{
                ".MuiInputBase-input": {
                    height: 16,
                },
                ...props.sx,
            }}
            slotProps={{
                popper: {
                    sx: {
                        "& .MuiAutocomplete-groupLabel": {
                            color: theme.palette.primary.contrastText,
                            backgroundColor: theme.palette.primary.dark,
                            display: "flex",
                            alignItems: "center",
                            gap: 1,
                        },
                        "& .MuiAutocomplete-option": {
                            padding: 0,
                            paddingLeft: 2,
                            fontSize: 13,
                        },
                    },
                },
            }}
            renderGroup={(params) => {
                console.log(params.group, "params", params);
                const checkedLength = params.children.filter((child) => child?.props?.children[0]?.props?.checked || false).length;
                const totalLength = params.children.length;

                console.log(params.group, "checkedLength", checkedLength);
                console.log(params.group, "totalLength", totalLength);

                const groupState = checkedLength === totalLength ? "checked" : checkedLength > 0 ? "indeterminate" : "unchecked";

                return (
                    <li key={params.key}>
                        <div className="MuiAutocomplete-groupLabel">
                            <Checkbox
                                onChange={(e, checked) => handleGroupChecked(params.group, checked)}
                                icon={<CheckBoxOutlineBlank fontSize="small" />}
                                checkedIcon={<CheckBox fontSize="small" />}
                                checked={groupState === "checked"}
                                indeterminate={groupState === "indeterminate"}
                                indeterminateIcon={<IndeterminateCheckBox fontSize="small" />}
                                size="small"
                                sx={{
                                    color: theme.palette.primary.contrastText,
                                    "&.Mui-checked": {
                                        color: theme.palette.primary.contrastText,
                                    },
                                }}
                            />
                            {params.group}
                        </div>
                        <ul>{params.children}</ul>
                    </li>
                );
            }}
            renderInput={(params) => (
                <TextField
                    size="small"
                    variant="outlined"
                    {...params}
                    InputLabelProps={{
                        sx: {
                            ...params.InputProps.style,
                            color: "primary.contrastText",
                            fontSize: 13,
                            "&.Mui-disabled": {
                                color: "primary.contrastText",
                                opacity: 0.38,
                            },
                            // ...props.InputLabelStyle,
                            ...InputLabelStyle,
                        },
                    }}
                    InputProps={{
                        ...params.InputProps,
                        style: {
                            ...params.InputProps.style,
                            borderRadius: borderRadius || 4,
                            backgroundColor: backgroundColor || theme.palette.primary.main,
                            opacity: props.disabled ? 0.38 : 1,
                        },
                    }}
                    label={props.label}
                    // {...props.TextFieldProps}
                    {...TextFieldProps}
                />
            )}
            renderOption={(innerProps, option, { selected }) => {
                const { key, ...optionProps } = innerProps;
                const label = props.getOptionLabel ? props.getOptionLabel(option) : "label not found";
                return (
                    <li key={key} {...optionProps}>
                        <Checkbox
                            icon={<CheckBoxOutlineBlank fontSize="small" />}
                            checkedIcon={<CheckBox fontSize="small" />}
                            // style={{ marginRight: 8 }}
                            checked={selected}
                        />
                        {label}
                    </li>
                );
            }}
            {...props}
        />
    );
}
