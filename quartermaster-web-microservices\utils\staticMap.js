// const {default: Supercluster} = require("supercluster");
const geoViewport = require("@mapbox/geo-viewport");


const mapDefaultOptions = {
    clusterRadius: 80,
    mapType: "terrain",
    zoom: 3,
    scale: 2,
    width: 800,
    height: 400,
};

/**
 * Calculates the geographic center (centroid) of an array of points
 * using the Cartesian vector average method. (Corrected Version)
 *
 * @param {Array<{lat: number, lng: number}> | Array<{latitude: number, longitude: number}>} points - An array of point objects.
 *                 Accepts objects with lat/lng or latitude/longitude properties.
 * @returns {LatLonEllipsoidal | null} The calculated center point as a LatLonEllipsoidal object,
 *                                     or null if the input array is empty or contains no valid points.
 */
async function getCenterFromPointsVector(points) {
    // const { default: LatLonEllipsoidal, Vector3d } = await import('geodesy/latlon-ellipsoidal.js');
    const { default: LatLon_NvectorEllipsoidal, Nvector } = await import('geodesy/latlon-nvector-ellipsoidal.js');

    if (!points || points.length === 0) {
        return null; // No points, no center
    }

    if (points.length === 1) {
        const p = points[0];
        const lat = p.lat ?? p.latitude;
        const lon = p.lng ?? p.longitude;
        // Basic validation for single point
        if (typeof lat !== "number" || typeof lon !== "number" || isNaN(lat) || isNaN(lon)) {
            console.warn("Skipping single invalid point:", p);
            return null;
        }
        try {
            return new LatLon_NvectorEllipsoidal(lat, lon);
        } catch (e) {
            console.warn(`Skipping single point due to geodesy error: ${e.message}`, p);
            return null;
        }
    }

    let sumVector = new Nvector(0, 0, 0);
    let validPointsCount = 0;

    for (const p of points) {
        const lat = p.lat ?? p.latitude;
        const lon = p.lng ?? p.longitude;

        if (typeof lat !== "number" || typeof lon !== "number" || isNaN(lat) || isNaN(lon)) {
            console.warn("Skipping invalid point (NaN or wrong type):", p);
            continue;
        }

        try {
            const pointLatLon = new LatLon_NvectorEllipsoidal(lat, lon);
            const vector = pointLatLon.toNvector();
            sumVector = sumVector.plus(vector);
            validPointsCount++;
        } catch (e) {
            console.warn(`Skipping point due to geodesy error (e.g., invalid lat/lon range [-90..90, -180..180]): ${e.message}`, p);
        }
    }

    if (validPointsCount === 0) {
        console.warn("No valid points found in the input array.");
        return null; //INFO: No valid points processed
    }

    const averageVector = sumVector.times(1 / validPointsCount);

    try {
        return new Nvector(
            averageVector.x,
            averageVector.y,
            averageVector.z,
        ).toLatLon();
    } catch (e) {
        // Possible if the average vector somehow ends up being zero (e.g., exactly two antipodal points)
        console.error("Error converting average vector back to LatLon:", e.message, averageVector);
        return null;
    }
}

/**
 * Converts an array of {lat, lng} points to GeoJSON Point features
 * Supercluster expects [lng, lat] coordinate order.
 * @param {Array<{lat: number, lng: number}>} points
 * @returns {Array<Object>} GeoJSON Point features
 */
function pointsToGeoJSON(points) {
    return points.map((point, index) => ({
        type: "Feature",
        properties: {
            pointId: index,
            isCluster: false, // supercluster will add its own
        },
        geometry: {
            type: "Point",
            coordinates: [point.lng, point.lat], // GeoJSON uses [longitude, latitude]
        },
    }));
}

async function getClustersAndPoints(pointsData, options = {}) {
    if (!options.centerLat || !options.centerLng) {
        const center = await getCenterFromPointsVector(pointsData);
        options.centerLat = center.lat;
        options.centerLng = center.lng;
    }

    let { centerLat, centerLng, clusterRadius = 60, maxZoom = 16, zoom = 3, width = 800, height = 400 } = options;

    if (pointsData.length) {
        const { default: Supercluster } = await import("supercluster");

        const index = new Supercluster({
            radius: clusterRadius, // cluster radius in pixels
            maxZoom: maxZoom,
            minZoom: 0,
            log: false,
        });

        index.load(pointsToGeoJSON(pointsData));

        const bounds = geoViewport.bounds([centerLng, centerLat], zoom, [width, height]);
        const clustersAndPoints = index.getClusters(bounds, zoom);
        return clustersAndPoints;
    }

    return undefined;
}

module.exports = {
    getClustersAndPoints,
    mapDefaultOptions,
};
