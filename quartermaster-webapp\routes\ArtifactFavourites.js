const express = require("express");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const isAuthenticated = require("../middlewares/auth");
const { validateData } = require("../middlewares/validator");
const { body, param } = require("express-validator");
const mongoose = require("mongoose");
const {
    addFavouriteArtifact,
    getAllFavouriteArtifacts,
    deleteFavouriteArtifact,
    getUserFavouriteArtifacts,
} = require("../services/ArtifactFavourites.service");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 20,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_FAVOURITE_ARTIFACT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                body("user_id")
                    .isString()
                    .notEmpty()
                    .custom((value) => mongoose.Types.ObjectId.isValid(value))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("artifact_id")
                    .isString()
                    .notEmpty()
                    .custom((value) => mongoose.Types.ObjectId.isValid(value))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    addFavouriteArtifact,
);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ALL_FAVOURITE_ARTIFACTS), isAuthenticated, getAllFavouriteArtifacts);

router.get(
    "/:user_id",
    assignEndpointId.bind(this, endpointIds.FETCH_USER_FAVOURITE_ARTIFACTS),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                param("user_id")
                    .isString()
                    .notEmpty()
                    .custom((value) => mongoose.Types.ObjectId.isValid(value))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    getUserFavouriteArtifacts,
);

router.delete(
    "/",
    assignEndpointId.bind(this, endpointIds.DELETE_FAVOURITE_ARTIFACT),
    isAuthenticated,
    (req, res, next) =>
        validateData(
            [
                body("user_id")
                    .isString()
                    .notEmpty()
                    .custom((value) => mongoose.Types.ObjectId.isValid(value))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
                body("artifact_id")
                    .isString()
                    .notEmpty()
                    .custom((value) => mongoose.Types.ObjectId.isValid(value))
                    .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
            ],
            req,
            res,
            next,
        ),
    deleteFavouriteArtifact,
);

module.exports = router;
