import { render, screen } from '@testing-library/react';
import { AppContext } from '../../src/contexts/AppContext';
import { useContext } from 'react';

const SomeComponent = () => {
    const { screenSize } = useContext(AppContext);
    return <div>{screenSize.xs ? 'Mobile' : 'Desktop'}</div>;
};

describe('AppContext', () => {
    it('should render Mobile when screenSize.xs is true', () => {
        const mockContextValue = {
            screenSize: { xs: true, md: false },
        };

        render(
            <AppContext.Provider value={mockContextValue}>
                <SomeComponent />
            </AppContext.Provider>
        );

        expect(screen.getByText('Mobile')).toBeInTheDocument();
    });

    it('should render Desktop when screenSize.xs is false', () => {
        const mockContextValue = {
            screenSize: { xs: false, md: true },
        };

        render(
            <AppContext.Provider value={mockContextValue}>
                <SomeComponent />
            </AppContext.Provider>
        );

        expect(screen.getByText('Desktop')).toBeInTheDocument();
    });
});
