require('dotenv').config()
require('aws-sdk/lib/maintenance_mode_message').suppress = true;

console.log('process has started')

require('./modules/processLogs')
const db = require('./modules/db');

require('./services/dbBackup')

Promise.all([
    new Promise((resolve, reject) => {
        db.qm.once('open', resolve);
        db.qm.on('error', reject);
    }), new Promise((resolve, reject) => {
        db.qmai.once('open', resolve);
        db.qmai.on('error', reject);
    }), new Promise((resolve, reject) => {
        db.qmShared.once('open', resolve);
        db.qmShared.on('error', reject);
    })
]).then(() => {
    // if (process.env.NODE_ENV !== 'prod') {
    //     console.warn('Disabled gps service in non-production environment')
    // } else {
        require('./services/gps')
    // }
    // require('./services/statsProcessor')
    // require('./services/notificationAlertsProcessor')
    // require('./services/newArtifactProcessor')
    // require('./services/summaryReportsProcessor')
    // require('./services/seaVisionSampleRequests')
})

process.on('uncaughtException', (err) => {
    console.error('(FATAL ERROR) Uncaught Exception:', err)
})