import axiosInstance from "../axios";

class ApiKeyController {
    async fetchAll() {
        const response = await axiosInstance.get("/apiKeys");
        return response.data;
    }

    async create({ description, email }) {
        const response = await axiosInstance.post("/apiKeys", { description, email }, { meta: { showSnackbar: true } });
        return response.data;
    }

    async update({ id, email, description }) {
        const response = await axiosInstance.patch(`/apiKeys/${id}/details`, { email, description }, { meta: { showSnackbar: true } });
        return response.data;
    }

    async delete({ id }) {
        const response = await axiosInstance.delete(`/apiKeys/${id}`, { meta: { showSnackbar: true } });
        return response.data;
    }

    async revoke({ id, revoke }) {
        const response = await axiosInstance.patch(`/apiKeys/${id}/revoke`, { revoke }, { meta: { showSnackbar: true } });
        return response.data;
    }

    async updateAllowedEndpoints({ id, allowed_endpoints }) {
        const response = await axiosInstance.patch(`/apiKeys/${id}/allowedEndpoints`, { allowed_endpoints }, { meta: { showSnackbar: true } });
        return response.data;
    }

    async updateAllowedUnits({ id, allowed_units }) {
        const response = await axiosInstance.patch(`/apiKeys/${id}/allowedUnits`, { allowed_units }, { meta: { showSnackbar: true } });
        return response.data;
    }
}

const apiKeyController = new ApiKeyController();

export default apiKeyController;
