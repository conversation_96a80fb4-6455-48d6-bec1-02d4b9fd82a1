// eslint-disable no-unused-vars
import React, { Component } from "react";
import { Typo<PERSON>, Button, Grid, Accordion, AccordionSummary, AccordionDetails, Box } from "@mui/material";
import { ReportProblem } from "@mui/icons-material";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import theme from "../theme";

class ErrorBoundary extends Component {
    constructor(props) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null,
            errorPath: null,
        };
    }

    static getDerivedStateFromError(error) {
        return { hasError: true, error };
    }

    componentDidCatch(error, errorInfo) {
        console.error("Error caught by ErrorBoundary:", error, errorInfo);
        this.setState({
            error: error,
            errorInfo: errorInfo,
        });
    }

    handleRefresh = () => {
        this.setState({ hasError: false, error: null, errorInfo: null });
        window.location.reload();
    };

    render() {
        const { hasError, error, errorInfo } = this.state;

        if (hasError) {
            return (
                <Grid
                    container
                    direction={"column"}
                    justifyContent={"center"}
                    alignItems={"center"}
                    sx={{ height: "100%", background: theme.palette.custom.darkBlue }}
                >
                    <Grid display="flex" flexDirection={"column"} justifyContent={"center"} alignItems={"center"}>
                        <ReportProblem sx={{ fontSize: 100, color: "#E60000" }} />
                        <Typography fontSize={24} color={"#FFFFFF"}>
                            Something went wrong!
                        </Typography>
                        <Typography fontWeight={400} color={"#FFFFFF"} marginBottom={2}>
                            We apologize for the inconvenience. Please try refreshing the page.
                        </Typography>
                        <Accordion disableGutters sx={{ background: "#1E293B", maxWidth: 500 }}>
                            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                                <Typography color={"#FFFFFF"}>Error Details</Typography>
                            </AccordionSummary>
                            <AccordionDetails sx={{ backgroundColor: "primary.main", marginBottom: 2 }}>
                                <Typography color="#FFFFFF" fontWeight={400}>
                                    {error && error.toString()}
                                </Typography>
                                <Box
                                    component="span"
                                    sx={{
                                        display: "block",
                                        padding: 1,
                                        border: "1px solid",
                                        borderColor: "#FFFFFF",
                                        borderRadius: 1,
                                        maxHeight: 300,
                                        overflow: "auto",
                                    }}
                                >
                                    <Typography color="#FFFFFF" fontWeight={400} fontSize={12}>
                                        {errorInfo && errorInfo.componentStack}
                                    </Typography>
                                </Box>
                            </AccordionDetails>
                        </Accordion>
                        <Button
                            variant="contained"
                            disableRipple
                            color="primary"
                            sx={{ textTransform: "none", marginTop: 2 }}
                            onClick={this.handleRefresh}
                        >
                            Reload Page
                        </Button>
                    </Grid>
                </Grid>
            );
        }

        return this.props.children;
    }
}

export default ErrorBoundary;
