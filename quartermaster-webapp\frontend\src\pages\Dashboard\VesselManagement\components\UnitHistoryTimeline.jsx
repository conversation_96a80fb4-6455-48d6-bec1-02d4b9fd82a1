import { alpha, Grid, Typography, Card, Chip, Box } from "@mui/material";
import { History, PlayArrow, Stop } from "@mui/icons-material";
import dayjs from "dayjs";
import theme from "../../../../theme";

const UnitHistoryTimeline = ({ unitsHistory, timezone }) => {
    if (!unitsHistory || unitsHistory.length === 0) {
        return (
            <Grid container alignItems="center" gap={1} sx={{ py: 2 }}>
                <History sx={{ color: theme.palette.custom.offline, fontSize: 20 }} />
                <Typography variant="body2" color={theme.palette.custom.offline}>
                    No unit history available
                </Typography>
            </Grid>
        );
    }

    return (
        <Grid container direction="column" sx={{ gap: 1, py: 1, maxHeight: { xs: "none", lg: "50vh" }, overflowY: "auto", flexWrap: "nowrap" }}>
            <Grid container alignItems="center" sx={{ gap: 1, mb: 1 }}>
                <History sx={{ color: theme.palette.custom.mainBlue, fontSize: 20 }} />
                <Typography variant="body2" fontWeight={600} color="#FFFFFF">
                    Unit History ({unitsHistory.length} {unitsHistory.length === 1 ? "entry" : "entries"})
                </Typography>
            </Grid>

            {unitsHistory.map((entry, index) => {
                const isActive = !entry.unmount_timestamp;
                const mountDate = dayjs(entry.mount_timestamp).tz(timezone);
                const unmountDate = entry.unmount_timestamp ? dayjs(entry.unmount_timestamp).tz(timezone) : null;

                return (
                    <Grid key={index} container alignItems="center" sx={{ gap: 1, pl: 3 }}>
                        <Grid>
                            <Box
                                sx={{
                                    width: 8,
                                    height: 8,
                                    borderRadius: "50%",
                                    bgcolor: isActive ? theme.palette.success.main : theme.palette.custom.offline,
                                    border: `2px solid ${isActive ? theme.palette.success.main : theme.palette.custom.offline}`,
                                }}
                            />
                        </Grid>
                        <Grid size="grow">
                            <Card
                                sx={{
                                    bgcolor: alpha(theme.palette.custom.darkBlue, 0.3),
                                    border: `1px solid ${alpha(theme.palette.custom.mainBlue, 0.2)}`,
                                    borderRadius: 2,
                                    p: 1.5,
                                }}
                            >
                                <Grid container justifyContent="space-between" alignItems="center" sx={{ gap: 1 }}>
                                    <Grid size="grow">
                                        <Typography variant="body2" fontWeight={600} color="#FFFFFF">
                                            {entry.unit_id}
                                        </Typography>
                                        <Grid container alignItems="center" sx={{ gap: 1, mt: 0.5 }}>
                                            <PlayArrow sx={{ color: theme.palette.success.main, fontSize: 14 }} />
                                            <Typography variant="caption" color={theme.palette.success.main}>
                                                Mounted: {mountDate.format("MMM DD, YYYY HH:MM:ss A")}
                                            </Typography>
                                        </Grid>
                                        {unmountDate && (
                                            <Grid container alignItems="center" sx={{ gap: 1, mt: 0.5 }}>
                                                <Stop sx={{ color: theme.palette.warning.main, fontSize: 14 }} />
                                                <Typography variant="caption" color={theme.palette.warning.main}>
                                                    Unmounted: {unmountDate.format("MMM DD, YYYY HH:MM:ss A")}
                                                </Typography>
                                            </Grid>
                                        )}
                                    </Grid>
                                    <Grid>
                                        <Chip
                                            label={isActive ? "Active" : "Inactive"}
                                            size="small"
                                            sx={{
                                                bgcolor: isActive ? alpha(theme.palette.success.main, 0.2) : alpha(theme.palette.custom.offline, 0.2),
                                                color: isActive ? theme.palette.success.main : theme.palette.custom.offline,
                                                border: `1px solid ${isActive ? theme.palette.success.main : theme.palette.custom.offline}`,
                                                fontWeight: 600,
                                                fontSize: "0.7rem",
                                            }}
                                        />
                                    </Grid>
                                </Grid>
                            </Card>
                        </Grid>
                    </Grid>
                );
            })}
        </Grid>
    );
};

export default UnitHistoryTimeline;
