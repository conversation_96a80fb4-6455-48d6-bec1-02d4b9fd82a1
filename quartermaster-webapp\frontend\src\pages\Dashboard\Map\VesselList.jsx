import { Grid, Skeleton, FormControlLabel, Checkbox, IconButton, CircularProgress, Typography } from "@mui/material";
import { GpsFixed, GpsNotFixed, Warning } from "@mui/icons-material";
import { defaultValues } from "../../../utils";
import { useApp } from "../../../hooks/AppHook";
import RegionGroupFilter from "../../../components/RegionGroupFilter";
import { useEffect, useState, useMemo } from "react";
import useGroupRegions from "../../../hooks/GroupRegionHook";

const VesselList = ({
    vessels,
    selectedVessels,
    availableVessels,
    emptyVessels,
    errorsomeVessels,
    loadingVessels,
    handleVesselSelect,
    setFocusedVessel,
    focusedVessel,
}) => {
    const { devMode } = useApp();
    const { regions, fetchRegions } = useGroupRegions();
    const [selectedRegionGroup, setSelectedRegionGroup] = useState("all");
    const [regionGroups, setRegionGroups] = useState([]);

    useEffect(() => {
        if (!vessels) return;
        fetchRegionGroups();
    }, [vessels, regions]);

    const fetchRegionGroups = async () => {
        if (regions) {
            const filteredRegionGroups = regions.filter((rg) => vessels.some((v) => v.region_group === rg._id));
            setRegionGroups(filteredRegionGroups);
        } else {
            fetchRegions();
        }
    };

    const filteredVessels = useMemo(() => {
        if (selectedRegionGroup === "all") return vessels;
        console.log(selectedRegionGroup, vessels);
        return vessels.filter((vessel) => vessel.region_group === selectedRegionGroup);
    }, [vessels, selectedRegionGroup]);

    if (!vessels)
        return (
            <Grid container padding={2} gap={1} flexDirection={"column"} className="map-step-2">
                {Array.from({ length: 3 }).map((_, i) => (
                    <Grid container key={i} justifyContent={"space-between"}>
                        <Grid container alignItems={"center"} gap={2} size="auto">
                            <Grid>
                                <Skeleton animation="wave" variant="rectangular" width={20} height={20} />
                            </Grid>
                            <Grid>
                                <Skeleton animation="wave" variant="text" width={150} />
                            </Grid>
                        </Grid>
                        <Grid size="auto">
                            <Skeleton animation="wave" variant="circular" width={30} height={30} />
                        </Grid>
                    </Grid>
                ))}
            </Grid>
        );

    return (
        <Grid
            container
            flexDirection={"column"}
            color={"#FFFFFF"}
            padding={2}
            gap={1}
            className="map-step-2"
            minWidth={{ xs: "auto", md: "auto", lg: "350px" }}
        >
            <Grid
                sx={{ position: "absolute", top: { xs: "30px", md: "30px", lg: "8px" }, right: { xs: "10px", md: "10px", lg: "45px" } }}
                display={regionGroups.length > 1 ? "block" : "none"}
            >
                <RegionGroupFilter regionGroups={regionGroups} value={selectedRegionGroup} onChange={setSelectedRegionGroup} />
            </Grid>
            {filteredVessels.length === 0 ? (
                <Grid>
                    <Typography>No vessels found in {regionGroups.find((rg) => rg._id === selectedRegionGroup)?.name}</Typography>
                </Grid>
            ) : (
                filteredVessels.map((vessel) => (
                    <Grid key={vessel.id} container justifyContent={"space-between"} wrap="nowrap">
                        <Grid>
                            <FormControlLabel
                                sx={{ paddingLeft: 1, gap: 1 }}
                                componentsProps={{ typography: { fontSize: { xs: 12, sm: 16 } } }}
                                control={
                                    <Checkbox
                                        checked={selectedVessels.some((v) => v.id === vessel.id)}
                                        disabled={
                                            selectedVessels.some((v) => v.id === vessel.id) &&
                                            !availableVessels.some((v) => v.id === vessel.id) &&
                                            !emptyVessels.some((v) => v.id === vessel.id) &&
                                            !errorsomeVessels.some((v) => v.id === vessel.id)
                                        }
                                        onChange={() => handleVesselSelect(vessel)}
                                        size="small"
                                        disableRipple
                                        sx={{
                                            width: 10,
                                            height: 10,
                                            "&.Mui-checked": {
                                                color: defaultValues.polylineColors[vessel.id] || undefined,
                                            },
                                        }}
                                    />
                                }
                                label={devMode ? vessel.id : vessel.name}
                            />
                        </Grid>
                        <Grid color={"#FFFFFF"} alignItems={"center"} display={"flex"}>
                            {loadingVessels.some((id) => id === vessel.id) ? (
                                <CircularProgress size={18} />
                            ) : selectedVessels.some((v) => v.id === vessel.id) ? (
                                availableVessels.some((v) => v.id === vessel.id) ? (
                                    <IconButton
                                        disabled={
                                            !selectedVessels.some((v) => v.id === vessel.id) || !availableVessels.some((v) => v.id === vessel.id)
                                        }
                                        disableRipple
                                        sx={{ width: 10, height: 10 }}
                                        onClick={() => setFocusedVessel(vessel.id)}
                                    >
                                        <GpsFixed sx={{ color: vessel.id === focusedVessel ? "red" : undefined }} />
                                    </IconButton>
                                ) : emptyVessels.some((v) => v.id === vessel.id) ? (
                                    <IconButton disabled={true} disableRipple sx={{ width: 10, height: 10 }}>
                                        <GpsNotFixed />
                                    </IconButton>
                                ) : errorsomeVessels.some((v) => v.id === vessel.id) ? (
                                    <IconButton disabled={true} disableRipple sx={{ width: 10, height: 10 }}>
                                        <Warning sx={{ color: "yellow" }} />
                                    </IconButton>
                                ) : (
                                    <CircularProgress size={18} />
                                )
                            ) : (
                                <></>
                            )}
                        </Grid>
                    </Grid>
                ))
            )}
        </Grid>
    );
};

export default VesselList;
