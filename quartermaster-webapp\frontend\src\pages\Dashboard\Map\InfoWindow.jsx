import React, { useEffect, useState } from "react";
import { Grid, Typography, IconButton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import s3Controller from "../../../controllers/S3.controller";
import PreviewMedia from "../../../components/PreviewMedia";
import dayjs from "dayjs";
import { defaultValues } from "../../../utils";
import favouriteArtifactsController from "../../../controllers/FavouriteArtifacts.controller";
import { UserProvider } from "../../../providers/UserProvider";

const InfoWindow = ({ artifact, artifactInfowWindow, timezone, user }) => {
    const [src, setSrc] = useState(null);
    const [thumbnail, setThumbnail] = useState(null);
    const [favouriteArtifacts, setFavouriteArtifacts] = useState([]);

    const fetchFavouriteArtifacts = async () => {
        const { favourites } = await favouriteArtifactsController.getAllFavouriteArtifactsAPI({ userId: user._id });
        setFavouriteArtifacts(favourites);
    };

    useEffect(() => {
        fetchFavouriteArtifacts();
    }, []);

    useEffect(() => {
        if (artifact) {
            setSrc(s3Controller.fetchUrl(artifact, artifact.video_path ? "video" : "image"));
            setThumbnail(s3Controller.fetchPreviewUrl(artifact));
        }
    }, [artifact]);

    const handleClose = () => {
        artifactInfowWindow.close();
    };

    return (
        <Grid
            container
            direction="column"
            sx={{
                padding: 2,
                backgroundColor: "#343B44",
                color: "white",
                borderRadius: 2,
                maxWidth: 330,
                gap: 2,
            }}
        >
            {/* Add custom styles for InfoWindow */}
            <style>
                {`
                    .gm-style-iw-chr, .gm-style-iw-tc {
                        display: none !important;
                    }
                    .gm-style .gm-style-iw-c {
                        background-color: #343B44 !important;
                        outline: none;
                        padding: 0;
                    }
                    .gm-style .gm-style-iw-d {
                        overflow: auto !important;
                    }
                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
                        background-color: #fff !important;
                    }
                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
                        background: #343B44 !important;
                    }
                `}
            </style>
            {/* Header */}
            <Grid container justifyContent="space-between" alignItems="center">
                <Typography variant="h6">{artifact.name || "Artifact"}</Typography>
                <IconButton
                    onClick={handleClose}
                    sx={{
                        color: "white",
                        border: "1px solid white",
                        "&:hover": {
                            backgroundColor: "white",
                            color: "#4F5968",
                        },
                    }}
                >
                    <CloseIcon sx={{ fontSize: "14px" }} />
                </IconButton>
            </Grid>
            <Grid
                sx={{
                    position: "relative",
                    backgroundColor: "#343B44",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    height: 200,
                    borderRadius: 1,
                }}
            >
                <PreviewMedia
                    thumbnailLink={thumbnail}
                    originalLink={src}
                    cardId={artifact._id}
                    isImage={!artifact.video_path}
                    style={{ borderRadius: 8 }}
                    favouriteArtifacts={favouriteArtifacts}
                    showFullscreenIconForMap={!artifact.video_path}
                    showVideoThumbnail={artifact.video_path}
                    userTest={user}
                    skeletonStyle={{
                        minHeight: 200,
                        minWidth: 290,
                    }}
                />
            </Grid>
            <Grid>
                <Typography>
                    <strong>Super Category:</strong> {artifact.super_category || "Not available"}
                </Typography>
                <Typography>
                    <strong>Category:</strong> {artifact.category || "Not available"}
                </Typography>
                <Typography>
                    <strong>Color:</strong> {artifact.color || "Not available"}
                </Typography>
                <Typography>
                    <strong>Size:</strong> {artifact.size || "Not available"}
                </Typography>
                <Typography>
                    <strong>Timestamp</strong>:{" "}
                    {artifact.timestamp ? dayjs(artifact.timestamp).tz(timezone).format(defaultValues.dateTimeFormat()) : "Not available"}
                </Typography>
                <Typography>{artifact.others || "Not available"}</Typography>
            </Grid>
        </Grid>
    );
};

const WrappedInfoWindow = (props) => (
    <UserProvider>
        <InfoWindow {...props} />
    </UserProvider>
);

export default WrappedInfoWindow;
