import { renderHook } from '@testing-library/react';
import { useToaster } from '../../src/hooks/ToasterHook';
import { useSnackbar } from 'notistack';

// Mock notistack's useSnackbar
jest.mock('notistack', () => ({
    useSnackbar: jest.fn()
}));

describe('useToaster Hook', () => {
    let mockEnqueueSnackbar;
    let consoleWarnSpy;

    beforeEach(() => {
        mockEnqueueSnackbar = jest.fn();
        useSnackbar.mockReturnValue({ enqueueSnackbar: mockEnqueueSnackbar });
        consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
    });

    afterEach(() => {
        jest.clearAllMocks();
        consoleWarnSpy.mockRestore();
    });

    it('should call enqueueSnackbar with default options', () => {
        const { result } = renderHook(() => useToaster());
        const message = 'Test message';
        
        result.current(message);

        expect(mockEnqueueSnackbar).toHaveBeenCalledWith(message, {
            hideIconVariant: true,
            preventDuplicate: true
        });
    });

    it('should merge custom options with defaults', () => {
        const { result } = renderHook(() => useToaster());
        const message = 'Test message';
        const customOptions = {
            variant: 'success',
            autoHideDuration: 3000
        };

        result.current(message, customOptions);

        expect(mockEnqueueSnackbar).toHaveBeenCalledWith(message, {
            hideIconVariant: true,
            preventDuplicate: true,
            variant: 'success',
            autoHideDuration: 3000
        });
    });

    it('should override default options with custom options', () => {
        const { result } = renderHook(() => useToaster());
        const message = 'Test message';
        const customOptions = {
            hideIconVariant: false,
            preventDuplicate: false,
            variant: 'error'
        };

        result.current(message, customOptions);

        expect(mockEnqueueSnackbar).toHaveBeenCalledWith(message, customOptions);
    });

    it('should log warning and not call enqueueSnackbar when message is empty', () => {
        const { result } = renderHook(() => useToaster());

        result.current('');

        expect(consoleWarnSpy).toHaveBeenCalledWith(
            expect.stringContaining('useToaster called without a message')
        );
        expect(mockEnqueueSnackbar).not.toHaveBeenCalled();
    });

    it('should log warning and not call enqueueSnackbar when message is undefined', () => {
        const { result } = renderHook(() => useToaster());

        result.current();

        expect(consoleWarnSpy).toHaveBeenCalledWith(
            expect.stringContaining('useToaster called without a message')
        );
        expect(mockEnqueueSnackbar).not.toHaveBeenCalled();
    });

    it('should handle all possible option combinations', () => {
        const { result } = renderHook(() => useToaster());
        const testCases = [
            {
                message: 'Success message',
                options: { variant: 'success' }
            },
            {
                message: 'Error message',
                options: { variant: 'error', anchorOrigin: { vertical: 'top', horizontal: 'right' } }
            },
            {
                message: 'Warning message',
                options: { variant: 'warning', autoHideDuration: 5000 }
            },
            {
                message: 'Info message',
                options: { variant: 'info', preventDuplicate: false }
            }
        ];

        testCases.forEach(({ message, options }) => {
            result.current(message, options);
            expect(mockEnqueueSnackbar).toHaveBeenCalledWith(message, {
                hideIconVariant: true,
                preventDuplicate: true,
                ...options
            });
        });
    });
});
