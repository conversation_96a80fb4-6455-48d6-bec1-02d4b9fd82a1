const db = require("../modules/db");

const mongoose = require("mongoose");
const scheme = mongoose.Schema;

const inviteTokenSchema = new scheme(
    {
        token: {
            type: String,
            required: true,
        },
        invited_by: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User",
            required: true,
        },
        email: {
            type: String,
            required: true,
        },
        organization_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Organization",
        },
        role_id: {
            type: Number,
            required: true,
        },
        role: {
            type: String,
            required: true,
        },
        allowed_units: {
            type: [
                {
                    unit_id: {
                        type: String,
                    },
                    region: {
                        type: String,
                    },
                },
            ],
        },
        short_token: {
            type: String,
            required: true,
            unique: true,
        },
        is_used: {
            type: Boolean,
            default: false,
        },
        is_deleted: {
            type: Boolean,
            default: false,
        },
    },
    {
        timestamps: true,
    },
);

const InviteToken = db.qm.model("InviteToken", inviteTokenSchema, "invite_tokens");

module.exports = InviteToken;
