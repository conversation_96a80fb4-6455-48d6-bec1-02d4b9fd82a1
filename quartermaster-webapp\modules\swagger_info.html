<div>
    <h2>
        <strong>Authentication Flow Overview</strong>
    </h2>
    <p>
        The Quartermaster API supports two primary methods for authentication:
        <strong>Basic Authentication</strong>
        and
        <strong>API Key Authentication</strong>
        . Both methods generate a JSON Web Token (JWT) with a 24-hour expiration,
    which is used to access protected routes.
    </p>
    <h3>
        <strong>1. Basic Authentication</strong>
    </h3>
    <h4>
        <strong>Authorization Flow:</strong>
    </h4>
    <ul>
        <li>
            Authenticate via the
            <code>/users/auth</code>
            endpoint by providing either the user's
            <strong>username</strong>
            or
            <strong>email</strong>
            , along with their
            <strong>password</strong>
            .
        </li>
        <li>
            These credentials should be sent in the
            <code>Authorization</code>
            header
      (
            <code>Basic &lt;username&gt;:&lt;password&gt;</code>
            ) as a
            <strong>Base64</strong>
            -encoded
      string.
        </li>
        <li>
            Upon successful authentication, the API generates a
            <strong>JWT token</strong>
            with a 24-hour expiration time.
        </li>
        <li>
            Include the JWT token in the
            <code>Authorization</code>
            header (
            <code>Bearer &lt;token&gt;</code>
            ) for all
      subsequent requests to protected routes.
        </li>
    </ul>
    <h4>
        <strong>Error Handling:</strong>
    </h4>
    <ul>
        <li>If credentials are invalid, the API will return appropriate error messages.</li>
    </ul>
    <h3>
        <strong>2. API Key Authentication</strong>
    </h3>
    <h4>
        <strong>Authorization Flow:</strong>
    </h4>
    <ul>
        <li>
            Authenticate via the
            <code>/users/auth</code>
            endpoint by providing the
            <strong>API key</strong>
            in the
            <code>qm-api-key</code>
            header.
        </li>
        <li>
            If valid, the API generates a
            <strong>JWT token</strong>
            with a 24-hour expiration time.
        </li>
        <li>If the API key is revoked, deleted, or invalid, the API returns error messages detailing the issue.</li>
        <li>
            Include the JWT token in the
            <code>Authorization</code>
            header
      (
            <code>Bearer &lt;token&gt;</code>
            ) for further requests to protected routes.
        </li>
    </ul>
    <hr>
    <h2>
        <strong>Security Measures</strong>
    </h2>
    <p>The Quartermaster API implements several security measures to protect user data and ensure API integrity:</p>
    <ul>
        <li>
            <strong>Rate Limiting:</strong>
            Enforced on all routes to prevent abuse and guarantee fair usage across clients.
        </li>
        <li>
            <strong>Password Security:</strong>
            All passwords are securely
            <strong>hashed</strong>
            before being stored.
        </li>
        <li>
            <strong>Data Privacy:</strong>
            Sensitive data such as passwords and JWT tokens are excluded from API responses.
        </li>
    </ul>
    <hr>
    <h2>
        <strong>Rate Limiting Information</strong>
    </h2>
    <p>
        Rate limiting helps control the number of requests a client can make within a certain time frame, protecting the
    API from overuse and ensuring balanced access for all clients.
    </p>
    <h3>
        <strong>Rate Limit Enforcement</strong>
    </h3>
    <ul>
        <li>
            If the client exceeds the allowed number of requests, the API responds with HTTP status
            <strong>
                429 (Too Many
        Requests)
            </strong>
            , indicating that the client has exceeded the request limit for that period.
        </li>
        <li>Different endpoints may have varying rate limits based on usage patterns and their level of importance.</li>
    </ul>
    <h3>
        <strong>Rate Limit Headers</strong>
    </h3>
    <p>The API provides important rate limit information in the response headers:</p>
    <ul>
        <li>
            <strong>X-RateLimit-Limit:</strong>
            Maximum number of requests allowed within the current time window.
        </li>
        <li>
            <strong>X-RateLimit-Remaining:</strong>
            Number of requests remaining in the current time window. This decreases
      as requests are made.
        </li>
        <li>
            <strong>X-RateLimit-Reset:</strong>
            Time (in seconds) until the rate limit window resets, allowing clients to
      make more requests.
        </li>
    </ul>
    <hr>
    <h2>
        <strong>Code Samples</strong>
    </h2>
    <ul>
        <li>
            <a href="https://qmai.atlassian.net/wiki/external/YzJmMmZkOTVmOTdiNGU3ZGE0MDczMTI4ZjU3OTA1YjM">Getting started with pyhton</a>
        </li>
    </ul>
    <hr>
    <h2>
        <strong>Contact</strong>
    </h2>
    <p>
        For support, assistance, or further inquiries, please contact the
        <strong>Quartermaster</strong>
        support team.
    </p>
    <hr>
    <p>
        <strong>Note:</strong>
        Ensure to follow the correct authentication flow, monitor rate limit headers, and handle
    error responses appropriately when integrating with the API.
    </p>
</div>
