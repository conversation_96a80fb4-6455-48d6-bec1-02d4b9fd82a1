import { useEffect, useRef, useState } from "react";
import { useUser } from "../../../../hooks/UserHook";
import { Autocomplete, TextField, Checkbox, Typography, Chip, Popper, Grid, alpha } from "@mui/material";
import { Close } from "@mui/icons-material";
import theme from "../../../../theme";

const PermissionsField = ({ role, permissions, setRoles, setAllowReset, setAllowSave, setUpdatedRoles, setRowHeight }) => {
    const { user } = useUser();
    const permissionsRef = useRef();
    const allowed_permissions = permissions.filter((p) => !role.denied_permissions.includes(p.permission_id));

    const [openDropdown, setOpenDropdown] = useState(false);

    useEffect(() => {
        if (permissionsRef.current) {
            const height = permissionsRef.current.offsetHeight + 15;
            setRowHeight(role._id, height); // Update the height for the specific row
        }
    }, [permissionsRef.current?.offsetHeight]); // Re-run if the number of permissions changes

    const onChange = (e, newValue) => {
        const filteredValue = newValue.filter((item) =>
            permissions.some((permission) => permission._id === item._id && permission.assignable && !checkRoleUpdation()),
        );

        setAllowSave(true);
        setAllowReset(true);
        setRoles((roles) =>
            roles.map((_role) => {
                if (_role._id === role._id) {
                    _role.denied_permissions = permissions.filter((p) => !filteredValue.find((_p) => _p._id === p._id)).map((p) => p.permission_id);
                    setUpdatedRoles((updatedRoles) => (updatedRoles.includes(role._id) ? updatedRoles : [...updatedRoles, role._id]));
                }
                return _role;
            }),
        );
    };

    const checkRoleUpdation = () => {
        if (user.role_id === role.role_id || !role.editable) return true;
        if (user.role.hierarchy_number >= role.hierarchy_number) return true;
        return false;
    };

    const isDisabled = checkRoleUpdation();

    return (
        <Autocomplete
            disabled={isDisabled}
            popupIcon={!isDisabled && role.editable && role.role_id !== user.role_id ? undefined : false}
            multiple
            options={permissions}
            getOptionLabel={(option) => option.permission_name}
            value={allowed_permissions}
            open={!isDisabled && openDropdown}
            onOpen={() => !isDisabled && setOpenDropdown(true)}
            onClose={() => setOpenDropdown(false)}
            disableCloseOnSelect
            renderOption={(props, option, { selected }) => {
                const { key, ...otherProps } = props;
                const isOptionDisabled = !option.assignable || isDisabled;

                return (
                    <Grid
                        key={key}
                        {...otherProps}
                        sx={{
                            "&:hover": {
                                backgroundColor: alpha(theme.palette.custom.darkBlue, 0.3) + " !important",
                            },
                        }}
                        onMouseDown={(e) => isOptionDisabled && e.preventDefault()}
                    >
                        <Checkbox checked={selected} sx={{ marginRight: 1 }} disabled={isOptionDisabled} />
                        <Typography
                            sx={{
                                color: isOptionDisabled ? "gray" : "inherit",
                            }}
                        >
                            {option.permission_name}
                        </Typography>
                    </Grid>
                );
            }}
            onChange={onChange}
            PopperComponent={(popperProps) =>
                isDisabled ? null : (
                    <Popper
                        {...popperProps}
                        sx={{
                            "& .MuiPaper-root": {
                                backgroundColor: theme.palette.primary.main,
                            },
                        }}
                    />
                )
            }
            renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                    <Chip
                        key={option._id || index}
                        label={option.permission_name}
                        // {...getTagProps({ index })}
                        onDelete={isDisabled || !option.assignable ? undefined : getTagProps({ index }).onDelete}
                        deleteIcon={<Close />}
                        sx={{
                            backgroundColor: "#1B1F2D",
                            margin: 0.5,
                            color: "#FFFFFF",
                            padding: "10px",
                            borderRadius: "5px",
                            height: "auto",
                            fontSize: "14px",
                            fontWeight: "400",
                            "& .MuiChip-deleteIcon": {
                                display: isDisabled || !option.assignable ? "none" : "inline-flex",
                            },
                            "&.Mui-disabled": {
                                opacity: 1,
                            },
                        }}
                    />
                ))
            }
            renderInput={(params) => (
                <TextField
                    ref={permissionsRef}
                    {...params}
                    sx={{
                        backgroundColor: theme.palette.custom.darkBlue,
                    }}
                    variant="outlined"
                />
            )}
            label="Add Permissions"
            sx={{
                padding: "10px",
                "& .MuiFormControl-root": {
                    border: `1px solid ${theme.palette.custom.borderColor}`,
                    borderRadius: "5px",
                },
            }}
        />
    );
};

export default PermissionsField;
