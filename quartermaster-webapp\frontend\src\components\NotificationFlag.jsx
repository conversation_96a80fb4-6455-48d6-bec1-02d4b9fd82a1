import * as FlagIcon from "country-flag-icons/react/3x2";

const NotificationFlag = ({ option }) => {
    if (!option) return null;

    const ComponentToRender = FlagIcon[option?.code];
    const name = (option.name || option).replace(/_/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
    return ComponentToRender ? <ComponentToRender title={name} style={{ marginRight: "8px", height: "20px" }} /> : null;
};

export default NotificationFlag;
