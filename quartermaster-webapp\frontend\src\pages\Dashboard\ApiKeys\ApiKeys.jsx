import {
    alpha,
    CircularProgress,
    FormControl,
    Grid,
    IconButton,
    MenuItem,
    Pagination,
    Select,
    Tooltip,
    Typography,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableRow,
    Collapse,
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import React, { useEffect, useState } from "react";
import axiosInstance from "../../../axios";
import dayjs from "dayjs";
import { userValues } from "../../../utils";
import { Block, Delete, Launch, Replay, VisibilityOutlined, VisibilityOffOutlined, SentimentVeryDissatisfied } from "@mui/icons-material";
// import { getSocket } from "../../../socket";
import { useApp } from "../../../hooks/AppHook";
import AddKeyModal from "./AddKeyModal";
import DeleteKeyModal from "./DeleteKeyModal";
import RevokeKeyModal from "./RevokeKeyModal";
import ApiAccessModal from "./ApiAccessModal";
import EditDetailsModal from "./EditDetailsModal";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import ExpandLessIcon from "@mui/icons-material/ExpandLess";
import { useToaster } from "../../../hooks/ToasterHook";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";
import useGroupRegions from "../../../hooks/GroupRegionHook.jsx";
import apiKeyController from "../../../controllers/ApiKey.controller";
import theme from "../../../theme";
import DeleteButton from "../../../components/DeleteButton";
import EditButton from "../../../components/EditButton";
import UpdateKeyUnits from "./UpdateKeyUnits";
import { useUser } from "../../../hooks/UserHook.jsx";

export default function ApiKeys({ searchQuery, showAddKey, setShowAddKey }) {
    const { isMobile, timezone } = useApp();
    const toaster = useToaster();
    const { user } = useUser();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const { regions, fetchRegions } = useGroupRegions();

    const [isLoading, setIsLoading] = useState(true);
    const [keys, setKeys] = useState([]);
    const [filteredKeys, setFilteredKeys] = useState([]);
    const [endpoints, setEndpoints] = useState();

    const [showKey, setShowKey] = useState(null);

    const [adding, setAdding] = useState(false);

    const [deleteKey, setDeleteKey] = useState();
    const [deleting, setDeleting] = useState();

    const [revokeKey, setRevokeKey] = useState();
    const [revoking, setRevoking] = useState();

    const [updateKeyAccess, setUpdateKeyAccess] = useState();

    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);

    const [expandedRow, setExpandedRow] = useState({});
    const [vessels, setVessels] = useState([]);
    const [regionGroups, setRegionGroups] = useState([]);

    const [editingDetails, setEditingDetails] = useState(null);
    const [updatingDetails, setUpdatingDetails] = useState(null);
    useEffect(() => {
        fetchRegionGroups();
    }, [regions]);

    useEffect(() => {
        fetchKeys();
        fetchEndpoints();
        // fetchVessels();
        // fetchRegionGroups();

        /** TODO: temporarily disabled due to an api endpoint spam resulting in continuous listener invocation */
        // const socket = getSocket();

        // // socket.on("apiKeys/changed", fetchKeys);

        // // return () => {
        // //     socket.off("apiKeys/changed", fetchKeys);
        // // };
    }, []);

    useEffect(() => {
        fetchKeys();
    }, [adding, deleting, revoking, updatingDetails]);

    useEffect(() => {
        if (updateKeyAccess) setUpdateKeyAccess((v) => keys.find((k) => k._id === v._id));
    }, [keys]);

    useEffect(() => {
        let newFilteredKeys = [];
        if (searchQuery.trim()) {
            newFilteredKeys = keys.filter((key) => {
                const description = key.description ? key.description.toLowerCase() : "";
                const requests = key.requests ? parseInt(key.requests, 10) : 0;
                const searchNumber = parseInt(searchQuery, 10);

                return description.includes(searchQuery.toLowerCase()) || (!isNaN(searchNumber) && requests >= searchNumber);
            });
            setFilteredKeys(newFilteredKeys);
        } else {
            setFilteredKeys(keys);
        }
        setPage(1);
    }, [searchQuery, keys, endpoints]);

    useEffect(() => {
        fetchVessels();
    }, [vesselInfo]);

    const fetchKeys = async () => {
        try {
            setIsLoading(true);
            const data = await apiKeyController.fetchAll();
            if (Array.isArray(data) && data.length > 0) {
                const serializedKeys = data.map((e, i) => ({ serial: i + 1, ...e }));
                setKeys(serializedKeys);
            } else {
                setKeys([]);
                toaster("No data found for keys", { variant: "warning" });
            }
        } catch (err) {
            setKeys([]);
            toaster("Something went wrong", { variant: "error" });
            console.error("An error occurred while fetching keys on the ApiKeys Page", err);
        } finally {
            setIsLoading(false);
        }
    };

    const fetchEndpoints = async () => {
        try {
            setIsLoading(true);
            const { data } = await axiosInstance.get("/apiEndpoints");
            if (Array.isArray(data) && data.length > 0) {
                setEndpoints(data);
            } else {
                setEndpoints([]);
                toaster("No data found for endpoints", { variant: "warning" });
            }
        } catch (err) {
            setEndpoints([]);
            toaster("Something went wrong", { variant: "error" });
            console.error("An error occurred while fetching endpoints on the ApiKeys Page", err);
        } finally {
            setIsLoading(false);
        }
    };

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handlePageSizeChange = (event) => {
        setRowsPerPage(event.target.value);
    };

    const handleExpandClick = (row) => {
        setExpandedRow((prevRow) => {
            if (prevRow) {
                return prevRow._id == row._id ? {} : row;
            } else {
                return row;
            }
        });
    };

    const fetchVessels = async () => {
        try {
            if (vesselInfo) {
                setVessels(vesselInfo);
            } else {
                fetchVesselsInfo();
            }
        } catch (err) {
            console.error("An error occurred while fetching vessels on the Users Page:", err);
        }
    };

    const fetchRegionGroups = async () => {
        if (regions) {
            setRegionGroups(regionGroups);
        } else {
            fetchRegions();
        }
    };

    /**
     * @type {Array<import("@mui/x-data-grid").GridColDef>}
     */
    const columns = [
        { field: "serial", headerName: "", maxWidth: 50, renderCell: ({ row }) => row.serial + "." },
        {
            field: "api_key",
            headerName: "API Key",
            flex: 1,
            minWidth: 360,
            renderCell: ({ row }) => (
                <Grid container justifyContent={"space-between"} alignItems={"center"} gap={2} flexWrap={"nowrap"} overflow={"auto"}>
                    <Grid>
                        <Typography>{showKey === row._id ? row.api_key : Array.from({ length: 16 }).fill("*").join("")}</Typography>
                    </Grid>
                    <Grid>
                        <IconButton onClick={() => setShowKey((v) => (v === row._id ? null : row._id))}>
                            {showKey === row._id ? <VisibilityOffOutlined /> : <VisibilityOutlined />}
                        </IconButton>
                    </Grid>
                </Grid>
            ),
        },
        {
            field: "description",
            headerName: "Description",
            flex: 1,
            minWidth: 250,
            renderCell: ({ row }) => (
                <Grid container alignItems={"center"} height={"100%"} gap={1}>
                    <Tooltip enterDelay={300} title={row.description} placement="bottom">
                        <Typography fontSize={"14px"} fontWeight={400}>
                            {row.description.length > 20 ? row.description.slice(0, 30) + "..." : row.description}
                        </Typography>
                    </Tooltip>
                </Grid>
            ),
        },
        {
            field: "email",
            headerName: "Email",
            flex: 1,
            minWidth: 200,
            renderCell: ({ row }) => (
                <Grid container alignItems={"center"} height={"100%"} gap={1}>
                    <Typography fontSize={"14px"} fontWeight={400}>
                        {row.email || "--"}
                    </Typography>
                </Grid>
            ),
        },
        {
            field: "allowed_endpoints",
            headerName: "API Access",
            minWidth: 220,
            renderCell: ({ row }) => (
                <Grid
                    container
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    gap={2}
                    flexWrap={"nowrap"}
                    overflow={"auto"}
                    sx={{ cursor: "pointer" }}
                    onClick={() => setUpdateKeyAccess(row)}
                >
                    <Grid>
                        <Typography fontWeight={"400"} sx={{ textDecoration: "underline" }}>
                            Manage Permissions
                        </Typography>
                    </Grid>
                    <Grid>
                        <Tooltip enterDelay={300} title="Manage Permissions" placement="bottom">
                            <IconButton>
                                <Launch sx={{ color: theme.palette.custom.mainBlue }} />
                            </IconButton>
                        </Tooltip>
                    </Grid>
                </Grid>
            ),
            sortable: false,
        },
        {
            field: "allowed_units",
            headerName: "SmartMasts",
            minWidth: 300,
            renderCell: ({ row }) => {
                const api_key = keys.find((u) => u._id === row._id);
                if (!api_key) return null;

                return (
                    <div style={{ maxWidth: 200 }}>
                        <UpdateKeyUnits disabled={false} api_key={api_key} vessels={vessels} regionGroups={regionGroups} fetchKeys={fetchKeys} />
                    </div>
                );
            },
        },
        {
            field: "requests",
            minWidth: 100,
            headerName: "Total Requests",
        },
        {
            field: "creation_timestamp",
            headerName: "Created",
            minWidth: 150,
            valueGetter: (v) =>
                dayjs(v)
                    .tz(timezone)
                    .format(userValues.dateTimeFormat(user, { exclude_seconds: true, exclude_hours: true, exclude_minutes: true })),
        },
        {
            field: "actions",
            headerName: "Actions",
            minWidth: 200,
            headerAlign: "center",
            renderCell: (params) => (
                <Grid container justifyContent={"center"} gap={1}>
                    <Grid>
                        {revoking === params.row._id ? (
                            <CircularProgress size={18} />
                        ) : (
                            <Tooltip enterDelay={300} title={params.row.is_revoked ? "Restore Access" : "Revoke Access"} placement="bottom">
                                <IconButton
                                    onClick={() => setRevokeKey(params.row)}
                                    sx={{
                                        background: params.row.is_revoked ? "#0478570D" : "#B453091A",
                                        border: `1px solid ${theme.palette.custom.borderColor}`,
                                        borderRadius: "5px",
                                        padding: "8px",
                                    }}
                                >
                                    {params.row.is_revoked ? (
                                        <Replay sx={{ fontSize: "18px" }} color="success" />
                                    ) : (
                                        <Block color="warning" sx={{ fontSize: "18px" }} />
                                    )}
                                </IconButton>
                            </Tooltip>
                        )}
                    </Grid>
                    <Grid>
                        {deleting === params.row._id ? <CircularProgress size={18} /> : <DeleteButton onClick={() => setDeleteKey(params.row)} />}
                    </Grid>
                    <Grid>
                        {updatingDetails === params.row._id ? (
                            <CircularProgress size={18} />
                        ) : (
                            <EditButton onClick={() => setEditingDetails(params.row)} />
                        )}
                    </Grid>
                </Grid>
            ),
            sortable: false,
        },
    ];

    const columnsWithouFilters = [
        ...columns.map((col) => ({
            ...col,
            filterable: false,
            resizable: false,
            disableColumnMenu: true,
            disableReorder: true,
            disableExport: true,
            flex: 1,
        })),
    ];

    // Custom Footer Component
    const CustomFooter = ({ page, rowsPerPage, totalRows, onPageChange, onRowsPerPageChange }) => {
        const startIndex = (page - 1) * rowsPerPage + 1;
        const endIndex = Math.min(page * rowsPerPage, totalRows);

        return (
            <Grid
                container
                justifyContent={{ sm: "space-between", xs: "center" }}
                alignItems={"center"}
                padding={"10px"}
                backgroundColor={alpha(theme.palette.custom.offline, 0.08)}
                gap={2}
                sx={{
                    borderRadius: "5px",
                }}
            >
                <Grid padding={"10px 20px"} size="auto">
                    <Typography fontSize={{ xs: "12px", lg: "14px" }} fontWeight={600}>
                        {`${startIndex} - ${endIndex} of ${totalRows}`}
                    </Typography>
                </Grid>
                <Grid size="auto">
                    <Pagination
                        count={Math.ceil(totalRows / rowsPerPage)}
                        page={page}
                        onChange={onPageChange}
                        shape="rounded"
                        siblingCount={isMobile ? 0 : 1}
                        boundaryCount={1}
                        sx={{
                            "& .MuiButtonBase-root, .MuiPaginationItem-root": {
                                color: "#FFFFFF",
                                minHeight: "30px",
                                fontSize: isMobile ? "9px" : "14px",
                                borderRadius: "8px",
                                minWidth: "32px",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                backgroundColor: alpha(theme.palette.custom.offline, 0.2),
                            },
                            "& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected": {
                                color: "#FFFFFF",
                                backgroundColor: theme.palette.custom.mainBlue,
                            },
                        }}
                    />
                </Grid>
                <Grid justifyContent="flex-end" display={"flex"} size="auto">
                    <FormControl variant="outlined">
                        <Select
                            value={rowsPerPage}
                            onChange={onRowsPerPageChange}
                            sx={{
                                "& .MuiOutlinedInput-notchedOutline": {
                                    border: "none",
                                },
                                "& .MuiSelect-select": {
                                    padding: "10px",
                                    fontSize: isMobile ? "12px" : "16px",
                                    backgroundColor: theme.palette.custom.mainBlue,
                                    borderRadius: "5px",
                                    color: "#FFFFFF",
                                    minWidth: isMobile ? 0 : "80px",
                                },
                            }}
                        >
                            {[5, 10, 20].map((size) => (
                                <MenuItem key={size} value={size}>
                                    {isMobile ? size : `${size} / Page`}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Grid>
            </Grid>
        );
    };

    const noRowsOverlay = () => (
        <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"} height={"100%"}>
            <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
            <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                No data available
            </Typography>
        </Grid>
    );

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            {!isMobile && (
                <Grid overflow={"auto"} size="grow">
                    <DataGrid
                        loading={isLoading}
                        disableRowSelectionOnClick
                        rows={filteredKeys.slice((page - 1) * rowsPerPage, page * rowsPerPage)}
                        columns={columnsWithouFilters}
                        getRowId={(row) => row._id}
                        slots={{
                            footer: () => (
                                <CustomFooter
                                    page={page}
                                    rowsPerPage={rowsPerPage}
                                    totalRows={filteredKeys.length}
                                    onPageChange={handlePageChange}
                                    onRowsPerPageChange={handlePageSizeChange}
                                />
                            ),
                            noRowsOverlay,
                        }}
                    />
                </Grid>
            )}
            {isMobile && (
                <Grid
                    container
                    overflow={"auto"}
                    display={"block"}
                    border={`1px solid ${theme.palette.custom.borderColor}`}
                    borderRadius={"10px"}
                    padding={"10px 24px"}
                    size="grow"
                >
                    <Grid container paddingY={1} size="grow">
                        {["#", "API Keys", "Details"].map((col, i) => (
                            <Grid
                                key={i}
                                sx={{
                                    color: col == "#" ? theme.palette.custom.darkBlue : theme.palette.custom.mainBlue,
                                    minWidth: col == "#" ? "30px" : "auto",
                                    flex: col == "API Keys" ? 1 : 0,
                                    padding: 0,
                                    border: "none",
                                }}
                            >
                                {col}
                            </Grid>
                        ))}
                    </Grid>
                    {isLoading && (
                        <Grid
                            container
                            display={"flex"}
                            justifyContent={"center"}
                            alignItems={"center"}
                            height={{ xs: "70%", sm: "80%" }}
                            overflow={"auto"}
                            marginBottom={2}
                            size="grow"
                        >
                            <CircularProgress size={40} />
                        </Grid>
                    )}
                    {!isLoading && filteredKeys.length === 0 && (
                        <Grid
                            container
                            display={"flex"}
                            justifyContent={"center"}
                            alignItems={"center"}
                            height={{ xs: "70%", sm: "80%" }}
                            overflow={"auto"}
                            marginBottom={2}
                            size="grow"
                        >
                            {noRowsOverlay()}
                        </Grid>
                    )}
                    {!isLoading && filteredKeys.length !== 0 && (
                        <Grid container height={{ xs: "70%", sm: "80%" }} overflow={"auto"} marginBottom={2} size="grow">
                            <TableContainer>
                                <Table sx={{ minWidth: isMobile ? 0 : 650 }} aria-labelledby="tableTitle">
                                    <TableBody>
                                        {filteredKeys.map((key) => (
                                            <React.Fragment key={key.serial}>
                                                <TableRow hover>
                                                    <TableCell colSpan={5} sx={{ paddingX: "0 !important", borderBottom: 0 }}>
                                                        <Grid container display={"flex"}>
                                                            <Grid
                                                                container
                                                                display={"flex"}
                                                                flex={1}
                                                                alignItems={"center"}
                                                                justifyContent={"space-between"}
                                                            >
                                                                <Grid
                                                                    container
                                                                    justifyContent={"space-between"}
                                                                    alignItems={"center"}
                                                                    gap={2}
                                                                    flexWrap={"nowrap"}
                                                                    overflow={"auto"}
                                                                    color={"#FFFFFF"}
                                                                >
                                                                    <Grid size="auto">
                                                                        <Typography>{key.serial + "."}</Typography>
                                                                    </Grid>
                                                                    <Grid size="grow">
                                                                        <Typography>
                                                                            {showKey === key._id
                                                                                ? key.api_key
                                                                                : Array.from({ length: 16 }).fill("*").join("")}
                                                                        </Typography>
                                                                    </Grid>
                                                                    <Grid size="auto">
                                                                        <IconButton
                                                                            onClick={() => setShowKey((v) => (v === key._id ? null : key._id))}
                                                                        >
                                                                            {showKey === key._id ? <VisibilityOffOutlined /> : <VisibilityOutlined />}
                                                                        </IconButton>
                                                                        <IconButton onClick={() => handleExpandClick(key)} sx={{ padding: 0 }}>
                                                                            {expandedRow?._id == key._id ? (
                                                                                <ExpandLessIcon
                                                                                    sx={{ color: alpha("#FFFFFF", 0.6), padding: 0, marginRight: 2 }}
                                                                                />
                                                                            ) : (
                                                                                <ExpandMoreIcon
                                                                                    sx={{ color: alpha("#FFFFFF", 0.6), padding: 0, marginRight: 2 }}
                                                                                />
                                                                            )}
                                                                        </IconButton>
                                                                    </Grid>
                                                                </Grid>
                                                            </Grid>
                                                        </Grid>
                                                    </TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell colSpan={5} sx={{ padding: 0, borderBottom: 0 }}>
                                                        <Collapse
                                                            in={expandedRow._id == key._id}
                                                            sx={{
                                                                width: "100%",
                                                                backgroundColor: alpha(theme.palette.custom.offline, 0.08),
                                                                borderRadius: "10px",
                                                                padding: "0 20px",
                                                                "& .MuiCollapse-wrapperInner": {
                                                                    display: "flex",
                                                                    flexDirection: "column",
                                                                },
                                                            }}
                                                        >
                                                            <Grid
                                                                container
                                                                key={expandedRow._id}
                                                                sx={{
                                                                    display: "flex",
                                                                    width: "100%",
                                                                    paddingY: 2,
                                                                    rowGap: "10px",
                                                                    flexWrap: "wrap",
                                                                }}
                                                            >
                                                                {[
                                                                    { label: "Description", value: expandedRow.description },
                                                                    {
                                                                        label: "Email",
                                                                        renderCell: () => (
                                                                            <Grid container alignItems={"center"} gap={1}>
                                                                                <Typography variant="h6" fontSize="16px !important" color="#fff">
                                                                                    {expandedRow.email || "--"}
                                                                                </Typography>
                                                                            </Grid>
                                                                        ),
                                                                    },
                                                                    {
                                                                        label: "Api Access",
                                                                        renderCell: () => (
                                                                            <Grid
                                                                                container
                                                                                justifyContent={"space-between"}
                                                                                alignItems={"center"}
                                                                                gap={2}
                                                                                flexWrap={"nowrap"}
                                                                                overflow={"auto"}
                                                                            >
                                                                                <Grid>
                                                                                    <Typography
                                                                                        fontWeight={"400"}
                                                                                        sx={{ textDecoration: "underline" }}
                                                                                    >
                                                                                        Manage Permissions
                                                                                    </Typography>
                                                                                </Grid>
                                                                                <Grid>
                                                                                    <Tooltip
                                                                                        enterDelay={300}
                                                                                        title="Manage Permissions"
                                                                                        placement="bottom"
                                                                                    >
                                                                                        <IconButton onClick={() => setUpdateKeyAccess(key)}>
                                                                                            <Launch sx={{ color: theme.palette.custom.mainBlue }} />
                                                                                        </IconButton>
                                                                                    </Tooltip>
                                                                                </Grid>
                                                                            </Grid>
                                                                        ),
                                                                    },
                                                                    { label: "Total Requests", value: expandedRow.requests },
                                                                    {
                                                                        label: "Created",
                                                                        value: dayjs(expandedRow.creation_timestamp)
                                                                            .tz(timezone)
                                                                            .format(
                                                                                userValues.dateTimeFormat(user, {
                                                                                    exclude_seconds: true,
                                                                                }),
                                                                            ),
                                                                    },
                                                                    {
                                                                        label: "Action",
                                                                        renderCell: () => (
                                                                            <Grid container gap={1}>
                                                                                <Grid>
                                                                                    {revoking === key._id ? (
                                                                                        <CircularProgress size={18} />
                                                                                    ) : (
                                                                                        <IconButton
                                                                                            onClick={() => setRevokeKey(key)}
                                                                                            sx={{
                                                                                                background: key.is_revoked
                                                                                                    ? "#0478570D"
                                                                                                    : "#B453091A",
                                                                                                border: `1px solid ${theme.palette.custom.borderColor}`,
                                                                                                borderRadius: "5px",
                                                                                                padding: "5px",
                                                                                            }}
                                                                                        >
                                                                                            {key.is_revoked ? (
                                                                                                <Replay color="success" sx={{ fontSize: "18px" }} />
                                                                                            ) : (
                                                                                                <Block color="warning" sx={{ fontSize: "18px" }} />
                                                                                            )}
                                                                                            <Typography
                                                                                                fontSize="16px"
                                                                                                marginLeft={0.2}
                                                                                                color={key.is_revoked ? "#047857" : "#B45309"}
                                                                                                fontWeight={"400"}
                                                                                            >
                                                                                                {key.is_revoked ? "Restore" : "Revoke"}
                                                                                            </Typography>
                                                                                        </IconButton>
                                                                                    )}
                                                                                </Grid>
                                                                                <Grid>
                                                                                    {deleting === key._id ? (
                                                                                        <CircularProgress size={18} />
                                                                                    ) : (
                                                                                        <IconButton
                                                                                            onClick={() => setDeleteKey(key)}
                                                                                            sx={{
                                                                                                background: "#E600000D",
                                                                                                border: `1px solid ${theme.palette.custom.borderColor}`,
                                                                                                borderRadius: "5px",
                                                                                                padding: "5px",
                                                                                            }}
                                                                                        >
                                                                                            <Delete color="error" sx={{ fontSize: "18px" }} />
                                                                                            <Typography
                                                                                                fontSize="16px"
                                                                                                color="error"
                                                                                                marginLeft={0.2}
                                                                                                fontWeight={"400"}
                                                                                            >
                                                                                                Delete
                                                                                            </Typography>
                                                                                        </IconButton>
                                                                                    )}
                                                                                </Grid>
                                                                            </Grid>
                                                                        ),
                                                                    },
                                                                ].map((field) => (
                                                                    <Grid
                                                                        key={field.label}
                                                                        sx={{
                                                                            color: "#FFFFFF",
                                                                            padding: 0,
                                                                            border: "none",
                                                                        }}
                                                                        size={{
                                                                            xs: 12,
                                                                            sm: 6,
                                                                        }}
                                                                    >
                                                                        <Typography
                                                                            fontSize="14px"
                                                                            fontWeight="400"
                                                                            sx={{
                                                                                color: theme.palette.custom.mainBlue,
                                                                                flex: 1,
                                                                                padding: 0,
                                                                                border: "none",
                                                                            }}
                                                                        >
                                                                            {field.label}
                                                                        </Typography>
                                                                        {field?.renderCell ? (
                                                                            field.renderCell()
                                                                        ) : (
                                                                            <Typography variant="h6" fontSize="16px !important" color="#fff">
                                                                                {field.value ?? "--"}
                                                                            </Typography>
                                                                        )}
                                                                    </Grid>
                                                                ))}
                                                            </Grid>
                                                        </Collapse>
                                                    </TableCell>
                                                </TableRow>
                                            </React.Fragment>
                                        ))}
                                    </TableBody>
                                </Table>
                            </TableContainer>
                        </Grid>
                    )}
                    <CustomFooter
                        page={page}
                        rowsPerPage={rowsPerPage}
                        totalRows={filteredKeys.length}
                        onPageChange={handlePageChange}
                        onRowsPerPageChange={handlePageSizeChange}
                    />
                </Grid>
            )}
            <AddKeyModal showAddKey={showAddKey} setShowAddKey={setShowAddKey} setAdding={setAdding} fetchKeys={fetchKeys} />
            <DeleteKeyModal deleteKey={deleteKey} setDeleteKey={setDeleteKey} setDeleting={setDeleting} fetchKeys={fetchKeys} />
            <RevokeKeyModal revokeKey={revokeKey} setRevokeKey={setRevokeKey} setRevoking={setRevoking} fetchKeys={fetchKeys} />
            {endpoints && (
                <ApiAccessModal
                    updateKeyAccess={updateKeyAccess}
                    setUpdateKeyAccess={setUpdateKeyAccess}
                    endpoints={endpoints}
                    fetchKeys={fetchKeys}
                />
            )}
            <EditDetailsModal
                apiKey={editingDetails}
                showEditModal={!!editingDetails}
                setShowEditModal={(show) => !show && setEditingDetails(null)}
                setUpdating={setUpdatingDetails}
                fetchKeys={fetchKeys}
            />
        </Grid>
    );
}
