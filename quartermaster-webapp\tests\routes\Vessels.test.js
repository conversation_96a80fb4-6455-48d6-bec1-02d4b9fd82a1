const User = require('../../models/User');
const { usersList } = require('../data/Users');
const { generateUserToken, authorizedUser, nonAuthorizedUser, generateApiToken, nonAuthorizedApiKey, authorizedApiKey } = require('../data/Auth');
const ApiKey = require('../../models/ApiKey');
const Region = require('../../models/Region');
const { regionsList } = require('../data/Regions');
const request = require("supertest");
const app = require('../../server');

const awsKinesis = require('../../modules/awsKinesis');

jest.mock('../../modules/awsKinesis');
jest.mock('../../models/Region');

jest.mock('../../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));

describe.only('GET /api/vessels/info', () => {
    const runTests = (authMethod, generateToken, userOrApiKey, mockAuthorizationModel) => {
        const authMockResolve = authMethod === 'user' ? [userOrApiKey.authorized] : userOrApiKey.authorized;
        const authToken = 'Bearer ' + generateToken(userOrApiKey.authorized._id);

        describe(`${authMethod} authentication`, () => {

            beforeEach(() => {
                jest.resetAllMocks();
            });

            it('should return 401 if no token is provided', async () => {
                const res = await request(app).get('/api/vessels/info');
                expect(res.status).toEqual(401);
            });

            /** Deprecated listStreamsInfo. Please fix accordingly */

            // it('should return 200 and fetch region and vessel data', async () => {
            //     mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

            //     Region.find.mockResolvedValueOnce(regionsList);
            //     awsKinesis.listStreamsInfo.mockResolvedValueOnce([{ streamName: 'vessel-1' }, { streamName: 'vessel-2' }]);

            //     const res = await request(app)
            //         .get('/api/vessels/info')
            //         .set('Authorization', authToken);

            //     expect(res.status).toBe(200);
            //     expect(res.body).toEqual(expect.arrayContaining([{ streamName: 'vessel-1' }, { streamName: 'vessel-2' }]));
            // });

            it('should return 500 if there is an error fetching regions', async () => {
                mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);
                Region.find.mockRejectedValueOnce(new Error('Error fetching regions'));

                const res = await request(app)
                    .get('/api/vessels/info')
                    .set('Authorization', authToken);

                expect(res.status).toBe(500);
                expect(res.body).toHaveProperty('message', 'Unexpected error occured: Error fetching regions');
            });

            /** Deprecated listStreamsInfo. Please fix accordingly */

            // it('should handle errors within awsKinesis.listStreamsInfo gracefully', async () => {
            //     mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

            //     Region.find.mockResolvedValueOnce(regionsList);

            //     awsKinesis.listStreamsInfo.mockImplementation(async ({ region }) => {
            //         if (region === regionsList[0].value) {
            //             throw new Error('Error fetching streams');
            //         }
            //         return [{ streamName: 'vessel-2' }];
            //     });

            //     const res = await request(app)
            //         .get('/api/vessels/info')
            //         .set('Authorization', authToken);

            //     expect(res.status).toBe(200);
            //     expect(res.body).toEqual(expect.arrayContaining([{ streamName: 'vessel-2' }]));
            // });

            /** Deprecated listStreamsInfo. Please fix accordingly */

            // it('should handle empty data response from awsKinesis.listStreamsInfo', async () => {
            //     mockAuthorizationModel.mockResolvedValueOnce(authMockResolve);

            //     Region.find.mockResolvedValueOnce(regionsList);
            //     awsKinesis.listStreamsInfo.mockResolvedValueOnce([]);

            //     const res = await request(app)
            //         .get('/api/vessels/info')
            //         .set('Authorization', authToken);

            //     expect(res.status).toBe(200);
            // });
        });
    };

    runTests('user', generateUserToken, { authorized: authorizedUser, nonAuthorized: nonAuthorizedUser }, User.aggregate);
    runTests('api-key', generateApiToken, { authorized: authorizedApiKey, nonAuthorized: nonAuthorizedApiKey }, ApiKey.findOne);
});
