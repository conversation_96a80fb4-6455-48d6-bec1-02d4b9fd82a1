import { Grid, Typography, FormControl, Select, MenuItem, Pagination, alpha } from "@mui/material";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";

const NotificationCustomFooter = ({ page, rowsPerPage, totalRows, onPageChange, onRowsPerPageChange }) => {
    const { isMobile } = useApp();
    const startIndex = (page - 1) * rowsPerPage + 1;
    const endIndex = Math.min(page * rowsPerPage, totalRows);

    return (
        <Grid
            container
            justifyContent={{ sm: "space-between", xs: "center" }}
            alignItems={"center"}
            padding={"10px"}
            backgroundColor={alpha(theme.palette.custom.offline, 0.08)}
            gap={2}
            sx={{
                borderRadius: "5px",
            }}
        >
            <Grid padding={"10px 20px"} size="auto">
                <Typography fontSize={{ xs: "12px", lg: "14px" }} fontWeight={600}>
                    {`${startIndex} - ${endIndex} of ${totalRows}`}
                </Typography>
            </Grid>
            <Grid size="auto">
                <Pagination
                    count={Math.ceil(totalRows / rowsPerPage)}
                    page={page}
                    onChange={onPageChange}
                    shape="rounded"
                    siblingCount={isMobile ? 0 : 1}
                    boundaryCount={1}
                    sx={{
                        "& .MuiButtonBase-root, .MuiPaginationItem-root": {
                            color: "#FFFFFF",
                            minHeight: "30px",
                            fontSize: isMobile ? "9px" : "14px",
                            borderRadius: "8px",
                            minWidth: "32px",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                            backgroundColor: alpha(theme.palette.custom.offline, 0.2),
                        },
                        "& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected": {
                            color: "#FFFFFF",
                            backgroundColor: theme.palette.custom.mainBlue,
                        },
                    }}
                />
            </Grid>
            <Grid justifyContent="flex-end" display={"flex"} size="auto">
                <FormControl variant="outlined">
                    <Select
                        value={rowsPerPage}
                        onChange={onRowsPerPageChange}
                        sx={{
                            "& .MuiOutlinedInput-notchedOutline": {
                                border: "none",
                            },
                            "& .MuiSelect-select": {
                                padding: "10px",
                                fontSize: isMobile ? "12px" : "16px",
                                backgroundColor: theme.palette.custom.mainBlue,
                                borderRadius: "5px",
                                color: "#FFFFFF",
                                minWidth: isMobile ? 0 : "80px",
                            },
                        }}
                    >
                        {[5, 10, 20].map((size) => (
                            <MenuItem key={size} value={size}>
                                {isMobile ? size : `${size} / Page`}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Grid>
        </Grid>
    );
};

export default NotificationCustomFooter;
