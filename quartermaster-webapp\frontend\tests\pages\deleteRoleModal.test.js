import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import DeleteRoleModal from '../../src/pages/Dashboard/User/Roles/DeleteRoleModal';
import axiosInstance from '../../src/axios';

jest.mock('../../src/axios', () => ({
    delete: jest.fn(),
}));

describe('DeleteRoleModal Component', () => {
    let setDeleteRoleMock, setDeletingMock, onSuccessMock;

    beforeEach(() => {
        setDeleteRoleMock = jest.fn();
        setDeletingMock = jest.fn();
        onSuccessMock = jest.fn();

        axiosInstance.delete.mockResolvedValue({});
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should render the modal with role name when deleteRole is provided', () => {
        const deleteRole = { _id: '1', role_name: 'Admin' };

        render(
            <DeleteRoleModal
                deleteRole={deleteRole}
                setDeleteRole={setDeleteRoleMock}
                setDeleting={setDeletingMock}
                onSuccess={onSuccessMock}
            />
        );

        expect(screen.getByText(/Are you sure you want to delete role/i)).toBeInTheDocument();
        expect(screen.getByText(/Admin/)).toBeInTheDocument();
    });

    it('should call handleClose when Cancel button is clicked', () => {
        const deleteRole = { _id: '1', role_name: 'Admin' };

        render(
            <DeleteRoleModal
                deleteRole={deleteRole}
                setDeleteRole={setDeleteRoleMock}
                setDeleting={setDeletingMock}
                onSuccess={onSuccessMock}
            />
        );

        const cancelButton = screen.getByText(/Cancel/i);
        fireEvent.click(cancelButton);

        expect(setDeleteRoleMock).toHaveBeenCalledWith();
    });

    it('should call onDelete and axiosInstance.delete when Delete button is clicked', async () => {
        const deleteRole = { _id: '1', role_name: 'Admin' };
        render(
            <DeleteRoleModal
                deleteRole={deleteRole}
                setDeleteRole={setDeleteRoleMock}
                setDeleting={setDeletingMock}
                onSuccess={onSuccessMock}
            />
        );

        const deleteButton = screen.getByRole('button', { name: /Delete/i });
        fireEvent.click(deleteButton);

        expect(setDeleteRoleMock).toHaveBeenCalledWith();
        expect(setDeletingMock).toHaveBeenCalledWith(deleteRole.role_id);
        await waitFor(() => expect(axiosInstance.delete).toHaveBeenCalledWith('/roles/1', expect.anything()));
        expect(onSuccessMock).toHaveBeenCalled();
    });

    it('should not render modal when deleteRole is null or undefined', () => {
        render(
            <DeleteRoleModal
                deleteRole={null}
                setDeleteRole={setDeleteRoleMock}
                setDeleting={setDeletingMock}
                onSuccess={onSuccessMock}
            />
        );

        expect(screen.queryByText(/Are you sure you want to delete role/i)).not.toBeInTheDocument();
    });
});
