import React from 'react';
import { render, screen } from '@testing-library/react';
import <PERSON><PERSON><PERSON> from '../../../src/components/Charts/BarChart';
import { Bar } from 'react-chartjs-2';

jest.mock('react-chartjs-2', () => ({
    Bar: jest.fn(() => <div data-testid="bar-chart" />)
}));

describe('BarChart Component', () => {
    const mockData = {
        labels: ['January', 'February', 'March', 'April'],
        datasets: [{
            label: 'Sales',
            data: [30, 50, 70, 90],
            backgroundColor: 'rgba(255, 99, 132, 0.2)',
            borderColor: 'rgba(255, 99, 132, 1)',
            borderWidth: 1,
        }],
    };

    const mockOptions = {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: 'Monthly Sales',
            },
        },
    };

    it('renders the BarChart component', () => {
        render(<BarChart data={mockData} options={mockOptions} />);
        
        const chartElement = screen.getByTestId('bar-chart');
        expect(chartElement).toBeInTheDocument();
    });

    it('should pass the correct data and options to the Bar component', () => {
        render(<BarChart data={mockData} options={mockOptions} />);
        
        expect(Bar).toHaveBeenCalledWith(
            expect.objectContaining({
                data: mockData,
                options: mockOptions,
            }),
            {}
        );
    });

    it('should render the chart with a title', () => {
        render(<BarChart data={mockData} options={mockOptions} />);
        
        const chartElement = screen.getByTestId('bar-chart');
        expect(chartElement).toBeInTheDocument();
    });
});
