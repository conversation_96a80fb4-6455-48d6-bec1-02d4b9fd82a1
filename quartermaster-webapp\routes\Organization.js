const express = require("express");
const hasPermission = require("../middlewares/hasPermission");
const { permissions } = require("../utils/permissions");
const { validateData } = require("../middlewares/validator");
const { body, param } = require("express-validator");
const { validateError } = require("../utils/functions");
const { default: mongoose } = require("mongoose");
const Organization = require("../models/Organization");
const { isValidObjectId } = require("mongoose");
const isAuthenticated = require("../middlewares/auth");
const { default: rateLimit } = require("express-rate-limit");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const User = require("../models/User");
const router = express.Router();

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get("/", assignEndpointId.bind(this, endpointIds.FETCH_ORGANIZATIONS), isAuthenticated, async (req, res) => {
    try {
        let hasManageOrgPermission = false;
        if (req.user && req.user.role && req.user.permissions) {
            hasManageOrgPermission = req.user.permissions.some((p) => p.permission_id === permissions.manageOrganizations);
        }
        const matchCondition = {};
        if (!hasManageOrgPermission && req.user.organization_id) {
            matchCondition._id = mongoose.Types.ObjectId(req.user.organization_id);
        }

        const organizations = await Organization.aggregate([
            { $match: matchCondition },
            {
                $lookup: {
                    from: "users",
                    let: { orgId: "$_id" },
                    pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$organization_id", "$$orgId"] }, { $ne: ["$is_deleted", true] }] } } }],
                    as: "users",
                },
            },
            {
                $lookup: {
                    from: "users",
                    let: { createdBy: "$created_by" },
                    pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$_id", "$$createdBy"] }, { $ne: ["$is_deleted", true] }] } } }],
                    as: "user",
                },
            },
            {
                $addFields: {
                    user: { $arrayElemAt: ["$user", 0] },
                    user_count: { $size: "$users" },
                },
            },
            {
                $project: {
                    name: 1,
                    domain: 1,
                    creation_timestamp: 1,
                    is_miscellaneous: 1,
                    "user.name": 1,
                    "user.username": 1,
                    user_count: 1,
                },
            },
        ]);

        res.json(organizations);
    } catch (err) {
        validateError(err, res);
    }
});

router.get(
    "/:id",
    assignEndpointId.bind(this, endpointIds.FETCH_ORGANIZATION_BY_ID),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            let hasManageOrgPermission = false;
            if (req.user && req.user.permissions) {
                hasManageOrgPermission = req.user.permissions.some((p) => p.permission_id === permissions.manageOrganizations);
            }

            if (!hasManageOrgPermission && req.user.organization_id && req.user.organization_id.toString() !== req.params.id) {
                return res.status(403).json({ message: "Forbidden: You can only access your own organization" });
            }

            const organization = await Organization.aggregate([
                {
                    $match: { _id: mongoose.Types.ObjectId(req.params.id) },
                },
                {
                    $lookup: {
                        from: "users",
                        let: { orgId: "$_id" },
                        pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$organization_id", "$$orgId"] }, { $ne: ["$is_deleted", true] }] } } }],
                        as: "users",
                    },
                },
                {
                    $lookup: {
                        from: "users",
                        let: { createdBy: "$created_by" },
                        pipeline: [{ $match: { $expr: { $and: [{ $eq: ["$_id", "$$createdBy"] }, { $ne: ["$is_deleted", true] }] } } }],
                        as: "user",
                    },
                },
                {
                    $addFields: {
                        user: { $arrayElemAt: ["$user", 0] },
                        user_count: { $size: "$users" },
                    },
                },
                {
                    $project: {
                        name: 1,
                        domain: 1,
                        creation_timestamp: 1,
                        is_miscellaneous: 1,
                        "user.name": 1,
                        "user.username": 1,
                        user_count: 1,
                    },
                },
            ]);

            if (!organization.length) return res.status(404).json({ message: "Organization not found" });
            res.json(organization[0]);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_ORGANIZATION),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageOrganizations]),
    validateData.bind(this, [
        body("name")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("domain")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const { name, domain } = req.body;

            const existingOrganization = await Organization.findOne({ domain });
            if (existingOrganization) {
                return res.status(400).json({ message: "An organization with this domain already exists" });
            }

            const organization = await Organization.create({
                name,
                domain,
                created_by: req.user._id,
            });

            res.status(201).json({ message: "Organization created successfully", organization });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_ORGANIZATION),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageOrganizations]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
        body("name").isString().optional(),
        body("domain").isString().optional(),
    ]),
    async (req, res) => {
        try {
            const updates = req.body;
            const organization = await Organization.findByIdAndUpdate(req.params.id, updates, { new: true });
            if (!organization) return res.status(404).json({ message: "Organization not found" });
            res.json({ message: "Organization updated successfully", organization });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:id",
    assignEndpointId.bind(this, endpointIds.DELETE_ORGANIZATION),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageOrganizations]),
    validateData.bind(this, [
        param("id")
            .custom(isValidObjectId)
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        try {
            const organizationId = req.params.id;

            const userCount = await User.countDocuments({ organization_id: organizationId });
            if (userCount > 0) {
                return res.status(400).json({ message: "Cannot delete organization as it is assigned to one or more users." });
            }

            const organization = await Organization.findByIdAndDelete(organizationId);
            if (!organization) return res.status(404).json({ message: "Organization not found" });

            res.json({ message: "Organization deleted successfully" });
        } catch (err) {
            validateError(err, res);
        }
    },
);

module.exports = router;
