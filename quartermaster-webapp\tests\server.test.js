const request = require('supertest'); // For testing HTTP requests
const jwt = require('jsonwebtoken');
const SessionLog = require('../models/SessionLog'); // Mock this model
const ioEmitter = require('../modules/ioEmitter');
const ioc = require("socket.io-client");
const app = require('../server')
const process = require('process')

// Mock external modules
jest.mock('mongoose');
jest.mock('jsonwebtoken');
jest.mock('../models/SessionLog');
jest.mock('../modules/processLogs');

jest.mock('../modules/db', () => ({
    qm: {
        model: jest.fn().mockReturnValue({
            find: jest.fn(),
            aggregate: jest.fn(),
            create: jest.fn(),
            findOne: jest.fn(),
            findById: jest.fn(),
            findOneAndDelete: jest.fn(),
        }),
        collection: jest.fn(),
    },
    qmai: {
        model: jest.fn(),
        collection: jest.fn(),
    },
}));


describe('Server and routes', () => {

    beforeEach(() => {
        jest.resetModules()
    })

    describe('Express API routes', () => {
        // let app;

        beforeAll(() => {
            jest.resetModules()
            // app = require('../server')
        })

        it('should return 404 for non-existent routes', async () => {
            const res = await request(app).get('/api/unknownRoute');
            expect(res.statusCode).toEqual(404);
            expect(res.text).toContain('Sorry, that route does not exist');
        });

        it('should return 200 for the base API route', async () => {
            const res = await request(app).get('/api');
            expect(res.statusCode).toEqual(200);
            expect(res.text).toContain('Welcome to Quartermaster API');
        });

        it('should serve static files (and return 404 if index.html does not exist)', async () => {
            const res = await request(app).get('/non-api-route');
            expect([200, 404]).toContain(res.statusCode)
        });
    });

    describe("Websocket connections", () => {
        let port, clientSocket;

        beforeAll((done) => {
            port = 5999;
            // app = require('../server')
            app.server.listen(port, done)
        });

        afterEach(() => {
            if (clientSocket)
                clientSocket.disconnect();
        });

        afterAll((done) => {
            app.server.close(done)
        })

        test("should not authenticate if no token is provided", (done) => {
            const clientSocket = ioc(`http://localhost:${port}`);
            clientSocket.on("connect_error", (err) => {
                expect(err.message).toBe('Authentication error: No token provided')
                done()
            });
        });

        test("should not authenticate if jwt token is invalid", (done) => {
            jwt.verify.mockReturnValue({})
            const clientSocket = ioc(`http://localhost:${port}`, { auth: { jwt_token: 'invalid-token' } });
            clientSocket.on("connect_error", (err) => {
                expect(err.message).toBe('Authentication error: Invalid token')
                done()
            });
        });

        test("should throw error during authentication", (done) => {
            jwt.verify.mockReturnValue(null)
            const clientSocket = ioc(`http://localhost:${port}`, { auth: { jwt_token: 'invalid-token' } });
            clientSocket.on("connect_error", (err) => {
                done()
            });
        });

        test("should authenticate if valid token is provided", (done) => {
            const createSpy = jest.spyOn(SessionLog, 'create').mockImplementation(() => Promise.resolve());
            jwt.verify.mockReturnValue({ user_id: '1234' })
            const clientSocket = ioc(`http://localhost:${port}`, { auth: { jwt_token: 'valid-token' } });
            clientSocket.on("connect", (err) => {
                clientSocket.disconnect()
            });
            clientSocket.on("disconnect", (err) => {
                const interval = setInterval(() => {
                    if (createSpy.mock.calls.length === 2) {
                        console.log('SessionLog.create has been called twice');

                        // Clear the interval and complete the test
                        clearInterval(interval);
                        expect(createSpy).toHaveBeenCalledTimes(2);
                        done();  // Call done after the second call to SessionLog.create
                    }
                }, 50);  // Check every 50ms
            });
        });

        test('should emit events via notifyAll', () => {
            const mockEvent = { name: 'testEvent', data: { key: 'value' } };
            const ioEmitSpy = jest.spyOn(app.io, 'emit').mockImplementation(() => jest.fn());

            ioEmitter.emit('notifyAll', mockEvent);
            expect(ioEmitSpy).toHaveBeenCalled();
        });
    });

    describe('Server startup', () => {
        beforeAll(() => {
            process.env.PORT = 5998;
            process.env.NODE_ENV = 'dev';
            jest.resetModules()
        })

        afterAll(() => {
            process.env.NODE_ENV = 'test'
        })
        it('should start the server in non-test environment', (done) => {
            let app = require('../server')
            setTimeout(() => {
                app.server.close(done)
            }, 50);
        })
    })

    describe('Error handling and process events', () => {
        test('should log uncaught exceptions', () => {
            const uncaughtError = new Error('Test uncaught error');
            process.emit('uncaughtException', uncaughtError);
        });
    });
});
