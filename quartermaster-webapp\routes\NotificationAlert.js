const { default: rateLimit } = require("express-rate-limit");
const express = require("express");
const router = express.Router();
const NotificationAlert = require("../models/NotificationAlert");
const User = require("../models/User");
const isAuthenticated = require("../middlewares/auth");
const { validateError, generateUnsubscribeToken, userHasPermissions, buildStaticMarkerSignature, getStaticMapOld } = require("../utils/functions");
const assignEndpointId = require("../middlewares/assignEndpointId");
const { endpointIds } = require("../utils/endpointIds");
const { validateData } = require("../middlewares/validator");
const { body, param, query } = require("express-validator");
const { permissions } = require("../utils/permissions");
const hasPermission = require("../middlewares/hasPermission");
const { NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT } = require("../utils/Email");
const { sendEmail } = require("../modules/email");
const jwt = require("jsonwebtoken");
const EmailDomains = require("../models/EmailDomains");
const { Readable } = require("node:stream");
const microservice_socket = require("../microservice_socket");
const { generateNumberedIconBuffer } = require("../utils/staticMap");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 15,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
});

router.use("/", apiLimiter);

router.get(
    "/",
    assignEndpointId.bind(this, endpointIds.FETCH_NOTIFICATION_ALERTS),
    isAuthenticated,
    hasPermission.bind(this, [permissions.manageNotifications]),
    async (req, res) => {
        const page = parseInt(req.query.page) || 1;
        const page_size = parseInt(req.query.page_size) || 10;

        try {
            const totalDocuments = await NotificationAlert.countDocuments({ created_by: req.user._id });
            const totalPages = Math.ceil(totalDocuments / page_size);

            const notificationAlerts = await NotificationAlert.find({ created_by: req.user._id })
                .skip((page - 1) * page_size)
                .limit(page_size);

            res.json({
                data: notificationAlerts,
                total_pages: totalPages,
                total_documents: totalDocuments,
                current_page: page,
                next_page: page < totalPages ? page + 1 : null,
                previous_page: page > 1 ? page - 1 : null,
            });
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.post(
    "/",
    assignEndpointId.bind(this, endpointIds.CREATE_NOTIFICATION_ALERTS),
    isAuthenticated,
    validateData.bind(this, [
        body("super_category")
            .isArray()
            .notEmpty()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((suer_category) => {
                if (suer_category.length === 0) {
                    throw new Error(`At least one super category is required`);
                }
                return true;
            }),
        body("sub_category")
            .isArray()
            .notEmpty()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((sub_category) => {
                if (sub_category.length === 0) {
                    throw new Error(`At least one sub category is required`);
                }
                return true;
            }),
        body("country_flags")
            .isArray()
            .notEmpty()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
        body("type")
            .isString()
            .isIn(["email", "app", "both"])
            .notEmpty()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
        body("title")
            .isArray()
            .notEmpty()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((tit) => {
                if (tit.length === 0) {
                    throw new Error(`At least one Title is required`);
                }
                return true;
            }),
        body("unit_id")
            .isArray()
            .notEmpty()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((unit) => {
                if (unit.length === 0) {
                    throw new Error(`At least one Unit ID is required`);
                }
                return true;
            }),
        body("receivers")
            .isArray()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((receivers) => {
                if (receivers.length === 0) {
                    throw new Error(`At least one receiver is required`);
                }
                for (let email of receivers) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error(`Invalid email address: ${email}`);
                    }
                }
                return true;
            }),
        body("is_enabled")
            .optional()
            .custom((value) => {
                if (value === 1 || value === 0) {
                    return true;
                } else {
                    throw new Error(`Invalid value ${value} provided for is_enabled`);
                }
            }),
    ]),
    hasPermission.bind(this, [permissions.manageNotifications]),
    async (req, res) => {
        try {
            const { super_category, sub_category, country_flags, type, title, unit_id, receivers, is_enabled } = req.body;

            if (["email", "both"].includes(type)) {
                if (!req.user.email) {
                    return res.status(403).json({ message: "No email is associated with your account" });
                }
            }

            /** Ensure user cannot subscribe to units not assigned to them */
            if (!userHasPermissions(req.user, [permissions.accessAllUnits])) {
                if (unit_id.filter((id) => id !== "all").some((id) => !req.user.allowed_units.find((u) => u.unit_id === id))) {
                    return res.status(403).json({ message: "Not allowed to subscribe units not assigned to you" });
                }
            }

            if (receivers) {
                const getDomains = await EmailDomains.find({});
                const domains = getDomains.map((domain) => domain.domain);
                if (!req.user.email) {
                    return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
                }
                const userEmailDomain = req.user.email.split("@")[1];
                if (!domains.includes(userEmailDomain)) {
                    return res.status(400).json({ message: "User email domain is not allowed." });
                }
                const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions.additionalEmailAddressesPrivilege);
                const allReceiversValid = receivers.every((email) => {
                    const receiverDomain = email.split("@")[1];
                    return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
                });
                if (!allReceiversValid) {
                    return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
                }
            }

            const notificationAlert = await NotificationAlert.create({
                super_category,
                sub_category,
                country_flags,
                type,
                title,
                unit_id,
                receivers,
                is_enabled: is_enabled !== 0,
                created_by: req.user._id,
            });

            if (receivers) {
                const { email } = req.user;
                if (receivers.length > 0 && (type === "email" || type === "both")) {
                    for (let newReceiver of receivers) {
                        const token = generateUnsubscribeToken(newReceiver, notificationAlert._id);
                        const payload = {
                            addBy: email,
                            vessel: notificationAlert.title.length > 0 ? notificationAlert.title.join(",") : "All",
                            sub: notificationAlert.sub_category.length > 0 ? notificationAlert.sub_category.join(",") : "All",
                            super: notificationAlert.super_category.length > 0 ? notificationAlert.super_category.join(",") : "All",
                            flag: notificationAlert.country_flags.length > 0 ? notificationAlert.country_flags.join(",") : "All",
                            preference:
                                notificationAlert.type === "email"
                                    ? "email"
                                    : notificationAlert.type === "app"
                                      ? "app"
                                      : notificationAlert.type === "both"
                                        ? "email & app"
                                        : "not given",
                            link: `${process.env.API_URL}/notificationsAlerts/unsubscribe/email?token=${token}`,
                        };
                        const emailBody = NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT(
                            payload,
                            new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear(),
                        );
                        sendEmail({
                            to: receivers,
                            subject: "Detection Notification",
                            html: emailBody,
                        });
                    }
                }
            }

            res.json(notificationAlert);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id",
    assignEndpointId.bind(this, endpointIds.UPDATE_NOTIFICATION_ALERTS),
    isAuthenticated,
    validateData.bind(this, [
        body("super_category")
            .isArray()
            .notEmpty()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((suer_category) => {
                if (suer_category.length === 0) {
                    throw new Error(`At least one super category is required`);
                }
                return true;
            }),
        body("sub_category")
            .isArray()
            .notEmpty()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((sub_category) => {
                if (sub_category.length === 0) {
                    throw new Error(`At least one sub category is required`);
                }
                return true;
            }),
        body("country_flags")
            .isArray()
            .notEmpty()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
        body("type")
            .isString()
            .isIn(["email", "app", "both"])
            .notEmpty()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
        body("title")
            .isArray()
            .notEmpty()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((tit) => {
                if (tit.length === 0) {
                    throw new Error(`At least one Title is required`);
                }
                return true;
            }),
        body("unit_id")
            .isArray()
            .notEmpty()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((unit) => {
                if (unit.length === 0) {
                    throw new Error(`At least one Unit ID is required`);
                }
                return true;
            }),
        body("receivers")
            .isArray()
            .optional()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            })
            .custom((receivers) => {
                // if (receivers.length === 0) {
                //     throw new Error(`At least one receiver is required`);
                // }
                for (let email of receivers) {
                    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
                        throw new Error(`Invalid email address: ${email}`);
                    }
                }
                return true;
            }),
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    hasPermission.bind(this, [permissions.manageNotifications]),
    async (req, res) => {
        const { super_category, sub_category, country_flags, type, title, unit_id, receivers } = req.body;

        if (["email", "both"].includes(type)) {
            if (!req.user.email) {
                return res.status(403).json({ message: "No email is associated with your account" });
            }
        }

        /** Ensure user cannot subscribe to units not assigned to them */
        if (unit_id) {
            if (!userHasPermissions(req.user, [permissions.accessAllUnits])) {
                if (unit_id.filter((id) => id !== "all").some((id) => !req.user.allowed_units.find((u) => u.unit_id === id))) {
                    return res.status(403).json({ message: "Not allowed to subscribe units not assigned to you" });
                }
            }
        }

        if (receivers) {
            const getDomains = await EmailDomains.find({});
            const domains = getDomains.map((domain) => domain.domain);
            if (!req.user.email) {
                return res.status(400).json({ message: "You cannot add an email because no email is associated with your account" });
            }
            const userEmailDomain = req.user.email.split("@")[1];
            if (!domains.includes(userEmailDomain)) {
                return res.status(400).json({ message: "User email domain is not allowed." });
            }
            const hasPermission = req.user.permissions.some((p) => p.permission_id === permissions.additionalEmailAddressesPrivilege);
            const allReceiversValid = receivers.every((email) => {
                const receiverDomain = email.split("@")[1];
                return hasPermission ? domains.includes(receiverDomain) : receiverDomain === userEmailDomain;
            });
            if (!allReceiversValid) {
                return res.status(400).json({ message: "One or more receiver email domains are not allowed." });
            }
            const notificationAlert = await NotificationAlert.findById(req.params.id);
            if (!notificationAlert) {
                return res.status(404).json({ message: "Notification Alert not found" });
            }
            //check if new addition is made in payload separate that out please for me
            const newReceivers = receivers.filter((receiver) => !notificationAlert.receivers.includes(receiver));
            const { email } = req.user;
            if (
                newReceivers.length > 0 &&
                (notificationAlert.type === "email" || notificationAlert.type === "both") &&
                notificationAlert.is_enabled
            ) {
                //generate new token for each new receiver
                for (let newReceiver of newReceivers) {
                    const token = generateUnsubscribeToken(newReceiver, notificationAlert._id);
                    const payload = {
                        addBy: email,
                        vessel: notificationAlert.title.length > 0 ? notificationAlert.title.join(",") : "All",
                        sub: notificationAlert.sub_category.length > 0 ? notificationAlert.sub_category.join(",") : "All",
                        super: notificationAlert.super_category.length > 0 ? notificationAlert.super_category.join(",") : "All",
                        flag: notificationAlert.country_flags.length > 0 ? notificationAlert.country_flags.join(",") : "All",
                        preference:
                            notificationAlert.type === "email"
                                ? "email"
                                : notificationAlert.type === "app"
                                  ? "app"
                                  : notificationAlert.type === "both"
                                    ? "email & app"
                                    : "not given",
                        link: `${process.env.API_URL}/notificationsAlerts/unsubscribe/email?token=${token}`,
                    };

                    const emailBody = NOTIFICATION_SUBSCRIPTION_EMAIL_CONTENT(
                        payload,
                        new Date().getDate() + "-" + new Date().getMonth() + "-" + new Date().getFullYear(),
                    );
                    sendEmail({
                        to: newReceivers,
                        subject: "Detection Notification",
                        html: emailBody,
                    });
                }
            }
        }
        try {
            const notificationAlert = await NotificationAlert.findByIdAndUpdate(
                req.params.id,
                {
                    super_category,
                    sub_category,
                    country_flags,
                    type,
                    title,
                    unit_id,
                    receivers,
                    updated_at: new Date().toISOString(),
                },
                { new: true },
            );
            res.json(notificationAlert);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id/enable",
    assignEndpointId.bind(this, endpointIds.UPDATE_NOTIFICATION_ALERTS),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    hasPermission.bind(this, [permissions.manageNotifications]),
    async (req, res) => {
        try {
            const notificationAlert = await NotificationAlert.findByIdAndUpdate(
                req.params.id,
                { is_enabled: true, updated_at: new Date().toISOString() },
                { new: true },
            );
            res.json(notificationAlert);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.patch(
    "/:id/disable",
    assignEndpointId.bind(this, endpointIds.UPDATE_NOTIFICATION_ALERTS),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    hasPermission.bind(this, [permissions.manageNotifications]),
    async (req, res) => {
        try {
            const notificationAlert = await NotificationAlert.findByIdAndUpdate(
                req.params.id,
                { is_enabled: false, updated_at: new Date().toISOString() },
                { new: true },
            );
            res.json(notificationAlert);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.delete(
    "/:id",
    assignEndpointId.bind(this, endpointIds.DELETE_NOTIFICATION_ALERTS),
    isAuthenticated,
    validateData.bind(this, [
        param("id")
            .isMongoId()
            .withMessage((value, { path }) => {
                return `Invalid value ${value} provided for ${path}`;
            }),
    ]),
    hasPermission.bind(this, [permissions.manageNotifications]),
    async (req, res) => {
        try {
            const notificationAlert = await NotificationAlert.findByIdAndDelete(req.params.id);
            res.json(notificationAlert);
        } catch (err) {
            validateError(err, res);
        }
    },
);

router.get(
    "/unsubscribe/email",
    assignEndpointId.bind(this, endpointIds.UNSUBSCRIBE_NOTIFICATION_ALERTS),
    validateData.bind(this, [
        query("token")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        // decrypt jwt_token to get email and notification_summary id in it
        const { token } = req.query;
        const { notificationId, email } = jwt.verify(token, process.env.JWT_SECRET);

        try {
            const notificationAlert = await NotificationAlert.findById(notificationId);
            if (!notificationAlert) {
                return res.redirect(
                    `${process.env.APP_URL}/subscription?status=404&title=Something Went Wrong&message=Notification Alert does not exist`,
                );
            }

            if (!notificationAlert.receivers.includes(email)) {
                const user = await User.findOne({ email });

                if (user && user._id.toString() === notificationAlert.created_by.toString()) {
                    await NotificationAlert.updateOne({ _id: notificationId }, { $set: { is_enabled: false, updated_at: new Date().toISOString() } });

                    return res.redirect(
                        `${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification alert emails `,
                    );
                }

                return res.redirect(
                    `${process.env.APP_URL}/subscription?status=401&title=Email Removed&message=This email does not exist or already removed from notification alert`,
                );
            }

            const updateRes = await NotificationAlert.updateOne({ _id: notificationId }, { $pull: { receivers: email } });

            if (!updateRes.modifiedCount) {
                return res.redirect(
                    `${process.env.APP_URL}/subscription?status=404&title=Email Removed&message=This email does not exist or already removed from notification alert`,
                );
            }

            return res.redirect(
                `${process.env.APP_URL}/subscription?status=200&title=Successfully Unsubscribed&message= You will no longer receive notification alert emails `,
            );
        } catch (err) {
            res.status(500).json({ message: err.message });
        }
    },
);

router.get(
    "/map.:ext?",
    assignEndpointId.bind(this, endpointIds.FETCH_MAP_FOR_ALERT),
    validateData.bind(this, [
        query("token")
            .isString()
            .notEmpty()
            .withMessage((value, { path }) => `Invalid value '${value}' provided for field '${path}'`),
    ]),
    async (req, res) => {
        // decrypt jwt_token to get locations
        const { token } = req.query;
        const { locations } = jwt.verify(token, process.env.JWT_SECRET);

        try {
            if (!locations) {
                return res.status(500).send({ message: "Error processing request" });
            }

            const markers = locations.map((location) => {
                return buildStaticMarkerSignature(location[0], location[1]);
            });

            const { ext } = req.params;

            const mapData = await getStaticMapOld(markers, null, ext);

            if (mapData) {
                res.setHeader("Content-Type", mapData.mimeType);
                res.setHeader("Content-Length", mapData.source.headers.get("content-length"));
                res.setHeader("access-control-allow-origin", "*");
                res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                res.setHeader("Expires", "0");
                res.setHeader("Pragma", "no-cache");

                if (mapData.source) {
                    const nodeStream = Readable.fromWeb(mapData.source.body);
                    nodeStream.pipe(res);
                } else {
                    return res.status(500).send({ message: "Error serving image" });
                }
            } else {
                res.status(500).send({ message: "Error serving image" });
            }
        } catch (error) {
            console.error("Error serving image:", error);
            res.status(500).send({ message: "Error serving image" });
        }
    },
);

router.get(
    "/cluster/:amount",
    assignEndpointId.bind(this, endpointIds.FETCH_MAP_CLUSTER),
    validateData.bind(this, [
        param("amount")
            .isNumeric()
            .withMessage((value) => {
                return `Invalid value ${value} provided for amount`;
            }),
    ]),
    async (req, res) => {
        try {
            const { bgColor, textColor } = req.query;
            const { amount } = req.params;

            const buffer = await generateNumberedIconBuffer(amount, bgColor, textColor);

            if (buffer) {
                res.setHeader("Content-Type", "image/png");
                res.setHeader("Content-Length", buffer.byteLength);
                res.setHeader("access-control-allow-origin", "*");
                res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
                res.setHeader("Expires", "0");
                res.setHeader("Pragma", "no-cache");

                const nodeStream = Readable.from(buffer);
                nodeStream.pipe(res);
            } else {
                res.status(500).send({ message: "Error serving image" });
            }
        } catch (error) {
            console.error("Error serving image:", error);
            res.status(500).send({ message: "Error serving image" });
        }
    },
);

router.get(
    "/testing",
    assignEndpointId.bind(this, endpointIds.FETCH_NOTIFICATION_ALERTS_BY_USER),
    isAuthenticated,
    hasPermission.bind(this, [permissions.testNotificationAlerts]),
    async (req, res) => {
        try {
            microservice_socket.emit("testing/triggerNotifications", JSON.stringify({ user_id: req.user._id }));
            res.status(200).json({ message: "Notification Alers have been emitted" });
        } catch (err) {
            validateError(err, res);
        }
    },
);
module.exports = router;
