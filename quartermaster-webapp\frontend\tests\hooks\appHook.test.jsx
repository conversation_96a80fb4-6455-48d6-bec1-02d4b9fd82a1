import { useApp } from '../../src/hooks/AppHook';
import { useContext } from 'react';

jest.mock('../../src/contexts/AppContext', () => ({
    AppContext: 'AppContext',
}));
jest.mock('react', () => ({
    useContext: jest.fn(),
}));

describe('useApp Hook', () => {
    it('should return app context', () => {
        const mockContextValue = {
            region: 'ap-southeast-1'
        };
        
        useContext.mockReturnValue(mockContextValue);
        
        const result = useApp();
        
        expect(result).toBe(mockContextValue);
    });

    it('should throw error when used outside of AppProvider', () => {
        useContext.mockReturnValue(undefined);
        
        expect(() => useApp()).toThrowError('useApp must be used within AppProvider');
    });
});
