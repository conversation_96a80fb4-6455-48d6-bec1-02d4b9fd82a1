import { useEffect, useMemo, useState } from "react";
import MultiSelect from "../../../components/MultiSelect";
import axiosInstance from "../../../axios";
import { CircularProgress } from "@mui/material";
import { isDevUnit } from "../../../utils";

export default function UpdateUserUnits({ user, vessels, regionGroups, disabled }) {
    const [checkedUnits, setCheckedUnits] = useState([]);
    const [updating, setUpdating] = useState(false);

    const vesselsByRegionGroup = useMemo(() => {
        return vessels
            .filter((v) => v.region_group && !isDevUnit(v.name))
            .map((v) => ({ ...v, region_group_object: regionGroups.find((rg) => rg._id === v.region_group) }));
    }, [vessels, regionGroups]);

    useEffect(() => {
        initializeCheckedUnits();
    }, [user, vessels]);

    const initializeCheckedUnits = () => setCheckedUnits(user.allowed_units.map((v) => v.unit_id));

    const isChanged = useMemo(() => {
        // If lengths are different, there was definitely a change
        if (checkedUnits.length !== user.allowed_units.length) return true;

        // Convert allowed_units to a Set of unit_ids for faster lookup
        const allowedUnitIds = new Set(user.allowed_units.map((v) => v.unit_id));

        // Check if any checked unit is not in allowed_units
        return checkedUnits.some((unitId) => !allowedUnitIds.has(unitId));
    }, [checkedUnits, user]);

    const handleUpdate = () => {
        setUpdating(true);
        const allowed_units = checkedUnits
            .filter((v) => vessels.find((o) => o.unit_id === v))
            .map((v) => ({ unit_id: v, region: vessels.find((o) => o.unit_id === v).region }));
        axiosInstance
            .patch(`/users/${user._id}/allowedUnits`, { allowed_units }, { meta: { showSnackbar: true } })
            .catch((err) => {
                initializeCheckedUnits();
                console.error(err);
            })
            .finally(() => {
                setUpdating(false);
            });
    };

    return updating ? (
        <CircularProgress />
    ) : (
        <MultiSelect
            loading={vessels.length === 0}
            options={vesselsByRegionGroup}
            value={checkedUnits}
            disabled={disabled || updating}
            multiple
            disableCloseOnSelect
            groupBy={(o) => o.region_group_object?.name}
            label={`${user.allowed_units.length} selected`}
            getOptionLabel={(o) => o.name || o.unit_id}
            isOptionEqualToValue={(o, v) => v.includes(o.unit_id)}
            renderTags={() => null}
            onChange={(e, v) => {
                console.log("changed value", v);
                setCheckedUnits(v.map((o) => (typeof o === "string" ? o : o.unit_id)));
            }}
            onClose={() => {
                if (isChanged) {
                    handleUpdate();
                }
            }}
        />
    );
}
