import { Chip, CircularProgress, Grid, Typography, FormControl, Select, MenuItem, Pagination, alpha } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import axiosInstance from "../../../axios";
import { DataGrid } from "@mui/x-data-grid";
import dayjs from "dayjs";
import { permissions, userValues } from "../../../utils";
import * as utils from "../../../utils";
import { ArrowDropDown, SentimentVeryDissatisfied } from "@mui/icons-material";
import { useUser } from "../../../hooks/UserHook";
import { getSocket } from "../../../socket";
import { useApp } from "../../../hooks/AppHook";
import UpdateRoleMenu from "./UpdateRoleMenu";
import InviteUserModal from "./InviteUserModal";
import DeleteUserModal from "./DeleteUserModal";
import FilterUserModal from "./FilterUserModal";
import { useToaster } from "../../../hooks/ToasterHook";
import theme from "../../../theme";
import UpdateUserUnits from "./UpdateUserUnits";
import useVesselInfo from "../../../hooks/VesselInfoHook";
import useGroupRegions from "../../../hooks/GroupRegionHook";
import UpdateOrganizationMenu from "./Organizations/UpdateOrganizationMenu";
import DeleteButton from "../../../components/DeleteButton";

export default function Users({ showAddUser, setShowAddUser, showFilterModal, setShowFilterModal, searchQuery }) {
    const { user } = useUser();
    const { isMobile, timezone } = useApp();
    const toaster = useToaster();
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const { regions, fetchRegions } = useGroupRegions();

    const [filters, setFilters] = useState({
        roles: null,
        organizations: null,
        vessels: null,
        hasEmail: null,
        full_name_or_email: null,
        created_after: null,
    });

    const [users, setUsers] = useState([]);
    const [roles, setRoles] = useState([]);
    const [organizations, setOrganizations] = useState([]);
    const [vessels, setVessels] = useState([]);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedUser, setSelectedUser] = useState();
    const [updateRoleAnchorEl, setUpdateRoleAnchorEl] = useState(null);
    const [updatingRole, setUpdatingRole] = useState();

    const [updateOrganizationAnchorEl, setUpdateOrganizationAnchorEl] = useState(null);
    const [selectedOrganizationUser, setSelectedOrganizationUser] = useState();
    const [updatingOrganization, setUpdatingOrganization] = useState(false);

    const [regionGroups, setRegionGroups] = useState([]);

    const [deleteUser, setDeleteUser] = useState();
    const [deleting, setDeleting] = useState();

    const [page, setPage] = useState(1);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [totalCount, setTotalCount] = useState(0);

    const fetchUsers = async (dropPagination) => {
        setIsLoading(true);
        if (dropPagination) setPage(1);

        try {
            const queryParams = new URLSearchParams({
                ...Object.fromEntries(Object.entries({ ...filters, ...searchQuery }).filter(([, value]) => value !== null && value !== "")),
                page: dropPagination ? 1 : page,
                rowsPerPage,
            });
            const { data } = await axiosInstance.get(`/v2/users?${queryParams.toString()}`);

            const { users, totalCount } = data;
            if (Array.isArray(users) && users.length > 0) {
                setUsers(users);
                setTotalCount(totalCount);
            } else {
                setUsers([]);
                setTotalCount(0);
                toaster("No data found for users", { variant: "warning" });
            }
        } catch (err) {
            setUsers([]);
            toaster("Something went wrong", { variant: "error" });
            console.error("An error occurred while fetching users on the Users Page:", err);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchVessels();
    }, [vesselInfo]);

    useEffect(() => {
        fetchRegionGroups();
    }, [regions]);
    useEffect(() => {
        fetchUsers();
        fetchRoles();
        fetchOrganizations();
        // fetchRegionGroups();

        const socket = getSocket();

        socket.on("users/changed", fetchUsers);
        socket.on("roles/changed", fetchRoles);

        return () => {
            socket.off("users/changed", fetchUsers);
            socket.off("roles/changed", fetchRoles);
        };
    }, []);

    useEffect(() => {
        if (Array.isArray(users) && users.length > 0) {
            setIsLoading(false);
        }
    }, [users]);

    useEffect(() => {
        const maxPage = Math.ceil(totalCount / rowsPerPage);
        if (page > maxPage && maxPage > 0) {
            setPage(1);
        }
    }, [page, rowsPerPage, totalCount, users]);

    useEffect(() => {
        fetchUsers(true);
    }, [rowsPerPage, filters, searchQuery]);

    useEffect(() => {
        fetchUsers(false);
    }, [page]);

    const fetchRoles = async () => {
        try {
            const { data } = await axiosInstance.get("/roles");
            if (Array.isArray(data) && data.length > 0) {
                setRoles(data);
            } else {
                setRoles([]);
                toaster("No data found for roles", { variant: "warning" });
            }
        } catch (err) {
            setRoles([]);
            toaster("Something went wrong", { variant: "error" });
            console.error("An error occurred while fetching roles on the Users Page:", err);
        }
    };

    const fetchOrganizations = async () => {
        try {
            const { data } = await axiosInstance.get("/organizations");
            if (Array.isArray(data) && data.length > 0) {
                setOrganizations(data);
            } else {
                setOrganizations([]);
                toaster("No data found for organizations", { variant: "warning" });
            }
        } catch (err) {
            setOrganizations([]);
            toaster("Something went wrong", { variant: "error" });
            console.error("An error occurred while fetching organizations on the Users Page:", err);
        }
    };

    const fetchVessels = async () => {
        try {
            if (vesselInfo) {
                setVessels(vesselInfo);
            } else {
                fetchVesselsInfo();
            }
        } catch (err) {
            console.error("An error occurred while fetching vessels on the Users Page:", err);
        }
    };

    const fetchRegionGroups = async () => {
        if (regions) {
            setRegionGroups(regions);
        } else {
            fetchRegions();
        }
    };

    const handleRoleUpdateClick = (e, user) => {
        setUpdateRoleAnchorEl(e.currentTarget);
        setSelectedUser(user);
    };

    const handleOrganizationUpdateClick = (e, user) => {
        setUpdateOrganizationAnchorEl(e.currentTarget);
        setSelectedOrganizationUser(user);
    };

    // Custom Footer Component
    const CustomFooter = ({ page, rowsPerPage, totalRows, onPageChange, onRowsPerPageChange }) => {
        const startIndex = (page - 1) * rowsPerPage + 1;
        const endIndex = Math.min(page * rowsPerPage, totalRows);

        return (
            <Grid
                container
                justifyContent={{ sm: "space-between", xs: "center" }}
                alignItems={"center"}
                padding={"10px"}
                backgroundColor={alpha(theme.palette.custom.offline, 0.08)}
                gap={2}
                sx={{
                    borderRadius: "5px",
                }}
            >
                <Grid padding={"10px 20px"} size="auto">
                    <Typography fontSize={{ xs: "12px", lg: "14px" }} fontWeight={600}>
                        {`${endIndex == 0 ? 0 : startIndex} - ${endIndex} of ${totalRows}`}
                    </Typography>
                </Grid>
                <Grid size="auto">
                    <Pagination
                        count={Math.ceil(totalRows / rowsPerPage)}
                        page={page}
                        onChange={onPageChange}
                        shape="rounded"
                        siblingCount={isMobile ? 0 : 1}
                        boundaryCount={1}
                        sx={{
                            "& .MuiButtonBase-root, .MuiPaginationItem-root": {
                                color: "#FFFFFF",
                                minHeight: "30px",
                                fontSize: isMobile ? "9px" : "14px",
                                borderRadius: "8px",
                                minWidth: "32px",
                                display: "flex",
                                justifyContent: "center",
                                alignItems: "center",
                                backgroundColor: alpha(theme.palette.custom.offline, 0.2),
                            },
                            "& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected": {
                                color: "#FFFFFF",
                                backgroundColor: theme.palette.custom.mainBlue,
                            },
                        }}
                    />
                </Grid>
                <Grid justifyContent="flex-end" display={"flex"} size="auto">
                    <FormControl variant="outlined">
                        <Select
                            value={rowsPerPage}
                            onChange={onRowsPerPageChange}
                            sx={{
                                "& .MuiOutlinedInput-notchedOutline": {
                                    border: "none",
                                },
                                "& .MuiSelect-select": {
                                    padding: "10px",
                                    fontSize: isMobile ? "12px" : "16px",
                                    backgroundColor: theme.palette.custom.mainBlue,
                                    borderRadius: "5px",
                                    color: "#FFFFFF",
                                    minWidth: isMobile ? 0 : "80px",
                                },
                            }}
                        >
                            {[5, 10, 20].map((size) => (
                                <MenuItem key={size} value={size}>
                                    {isMobile ? size : `${size} / Page`}
                                </MenuItem>
                            ))}
                        </Select>
                    </FormControl>
                </Grid>
            </Grid>
        );
    };

    const handlePageChange = (event, newPage) => {
        setPage(newPage);
    };

    const handlePageSizeChange = (event) => {
        setRowsPerPage(event.target.value);
        setPage(1);
    };

    const checkRoleUpdation = (newUser) => {
        if (user._id === newUser._id || user.role_id === newUser.role_id) return true;
        const newUserRole = roles.find((role) => role.role_id === newUser.role_id);
        if (user.role?.hierarchy_number >= newUserRole?.hierarchy_number) return true;
        return false;
    };

    const checkOrganizationUpdation = (newUser) => {
        if (!newUser) return true;
        if (user._id === newUser._id) return true;
        const newUserRole = roles.find((role) => role.role_id === newUser.role_id);
        if (!newUserRole) return true;
        if (user.role?.hierarchy_number >= newUserRole?.hierarchy_number) return true;
        if (!user.hasPermissions([permissions.manageUsers, permissions.manageOrganizations])) return true;
        return false;
    };

    const checkUnitUpdation = (newUser) => {
        if (user._id === newUser._id || user.role_id === newUser.role_id) return true;
        const newUserRole = roles.find((role) => role.role_id === newUser.role_id);
        if (user.role?.hierarchy_number >= newUserRole?.hierarchy_number) return true;
        return false;
    };

    /**
     * @type {Array<import("@mui/x-data-grid").GridColDef>}
     */
    const columns = [
        { field: "name", headerName: "Name", minWidth: 250 },
        { field: "email", headerName: "Email", minWidth: 300 },
        {
            field: "role_id",
            headerName: "Role",
            minWidth: 200,
            renderCell: (params) => {
                return (
                    <Chip
                        disabled={checkRoleUpdation(params.row)}
                        onClick={(e) => handleRoleUpdateClick(e, params.row)}
                        sx={{
                            paddingLeft: 0.5,
                            paddingRight: 2,
                            display: "flex",
                            flexDirection: "row-reverse",
                            width: "fit-content",
                            minWidth: { xs: "100%", xl: "50%" },
                            borderRadius: "5px",
                            justifyContent: "space-between",
                        }}
                        icon={
                            updatingRole === params.row._id ? (
                                <CircularProgress size={18} />
                            ) : (
                                <ArrowDropDown fontSize="small" sx={{ cursor: "pointer", opacity: 0.5 }} />
                            )
                        }
                        label={roles.find((r) => r.role_id === params.row.role_id)?.role_name}
                    />
                );
            },
            valueGetter: (value) => roles.find((r) => r.role_id === value)?.role_name || "",
        },
        {
            field: "allowed_units",
            headerName: "SmartMasts",
            minWidth: 300,
            renderCell: ({ row }) => {
                const user = users.find((u) => u._id === row._id);
                if (!user) return null;
                const role = roles.find((r) => r.role_id === user.role_id);
                if (!role) return null;

                return (
                    <div style={{ display: !role.denied_permissions.includes(utils.permissions.accessAllUnits) ? "none" : "block", maxWidth: 200 }}>
                        <UpdateUserUnits disabled={checkUnitUpdation(user)} user={user} vessels={vessels} regionGroups={regionGroups} />
                    </div>
                );
            },
        },
        {
            field: "organization_id",
            headerName: "Organization",
            minWidth: 200,
            renderCell: (params) => {
                return (
                    <Chip
                        disabled={checkOrganizationUpdation(params.row)}
                        onClick={(e) => handleOrganizationUpdateClick(e, params.row)}
                        sx={{
                            paddingLeft: 0.5,
                            paddingRight: 2,
                            display: "flex",
                            flexDirection: "row-reverse",
                            width: "fit-content",
                            minWidth: { xs: "100%", xl: "50%" },
                            borderRadius: "5px",
                            justifyContent: "space-between",
                        }}
                        icon={
                            updatingOrganization === params.row.organization_id ? (
                                <CircularProgress size={18} />
                            ) : (
                                <ArrowDropDown fontSize="small" sx={{ cursor: "pointer", opacity: 0.5 }} />
                            )
                        }
                        label={organizations.find((r) => r._id === params.row.organization_id)?.name}
                    />
                );
            },
            valueGetter: (value) => organizations.find((r) => r._id === value)?.name || "",
        },
        {
            field: "creation_timestamp",
            headerName: "Created",
            minWidth: 150,
            valueGetter: (v) =>
                dayjs(v)
                    .tz(timezone)
                    .format(userValues.dateTimeFormat(user, { exclude_seconds: true, exclude_hours: true, exclude_minutes: true })),
        },
        {
            field: "actions",
            headerName: "Actions",
            minWidth: 400,
            renderCell: (params) => (
                <Grid container>
                    <Grid>
                        {deleting === params.row._id ? (
                            <CircularProgress size={18} />
                        ) : (
                            <DeleteButton onClick={() => setDeleteUser(params.row)} disabled={checkRoleUpdation(params.row)} />
                        )}
                    </Grid>
                </Grid>
            ),
        },
    ];

    const columnsWithouFilters = useMemo(
        () => [
            ...columns.map((col) => ({
                ...col,
                filterable: false,
                sortable: true,
                resizable: false,
                disableColumnMenu: true,
                disableReorder: true,
                disableExport: true,
                flex: 1,
            })),
        ],
        [columns],
    );

    // useEffect(() => {
    //     console.log("filteredUsers",filteredUsers);
    // }, [filteredUsers])

    return (
        <Grid container color={"#FFFFFF"} flexDirection={"column"} height={"100%"}>
            <Grid overflow={"auto"} size="grow">
                <DataGrid
                    loading={isLoading}
                    disableRowSelectionOnClick
                    rows={users}
                    columns={columnsWithouFilters}
                    getRowId={(row) => row._id}
                    slots={{
                        footer: () => (
                            <CustomFooter
                                page={page}
                                rowsPerPage={rowsPerPage}
                                totalRows={totalCount}
                                onPageChange={handlePageChange}
                                onRowsPerPageChange={handlePageSizeChange}
                            />
                        ),
                        noRowsOverlay: () => (
                            <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"} height={"100%"}>
                                <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
                                <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                                    No data available
                                </Typography>
                            </Grid>
                        ),
                    }}
                />
            </Grid>
            {roles && selectedOrganizationUser && (
                <UpdateOrganizationMenu
                    updateOrganizationAnchorEl={updateOrganizationAnchorEl}
                    setUpdateOrganizationAnchorEl={setUpdateOrganizationAnchorEl}
                    organizations={organizations}
                    roles={roles}
                    selectedOrganizationUser={selectedOrganizationUser}
                    setUpdatingOrganization={setUpdatingOrganization}
                />
            )}
            {roles && selectedUser && (
                <UpdateRoleMenu
                    updateRoleAnchorEl={updateRoleAnchorEl}
                    setUpdateRoleAnchorEl={setUpdateRoleAnchorEl}
                    roles={roles}
                    selectedUser={selectedUser}
                    setUpdatingRole={setUpdatingRole}
                    organizations={organizations}
                />
            )}
            {roles && (
                <InviteUserModal
                    roles={roles}
                    organizations={organizations}
                    vessels={vessels}
                    showAddUser={showAddUser}
                    setShowAddUser={setShowAddUser}
                    regionGroups={regionGroups}
                />
            )}
            <DeleteUserModal deleteUser={deleteUser} setDeleteUser={setDeleteUser} setDeleting={setDeleting} />
            <FilterUserModal
                showFilterModal={showFilterModal}
                setShowFilterModal={setShowFilterModal}
                roles={roles}
                organizations={organizations}
                setFilters={setFilters}
            />
        </Grid>
    );
}
