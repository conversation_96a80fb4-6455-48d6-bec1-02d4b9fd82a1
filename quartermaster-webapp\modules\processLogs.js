// Store previous CPU and time data
let previousCpuUsage = process.cpuUsage();
let previousTime = Date.now();

const logCpuAndMemoryUsage = () => {
  // Get the current CPU usage and time
  const currentCpuUsage = process.cpuUsage(previousCpuUsage); // Difference from the previous call
  const currentTime = Date.now();

  // Calculate elapsed time in milliseconds
  const elapsedTime = currentTime - previousTime;

  // Calculate CPU usage in percentage
  const userCpuPercentage = ((currentCpuUsage.user / 1000) / elapsedTime) * 100;
  const systemCpuPercentage = ((currentCpuUsage.system / 1000) / elapsedTime) * 100;

  // Log CPU usage in percentage
  console.log(`CPU Usage: User: ${userCpuPercentage.toFixed(2)}%, System: ${systemCpuPercentage.toFixed(2)}%`);

  // Update previous values for the next interval calculation
  previousCpuUsage = process.cpuUsage();
  previousTime = Date.now();

  // Log memory usage
  const memoryUsage = process.memoryUsage();
  console.log(`Memory Usage: RSS: ${(memoryUsage.rss / 1024 / 1024).toFixed(2)} MB, Heap Total: ${(memoryUsage.heapTotal / 1024 / 1024).toFixed(2)} MB, Heap Used: ${(memoryUsage.heapUsed / 1024 / 1024).toFixed(2)} MB, External: ${(memoryUsage.external / 1024 / 1024).toFixed(2)} MB`);
};

// Set interval to log system usage every 60 seconds
if (process.env.NODE_ENV !== 'test')
  setInterval(logCpuAndMemoryUsage, Number(process.env.PROCESS_LOG_INTERVAL) || 60000);

module.exports = {
  logCpuAndMemoryUsage: process.env.NODE_ENV === 'test' && logCpuAndMemoryUsage
}