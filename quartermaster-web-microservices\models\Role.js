const mongoose = require('mongoose');
const db = require('../modules/db');

const roleSchema = new mongoose.Schema({
    role_id: { type: Number, required: true },
    role_name: { type: String, required: true, unique: true },
    denied_permissions: { type: Array, required: true },
    deletable: { type: Boolean, required: true, default: true },
    editable: { type: Boolean, required: true, default: true },
    hierarchy_number: { type: Number, required: true },
    creation_timestamp: { type: Date, required: true, default: () => new Date().toISOString() },
});

const Role = db.qm.model('Role', roleSchema);

module.exports = Role
